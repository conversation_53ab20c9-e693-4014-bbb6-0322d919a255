#!/bin/bash

# Dashboard Switch Script
# Quickly switch between original and modern dashboard implementations

VIEWS_DIR="/Users/<USER>/Developer/ai_marketing_hub/app/views/dashboard"
CONTROLLER_FILE="/Users/<USER>/Developer/ai_marketing_hub/app/controllers/dashboard_controller.rb"

case "$1" in
  "modern")
    echo "Switching to modern dashboard..."
    if [ -f "$VIEWS_DIR/index_original.html.erb" ]; then
      # Backup current if it exists and is different
      if [ -f "$VIEWS_DIR/index.html.erb" ]; then
        cp "$VIEWS_DIR/index.html.erb" "$VIEWS_DIR/index_backup.html.erb"
      fi
      
      # Ensure modern layout is set in controller
      if ! grep -q "layout 'modern_dashboard'" "$CONTROLLER_FILE"; then
        sed -i '' '3i\
  layout '\''modern_dashboard'\''
' "$CONTROLLER_FILE"
      fi
      
      echo "✅ Modern dashboard is active"
      echo "   Features: Clean cards, working dropdowns, no navbar"
      echo "   Layout: modern_dashboard.html.erb (no navbar/sidebar)"
    else
      echo "❌ Original dashboard backup not found"
    fi
    ;;
  
  "original")
    echo "Switching to original dashboard..."
    if [ -f "$VIEWS_DIR/index_original.html.erb" ]; then
      # Backup current modern version
      cp "$VIEWS_DIR/index.html.erb" "$VIEWS_DIR/index_modern_backup.html.erb"
      
      # Restore original dashboard
      cp "$VIEWS_DIR/index_original.html.erb" "$VIEWS_DIR/index.html.erb"
      
      # Remove modern layout from controller
      sed -i '' '/layout '\''modern_dashboard'\''/d' "$CONTROLLER_FILE"
      
      echo "✅ Original dashboard restored"
      echo "   Note: You may need to restart the Rails server"
      echo "   Layout: application.html.erb (with navbar/sidebar)"
    else
      echo "❌ Original dashboard backup not found"
    fi
    ;;
  
  "status")
    echo "Dashboard Status:"
    echo "================"
    if [ -f "$VIEWS_DIR/index.html.erb" ]; then
      if grep -q "modern-dashboard" "$VIEWS_DIR/index.html.erb"; then
        echo "🚀 Currently using: MODERN DASHBOARD"
        echo "   Features: Clean cards, integrated sidebar, working dropdowns"
        
        if grep -q "layout 'modern_dashboard'" "$CONTROLLER_FILE"; then
          echo "   Layout: modern_dashboard.html.erb (no navbar) ✅"
        else
          echo "   Layout: application.html.erb (with navbar) ⚠️"
        fi
      else
        echo "📄 Currently using: ORIGINAL DASHBOARD"
        echo "   Features: Traditional layout with navbar and sidebar"
      fi
    else
      echo "❌ No dashboard found"
    fi
    
    echo ""
    echo "Available files:"
    ls -la "$VIEWS_DIR"/index*.html.erb 2>/dev/null || echo "No dashboard files found"
    
    echo ""
    echo "Controller layout setting:"
    grep -n "layout" "$CONTROLLER_FILE" || echo "No layout specified (uses application layout)"
    ;;
  
  "dropdowns")
    echo "Testing Dropdown Functionality"
    echo "============================="
    echo "Modern dashboard dropdown features:"
    echo "• Click outside to close"
    echo "• Escape key to close"
    echo "• Arrow key navigation"
    echo "• Smooth animations"
    echo "• Auto-close other dropdowns"
    echo ""
    echo "To test:"
    echo "1. Navigate to /dashboard"
    echo "2. Click any dropdown (time range, filters)"
    echo "3. Try keyboard navigation and outside clicks"
    echo ""
    echo "If dropdowns don't work, check:"
    echo "• Rails server is running"
    echo "• Stimulus controllers are loaded"
    echo "• No JavaScript errors in browser console"
    ;;
  
  *)
    echo "Dashboard Switch Utility"
    echo "======================="
    echo "Usage: $0 {modern|original|status|dropdowns}"
    echo ""
    echo "Commands:"
    echo "  modern     - Switch to modern clean dashboard (no navbar)"
    echo "  original   - Revert to original dashboard (with navbar)"
    echo "  status     - Check current dashboard status"
    echo "  dropdowns  - Check dropdown functionality"
    echo ""
    echo "Features:"
    echo ""
    echo "Modern Dashboard:"
    echo "  ✅ Clean card-based design"
    echo "  ✅ Integrated sidebar navigation"
    echo "  ✅ Working Stimulus dropdowns"
    echo "  ✅ No navbar (as requested)"
    echo "  ✅ Mobile responsive"
    echo "  ✅ Modern color scheme"
    echo ""
    echo "Original Dashboard:"
    echo "  📄 Traditional Rails layout"
    echo "  📄 Separate navbar and sidebar"
    echo "  📄 Complex dashboard layout"
    echo ""
    echo "Files:"
    echo "  index.html.erb           - Active dashboard"
    echo "  index_original.html.erb  - Backup of original"
    echo "  index_modern_backup.html.erb - Backup when switching to original"
    ;;
esac
