class Admin::AiProviderConfigurationsController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_admin!
  before_action :set_ai_provider_configuration, only: [ :show, :edit, :update, :destroy, :test_connection, :toggle_active ]

  def index
    @ai_provider_configurations = current_tenant.ai_provider_configurations.includes(:tenant)
                                                .order(:provider_name, :ai_model_name)
    @providers_summary = current_tenant.ai_provider_configurations.group(:provider_name, :is_active).count
  end

  def show
    @usage_stats = calculate_usage_stats
    @recent_requests = get_recent_requests
  end

  def new
    @ai_provider_configuration = current_tenant.ai_provider_configurations.build
  end

  def create
    @ai_provider_configuration = current_tenant.ai_provider_configurations.build(ai_provider_configuration_params)

    if @ai_provider_configuration.save
      redirect_to admin_ai_provider_configuration_path(@ai_provider_configuration),
                  notice: "AI provider configuration was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @ai_provider_configuration.update(ai_provider_configuration_params)
      redirect_to admin_ai_provider_configuration_path(@ai_provider_configuration),
                  notice: "AI provider configuration was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    provider_name = @ai_provider_configuration.provider_name
    @ai_provider_configuration.destroy
    redirect_to admin_ai_provider_configurations_path,
                notice: "#{provider_name} configuration was successfully deleted."
  end

  def test_connection
    result = @ai_provider_configuration.test_connection

    if result[:success]
      flash[:notice] = "Connection successful! Response time: #{result[:response_time]}ms"
    else
      flash[:alert] = "Connection failed: #{result[:error]}"
    end

    redirect_to admin_ai_provider_configuration_path(@ai_provider_configuration)
  end

  def toggle_active
    @ai_provider_configuration.update!(is_active: !@ai_provider_configuration.is_active)
    status = @ai_provider_configuration.is_active? ? "activated" : "deactivated"

    redirect_to admin_ai_provider_configurations_path,
                notice: "#{@ai_provider_configuration.provider_name} configuration #{status}."
  end

  private

  def set_ai_provider_configuration
    @ai_provider_configuration = current_tenant.ai_provider_configurations.find(params[:id])
  end

  def ai_provider_configuration_params
    params.require(:ai_provider_configuration).permit(
      :provider_name, :ai_model_name, :api_key, :base_url, :max_tokens, :temperature,
      :cost_per_token, :priority, :is_active, :default_for_task_type, 
      task_types: [], capabilities: []
    )
  end

  def ensure_admin!
    redirect_to dashboard_path, alert: "Access denied." unless current_user.admin?
  end

  def calculate_usage_stats
    # This would be implemented with actual usage tracking
    {
      total_requests: rand(100..1000),
      successful_requests: rand(80..950),
      failed_requests: rand(0..50),
      average_response_time: rand(200..800),
      total_cost: rand(10..100).to_f
    }
  end

  def get_recent_requests
    # This would be implemented with actual request logs
    []
  end
end
