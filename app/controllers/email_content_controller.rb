# frozen_string_literal: true

# Email Content Management Controller
# Handles email campaign content creation, editing, and management
class EmailContentController < ApplicationController
  include AiProvidersHelper

  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_campaign
  before_action :set_email_campaign, only: [ :show, :edit, :update, :destroy, :optimize_content ]

  # GET /campaigns/:campaign_id/email_content
  def show
    @email_campaign = @campaign.email_campaign || @campaign.build_email_campaign(default_email_attributes)
    @preview_data = {
      subject_line: @email_campaign.subject_line,
      preview_text: @email_campaign.preview_text,
      content: @email_campaign.content,
      from_name: @email_campaign.from_name,
      from_email: @email_campaign.from_email
    }
  end

  # GET /campaigns/:campaign_id/email_content/new
  def new
    Rails.logger.info "🔍 Session content: #{session[:generated_email_content].inspect}"

    # Check if we have generated content in session first (takes priority)
    if session[:generated_email_content].present?
      Rails.logger.info "🎯 Loading generated content from session for review"
      @email_campaign = @campaign.build_email_campaign(session[:generated_email_content])
      @showing_generated_content = true
      Rails.logger.info "✅ Email campaign built with: #{@email_campaign.attributes.inspect}"

      # Keep the session data until content is successfully saved
      # session.delete(:generated_email_content) - moved to create action
      Rails.logger.info "📝 Keeping generated content in session until saved"
    elsif @campaign.email_campaign.present?
      # Only redirect to show if no generated content is waiting for review
      Rails.logger.info "📧 Email campaign exists, redirecting to show page"
      redirect_to campaign_email_content_path(@campaign)
      nil
    else
      # Create new email campaign with defaults
      Rails.logger.info "📝 Creating new email campaign with defaults"
      @email_campaign = @campaign.build_email_campaign(default_email_attributes)
      @showing_generated_content = false
    end
  end

  # GET /campaigns/:campaign_id/email_content/edit
  def edit
    @email_campaign = @campaign.email_campaign
    redirect_to new_campaign_email_content_path(@campaign) unless @email_campaign
  end

  # POST /campaigns/:campaign_id/email_content
  def create
    Rails.logger.info "📝 Creating email content with params: #{params.inspect}"

    # Try to get email_campaign params, fallback to session data if params are missing
    begin
      campaign_params = email_campaign_params
    rescue ActionController::ParameterMissing => e
      Rails.logger.info "⚠️ Missing email_campaign params, checking session data"
      if session[:generated_email_content].present?
        Rails.logger.info "✅ Using session data as fallback"
        campaign_params = session[:generated_email_content]
      else
        Rails.logger.error "❌ No email_campaign params and no session data available"
        flash[:alert] = "No email content data available. Please generate content first."
        redirect_to new_campaign_email_content_path(@campaign)
        return
      end
    end

    # Check if email campaign already exists
    if @campaign.email_campaign.present?
      @email_campaign = @campaign.email_campaign
      success = @email_campaign.update(campaign_params)
    else
      @email_campaign = @campaign.build_email_campaign(campaign_params)
      success = @email_campaign.save
    end

    if success
      # Track AI attribution for individual fields if this was generated content
      if session[:generated_email_content].present?
        track_field_attribution_from_session
      end

      # Clear the generated content session after successful save
      session.delete(:generated_email_content)
      flash[:notice] = "Email content created successfully!"
      redirect_to campaign_email_content_path(@campaign)
    else
      # Keep the generated content in session for retry
      @showing_generated_content = session[:generated_email_content].present?
      flash.now[:alert] = "Failed to create email content: #{@email_campaign.errors.full_messages.join(', ')}"
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /campaigns/:campaign_id/email_content
  def update
    @email_campaign = @campaign.email_campaign
    if @email_campaign&.update(email_campaign_params)
      flash[:notice] = "Email content updated successfully!"
      redirect_to campaign_email_content_path(@campaign)
    else
      flash.now[:alert] = "Failed to update email content."
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /campaigns/:campaign_id/email_content
  def destroy
    @email_campaign = @campaign.email_campaign
    @email_campaign&.destroy
    flash[:notice] = "Email content deleted successfully!"
    redirect_to campaign_path(@campaign)
  end

  # GET /campaigns/:campaign_id/email_content/preview
  def preview
    Rails.logger.info "🔍 Preview action called with format: #{request.format}"
    Rails.logger.info "🔍 Request headers: #{request.headers['Accept']}"

    @email_campaign = @campaign.email_campaign

    unless @email_campaign
      flash[:alert] = "No email content available to preview. Please create email content first."
      redirect_to new_campaign_email_content_path(@campaign)
      return
    end

    Rails.logger.info "✅ Email campaign found: #{@email_campaign.subject_line}"

    respond_to do |format|
      format.html {
        Rails.logger.info "📧 Rendering HTML preview template"
        render layout: false
      }
      format.json {
        Rails.logger.info "📊 Rendering JSON response"
        render json: {
          subject_line: @email_campaign.subject_line,
          preview_text: @email_campaign.preview_text,
          content: @email_campaign.content,
          from_name: @email_campaign.from_name,
          from_email: @email_campaign.from_email,
          estimated_send_time: @email_campaign.estimated_send_time
        }
      }
    end
  end

  # POST /campaigns/:campaign_id/email_content/:id/generate_ai_content
  def generate_ai_content
    Rails.logger.info "🤖 Starting AI content generation for campaign: #{@campaign.name}"

    service = EmailSpecialistAgentService.new(campaign: @campaign)
    result = service.generate_campaign_content(
      brand_voice: params[:brand_voice] || "professional",
      email_type: params[:email_type] || "promotional",
      key_message: params[:key_message]
    )

    Rails.logger.info "📊 AI service result: #{result.inspect}"

    if result[:status] == "success"
      # Extract the generated content from campaign_data (not :content)
      generated_content = result[:campaign_data]

      Rails.logger.info "📝 Generated content structure: #{generated_content.inspect}"

      # Track AI attribution for the generated content
      attribution_service = AiAttributionService.new(user: current_user, generatable: @campaign)

      # Track full email generation
      attribution_service.track_generation(
        content_type: 'full_email',
        result: result,
        input_prompt: build_generation_prompt_summary
      )

      if generated_content.present?
        # Store generated content in session to pre-fill the form
        session_content = {
          subject_line: generated_content[:subject_line] || "AI Generated Subject",
          preview_text: generated_content[:preview_text] || "AI Generated Preview",
          content: generated_content[:content] || "AI Generated Content",
          from_name: generated_content[:from_name] || current_user.full_name || current_tenant.name,
          from_email: generated_content[:from_email] || current_user.email
        }

        session[:generated_email_content] = session_content

        # Store AI generation metadata for attribution tracking
        session[:ai_generation_metadata] = {
          model_used: result[:model_used],
          generation_parameters: result[:generation_parameters],
          token_count: result[:token_count],
          generation_cost: result[:generation_cost],
          generation_time_ms: result[:generation_time_ms],
          confidence_score: result[:confidence_score],
          source: result[:source]
        }

        Rails.logger.info "✅ Content stored in session: #{session_content.inspect}"
        Rails.logger.info "🔍 Session after storage: #{session[:generated_email_content].inspect}"

        flash[:notice] = "🎉 AI email content generated successfully! Review the content below and make any adjustments before saving."
        redirect_to new_campaign_email_content_path(@campaign)
      else
        Rails.logger.error "❌ Generated content is nil or empty"
        flash[:alert] = "AI content was generated but could not be extracted. Please try again."
        redirect_to new_campaign_email_content_path(@campaign)
      end
    else
      Rails.logger.error "❌ AI generation failed: #{result[:message]}"
      flash[:alert] = "Failed to generate AI content: #{result[:message] || 'Unknown error'}"
      redirect_to new_campaign_email_content_path(@campaign)
    end
  rescue => error
    Rails.logger.error "💥 Exception in generate_ai_content: #{error.message}"
    Rails.logger.error "📍 Backtrace: #{error.backtrace.first(5).join('\n')}"
    flash[:alert] = "An error occurred while generating AI content. Please try again."
    redirect_to new_campaign_email_content_path(@campaign)
  end

  # POST /campaigns/:campaign_id/email_content/:id/optimize_content
  def optimize_content
    service = EmailSpecialistAgentService.new(campaign: @campaign)
    result = service.optimize_campaign_content(
      optimization_goals: params[:optimization_goals]&.split(",")&.map(&:strip) || [ "engagement" ],
      target_metrics: params[:target_metrics]
    )

    if result[:status] == "success"
      flash[:notice] = "Email content optimized successfully!"
    else
      flash[:alert] = "Failed to optimize content: #{result[:message]}"
    end

    redirect_to campaign_email_content_path(@campaign)
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def set_campaign
    @campaign = current_tenant.campaigns.find(params[:campaign_id])
  end

  def set_email_campaign
    @email_campaign = @campaign.email_campaign
    redirect_to new_campaign_email_content_path(@campaign) unless @email_campaign
  end

  def email_campaign_params
    params.require(:email_campaign).permit(
      :subject_line, :preview_text, :content, :from_name, :from_email,
      settings: {},
      images: [],
      documents: [],
      audio_files: [],
      video_files: []
    )
  end

  def default_email_attributes
    {
      subject_line: "#{@campaign.name} - Email Campaign",
      content: "Email content for #{@campaign.name}",
      from_name: current_user.full_name || current_tenant.name,
      from_email: current_user.email
    }
  end

  def build_generation_prompt_summary
    "Email content generation for campaign: #{@campaign.name}\n" \
    "Brand voice: #{params[:brand_voice] || 'professional'}\n" \
    "Email type: #{params[:email_type] || 'promotional'}\n" \
    "Key message: #{params[:key_message] || 'N/A'}"
  end

  def track_field_attribution_from_session
    return unless @email_campaign && session[:ai_generation_metadata]

    attribution_service = AiAttributionService.new(user: current_user, generatable: @email_campaign)
    metadata = session[:ai_generation_metadata]

    # Track individual field attributions
    %w[subject_line preview_text content].each do |field|
      if session[:generated_email_content][field].present?
        # Create a field-specific result for tracking
        field_result = {
          model_used: metadata[:model_used],
          ai_model_name: metadata[:model_used],
          generation_parameters: metadata[:generation_parameters],
          token_count: metadata[:token_count],
          generation_cost: metadata[:generation_cost],
          generation_time_ms: metadata[:generation_time_ms],
          confidence_score: metadata[:confidence_score],
          source: metadata[:source]
        }

        attribution_service.track_generation(
          content_type: field,
          result: field_result,
          input_prompt: build_generation_prompt_summary
        )
      end
    end

    # Clear the metadata from session
    session.delete(:ai_generation_metadata)
  end
end
