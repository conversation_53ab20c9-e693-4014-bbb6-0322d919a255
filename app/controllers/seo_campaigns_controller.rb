# frozen_string_literal: true

class SeoCampaignsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_campaign
  before_action :set_seo_campaign, only: [:show, :edit, :update, :destroy]

  # GET /campaigns/:campaign_id/seo_campaign
  def show
    @optimization_score = @seo_campaign.optimization_score
    @keyword_list = @seo_campaign.keyword_list
    @content_pillars = @seo_campaign.content_pillars
    @target_pages = @seo_campaign.target_pages
  end

  # GET /campaigns/:campaign_id/seo_campaign/new
  def new
    @seo_campaign = @campaign.build_seo_campaign
    @suggested_keywords = generate_suggested_keywords
    @content_templates = load_content_templates
  end

  # GET /campaigns/:campaign_id/seo_campaign/edit
  def edit
    @suggested_keywords = generate_suggested_keywords
    @content_templates = load_content_templates
    @current_score = @seo_campaign.optimization_score
  end

  # POST /campaigns/:campaign_id/seo_campaign
  def create
    @seo_campaign = @campaign.build_seo_campaign(seo_campaign_params)

    if @seo_campaign.save
      # Track the creation
      track_seo_campaign_event('created')
      
      # Generate AI-enhanced strategy if requested
      if params[:generate_ai_strategy] == 'true'
        generate_ai_strategy_async
        flash[:notice] = "SEO campaign created successfully! AI strategy generation is in progress."
      else
        flash[:notice] = "SEO campaign created successfully!"
      end
      
      redirect_to campaign_seo_campaign_path(@campaign)
    else
      @suggested_keywords = generate_suggested_keywords
      @content_templates = load_content_templates
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /campaigns/:campaign_id/seo_campaign
  def update
    if @seo_campaign.update(seo_campaign_params)
      track_seo_campaign_event('updated')
      
      # Regenerate AI strategy if requested
      if params[:regenerate_ai_strategy] == 'true'
        generate_ai_strategy_async
        flash[:notice] = "SEO campaign updated successfully! AI strategy regeneration is in progress."
      else
        flash[:notice] = "SEO campaign updated successfully!"
      end
      
      redirect_to campaign_seo_campaign_path(@campaign)
    else
      @suggested_keywords = generate_suggested_keywords
      @content_templates = load_content_templates
      @current_score = @seo_campaign.optimization_score
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /campaigns/:campaign_id/seo_campaign
  def destroy
    @seo_campaign.destroy
    track_seo_campaign_event('deleted')
    flash[:notice] = "SEO campaign deleted successfully!"
    redirect_to campaign_path(@campaign)
  end

  # POST /campaigns/:campaign_id/seo_campaign/generate_ai_strategy
  def generate_ai_strategy
    unless @seo_campaign
      render json: { success: false, error: "SEO campaign not found" }, status: :not_found
      return
    end

    begin
      service = SeoSpecialistAgentService.new(
        campaign: @campaign,
        seo_campaign: @seo_campaign,
        tenant: current_tenant
      )

      result = service.generate_comprehensive_strategy(
        focus_keywords: @seo_campaign.keyword_list,
        target_audience: @campaign.target_audience,
        business_context: extract_business_context
      )

      if result[:status] == "success"
        # Update the SEO campaign with AI-generated strategy
        update_seo_campaign_with_ai_strategy(result)
        
        render json: {
          success: true,
          message: "AI strategy generated successfully!",
          strategy: result[:strategy_data],
          optimization_score: @seo_campaign.reload.optimization_score
        }
      else
        render json: {
          success: false,
          error: result[:message] || "Failed to generate AI strategy"
        }, status: :unprocessable_entity
      end
    rescue => e
      Rails.logger.error "SEO AI Strategy Generation Error: #{e.message}"
      render json: {
        success: false,
        error: "An error occurred while generating the AI strategy"
      }, status: :internal_server_error
    end
  end

  # POST /campaigns/:campaign_id/seo_campaign/optimize_keywords
  def optimize_keywords
    unless @seo_campaign
      render json: { success: false, error: "SEO campaign not found" }, status: :not_found
      return
    end

    begin
      service = SeoSpecialistAgentService.new(
        campaign: @campaign,
        seo_campaign: @seo_campaign,
        tenant: current_tenant
      )

      result = service.research_keywords(
        focus_keywords: params[:focus_keywords]&.split(',')&.map(&:strip)
      )

      if result[:status] == "success"
        render json: {
          success: true,
          keywords: result[:keyword_strategy],
          suggestions: result[:primary_keywords] + result[:long_tail_keywords]
        }
      else
        render json: {
          success: false,
          error: result[:message] || "Failed to optimize keywords"
        }, status: :unprocessable_entity
      end
    rescue => e
      Rails.logger.error "SEO Keyword Optimization Error: #{e.message}"
      render json: {
        success: false,
        error: "An error occurred while optimizing keywords"
      }, status: :internal_server_error
    end
  end

  # GET /campaigns/:campaign_id/seo_campaign/analytics
  def analytics
    unless @seo_campaign
      redirect_to new_campaign_seo_campaign_path(@campaign), alert: "Please set up your SEO campaign first."
      return
    end

    begin
      service = SeoSpecialistAgentService.new(
        campaign: @campaign,
        seo_campaign: @seo_campaign,
        tenant: current_tenant
      )

      @analytics_data = service.analyze_seo_performance
      @recommendations = @analytics_data[:recommendations] || []
      @performance_metrics = @analytics_data[:performance_data] || {}
      @technical_audit = @analytics_data[:technical_audit] || {}
    rescue => e
      Rails.logger.error "SEO Analytics Error: #{e.message}"
      flash[:alert] = "Unable to load SEO analytics at this time."
      redirect_to campaign_seo_campaign_path(@campaign)
    end
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def set_campaign
    @campaign = current_tenant.campaigns.find(params[:campaign_id])
  end

  def set_seo_campaign
    @seo_campaign = @campaign.seo_campaign
    unless @seo_campaign
      redirect_to new_campaign_seo_campaign_path(@campaign), 
                  alert: "Please set up your SEO campaign first."
    end
  end

  def seo_campaign_params
    params.require(:seo_campaign).permit(
      :target_keywords,
      :meta_title,
      :meta_description,
      content_strategy: {},
      technical_settings: {},
      seo_settings: {}
    )
  end

  def generate_suggested_keywords
    return [] unless @campaign

    base_keywords = [
      @campaign.name,
      @campaign.target_audience,
      @campaign.description
    ].compact.join(' ').downcase.split(/\W+/).uniq.reject(&:blank?)

    # Add some intelligent suggestions based on campaign type and industry
    industry_keywords = suggest_industry_keywords
    base_keywords + industry_keywords
  end

  def suggest_industry_keywords
    # This could be enhanced with AI or external keyword tools
    case @campaign.campaign_type
    when 'email'
      ['email marketing', 'newsletter', 'email automation', 'customer engagement']
    when 'social'
      ['social media', 'content marketing', 'brand awareness', 'social engagement']
    when 'seo'
      ['search optimization', 'organic traffic', 'keyword ranking', 'content strategy']
    when 'multi_channel'
      ['digital marketing', 'marketing automation', 'lead generation', 'conversion optimization']
    else
      ['marketing', 'business growth', 'customer acquisition', 'brand building']
    end
  end

  def load_content_templates
    [
      {
        name: "Blog Post Strategy",
        description: "Optimize blog content for target keywords",
        pillars: ["Educational Content", "Industry Insights", "How-to Guides"]
      },
      {
        name: "Landing Page Strategy",
        description: "Create high-converting landing pages",
        pillars: ["Value Proposition", "Social Proof", "Clear CTAs"]
      },
      {
        name: "Product Page Strategy",
        description: "Optimize product pages for conversions",
        pillars: ["Product Benefits", "Customer Reviews", "Technical Specs"]
      }
    ]
  end

  def extract_business_context
    {
      industry: current_tenant.settings.dig('industry') || 'general',
      business_type: current_tenant.settings.dig('business_type') || 'service',
      target_market: @campaign.target_audience,
      campaign_goals: @campaign.settings.dig('goals') || [],
      brand_voice: current_tenant.settings.dig('brand_voice') || 'professional'
    }
  end

  def generate_ai_strategy_async
    # This could be moved to a background job for better UX
    SeoStrategyGenerationJob.perform_later(@campaign.id, current_user.id)
  end

  def update_seo_campaign_with_ai_strategy(result)
    strategy_data = result[:strategy_data] || {}
    
    @seo_campaign.update!(
      content_strategy: strategy_data[:content_strategy] || @seo_campaign.content_strategy,
      technical_settings: strategy_data[:technical_settings] || @seo_campaign.technical_settings,
      seo_settings: strategy_data[:seo_settings] || @seo_campaign.seo_settings
    )
  end

  def track_seo_campaign_event(action)
    # Track analytics event
    Rails.logger.info "SEO Campaign #{action}: Campaign #{@campaign.id}, User #{current_user.id}"
  end
end
