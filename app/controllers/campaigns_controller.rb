# frozen_string_literal: true

require_relative "../contracts/campaign_contract"

class CampaignsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_campaign, only: [ :show, :edit, :update, :destroy, :activate, :pause, :complete, :analytics ]

  def index
    # Use optimized query service for better performance
    query_service = CampaignQueryService.new(current_tenant)
    cache_service = CampaignCacheService.new(current_tenant)

    # Get filtered campaigns with optimized queries
    filters = {
      status: params[:status],
      type: params[:type],
      search: params[:search],
      sort: params[:sort],
      page: params[:page]
    }.compact

    @campaigns = query_service.filtered_campaigns(filters)

    # Pagination (if pagy gem is available)
    if respond_to?(:pagy) && !filters[:page]
      @pagy, @campaigns = pagy(@campaigns, items: 12)
    end

    # Get cached campaign statistics
    @campaign_stats = cache_service.cached_campaign_stats
  end

  def show
    # Use optimized query and caching services
    query_service = CampaignQueryService.new(current_tenant)
    cache_service = CampaignCacheService.new(current_tenant)

    begin
      # Get campaign with eager loaded associations
      @campaign = query_service.campaign_with_associations(params[:id])

      # Get cached performance summary
      @performance_summary = cache_service.cached_performance_summary(@campaign)

      # Get recent metrics for the chart
      @campaign_metrics = @campaign.campaign_metrics
                                  .where(metric_date: 30.days.ago..Date.current)
                                  .order(:metric_date)
    rescue ActiveRecord::StatementInvalid => e
      # Handle case when campaign_metrics table doesn't exist yet
      Rails.logger.warn "Campaign metrics table not found: #{e.message}"
      @campaign_metrics = []
      @performance_summary = {}
    end
  end

  def new
    @campaign = current_tenant.campaigns.build
    @campaign.created_by = current_user
  end

  def create
    # Validate input using contract
    begin
      contract_result = CampaignContract.new.call(campaign_contract_params.to_h)
    rescue NameError => e
      Rails.logger.error "CampaignContract not found: #{e.message}"
      # Fallback to basic validation without contract
      @campaign = current_tenant.campaigns.build(campaign_params)
      @campaign.created_by = current_user

      if @campaign.save
        create_associated_campaign_type
        cache_service = CampaignCacheService.new(current_tenant)
        cache_service.invalidate_tenant_aggregate_caches
        redirect_to @campaign, notice: "Campaign was successfully created."
      else
        flash.now[:alert] = "Campaign could not be created: #{@campaign.errors.full_messages.join(', ')}"
        render :new, status: :unprocessable_entity
      end
      return
    end

    if contract_result.success?
      @campaign = current_tenant.campaigns.build(contract_result.to_h)
      @campaign.created_by = current_user

      if @campaign.save
        create_associated_campaign_type

        # Invalidate tenant-wide caches after creating new campaign
        cache_service = CampaignCacheService.new(current_tenant)
        cache_service.invalidate_tenant_aggregate_caches

        redirect_to @campaign, notice: "Campaign was successfully created."
      else
        # Handle ActiveRecord validation errors
        flash.now[:alert] = "Campaign could not be created: #{@campaign.errors.full_messages.join(', ')}"
        render :new, status: :unprocessable_entity
      end
    else
      # Handle contract validation errors
      @campaign = current_tenant.campaigns.build(campaign_contract_params.to_h)
      @campaign.created_by = current_user
      flash.now[:alert] = "Campaign validation failed: #{format_contract_errors(contract_result.errors)}"
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    # Edit campaign form
  end

  def update
    # Validate input using contract
    begin
      contract_result = CampaignContract.new.call(campaign_contract_params.to_h)
    rescue NameError => e
      Rails.logger.error "CampaignContract not found: #{e.message}"
      # Fallback to basic validation without contract
      if @campaign.update(campaign_params)
        cache_service = CampaignCacheService.new(current_tenant)
        cache_service.invalidate_campaign_caches(@campaign)
        redirect_to @campaign, notice: "Campaign was successfully updated."
      else
        flash.now[:alert] = "Campaign could not be updated: #{@campaign.errors.full_messages.join(', ')}"
        render :edit, status: :unprocessable_entity
      end
      return
    end

    if contract_result.success?
      if @campaign.update(contract_result.to_h)
        # Invalidate caches for this campaign after update
        cache_service = CampaignCacheService.new(current_tenant)
        cache_service.invalidate_campaign_caches(@campaign)

        redirect_to @campaign, notice: "Campaign was successfully updated."
      else
        # Handle ActiveRecord validation errors
        flash.now[:alert] = "Campaign could not be updated: #{@campaign.errors.full_messages.join(', ')}"
        render :edit, status: :unprocessable_entity
      end
    else
      # Handle contract validation errors
      flash.now[:alert] = "Campaign validation failed: #{format_contract_errors(contract_result.errors)}"
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    # Invalidate caches before destroying the campaign
    cache_service = CampaignCacheService.new(current_tenant)
    cache_service.invalidate_campaign_caches(@campaign)

    @campaign.destroy
    redirect_to campaigns_url, notice: "Campaign was successfully deleted."
  end

  def activate
    if @campaign.can_be_activated?
      @campaign.update(status: "active")
      redirect_to @campaign, notice: "Campaign activated successfully."
    else
      redirect_to @campaign, alert: "Campaign cannot be activated in its current state."
    end
  end

  def pause
    if @campaign.active?
      @campaign.update(status: "paused")
      redirect_to @campaign, notice: "Campaign paused successfully."
    else
      redirect_to @campaign, alert: "Only active campaigns can be paused."
    end
  end

  def complete
    if @campaign.active? || @campaign.paused?
      @campaign.update(status: "completed")
      redirect_to @campaign, notice: "Campaign completed successfully."
    else
      redirect_to @campaign, alert: "Campaign cannot be completed in its current state."
    end
  end

  def analytics
    # Campaign Analytics Deep Dive
    @campaign_data = prepare_analytics_data
    @performance_metrics = calculate_detailed_metrics
    @chart_data = prepare_chart_data
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def set_campaign
    @campaign = current_tenant.campaigns.find(params[:id])
  end

  def campaign_params
    permitted_params = params.require(:campaign).permit(
      :name, :description, :campaign_type, :target_audience,
      :start_date, :end_date, :budget_in_dollars, :status,
      settings: {}
    )

    # Convert budget_in_dollars to budget_cents if present
    if permitted_params[:budget_in_dollars].present?
      permitted_params[:budget_cents] = (permitted_params[:budget_in_dollars].to_f * 100).round
      permitted_params.delete(:budget_in_dollars)
    end

    permitted_params
  end

  def campaign_contract_params
    permitted_params = params.require(:campaign).permit(
      :name, :description, :campaign_type, :target_audience,
      :start_date, :end_date, :budget_in_dollars
    )

    # Convert budget_in_dollars to budget_cents for contract validation
    if permitted_params[:budget_in_dollars].present?
      permitted_params[:budget_cents] = (permitted_params[:budget_in_dollars].to_f * 100).round
      permitted_params.delete(:budget_in_dollars)
    end

    permitted_params
  end

  def format_contract_errors(errors)
    errors.to_h.map do |field, messages|
      "#{field.to_s.humanize}: #{Array(messages).join(', ')}"
    end.join("; ")
  end

  def calculate_performance_summary
    return {} if @campaign_metrics.nil? || @campaign_metrics.empty?

    {
      total_impressions: @campaign_metrics.sum(:impressions),
      total_clicks: @campaign_metrics.sum(:clicks),
      total_conversions: @campaign_metrics.sum(:conversions),
      total_revenue: @campaign_metrics.sum(:revenue_cents) / 100.0,
      total_cost: @campaign_metrics.sum(:cost_cents) / 100.0,
      average_ctr: calculate_average_ctr,
      average_conversion_rate: calculate_average_conversion_rate
    }
  end

  def calculate_average_ctr
    return 0.0 if @campaign_metrics.nil? || @campaign_metrics.empty?

    total_impressions = @campaign_metrics.sum(:impressions)
    total_clicks = @campaign_metrics.sum(:clicks)
    return 0.0 if total_impressions.zero?

    (total_clicks.to_f / total_impressions * 100).round(2)
  end

  def calculate_average_conversion_rate
    return 0.0 if @campaign_metrics.nil? || @campaign_metrics.empty?

    total_clicks = @campaign_metrics.sum(:clicks)
    total_conversions = @campaign_metrics.sum(:conversions)
    return 0.0 if total_clicks.zero?

    (total_conversions.to_f / total_clicks * 100).round(2)
  end

  def create_associated_campaign_type
    case @campaign.campaign_type
    when "email"
      # Create placeholder email campaign - will be configured later
      @campaign.create_email_campaign!(
        subject_line: "#{@campaign.name} - Email Campaign",
        content: "Email content for #{@campaign.name}",
        from_name: current_user.full_name,
        from_email: current_user.email
      )
    when "social"
      # Create placeholder social campaign - will be configured later
      @campaign.create_social_campaign!(
        platforms: [ "twitter" ],
        content_variants: { "twitter" => "Social content for #{@campaign.name}" }
      )
    when "seo"
      # Create placeholder SEO campaign - will be configured later
      @campaign.create_seo_campaign!(
        target_keywords: @campaign.target_audience,
        meta_title: @campaign.name,
        meta_description: @campaign.description || "SEO campaign for #{@campaign.name}",
        content_strategy: {
          "pillars" => [],
          "target_pages" => [],
          "content_calendar" => {}
        }
      )
    end
  rescue => e
    Rails.logger.error "Failed to create associated campaign type: #{e.message}"
    # Don't fail the main campaign creation if associated type fails
  end

  def prepare_analytics_data
    {
      campaign_name: @campaign.name,
      campaign_type: @campaign.campaign_type&.titleize || "General",
      status: @campaign.status&.titleize || "Draft",
      budget: @campaign.budget_cents ? (@campaign.budget_cents / 100.0) : 0,
      start_date: @campaign.start_date,
      end_date: @campaign.end_date,
      days_running: calculate_days_running,
      target_audience: @campaign.target_audience
    }
  end

  def calculate_detailed_metrics
    # Mock data for demonstration - replace with real analytics
    {
      total_impressions: 2_456_890,
      total_clicks: 45_672,
      total_conversions: 1_234,
      total_revenue: 98_750.50,
      total_cost: 12_450.00,
      ctr: 1.86,
      conversion_rate: 2.70,
      roas: 7.93,
      cost_per_click: 0.27,
      cost_per_conversion: 10.09
    }
  end

  def prepare_chart_data
    # Mock chart data - replace with real data from your analytics
    {
      performance_chart: generate_performance_chart_data,
      conversion_funnel: generate_conversion_funnel_data,
      audience_breakdown: generate_audience_breakdown_data,
      platform_performance: generate_platform_performance_data
    }
  end

  def calculate_days_running
    return 0 unless @campaign.start_date
    
    end_date = @campaign.end_date || Date.current
    start_date = @campaign.start_date
    
    return 0 if start_date > Date.current
    
    (end_date - start_date).to_i + 1
  end

  def generate_performance_chart_data
    # 30 days of mock performance data
    dates = (30.days.ago.to_date..Date.current).to_a
    {
      labels: dates.map { |date| date.strftime("%m/%d") },
      impressions: dates.map { |_| rand(50_000..100_000) },
      clicks: dates.map { |_| rand(800..2_000) },
      conversions: dates.map { |_| rand(20..80) },
      revenue: dates.map { |_| rand(1_000..5_000) }
    }
  end

  def generate_conversion_funnel_data
    [
      { stage: "Impressions", count: 2_456_890, percentage: 100 },
      { stage: "Clicks", count: 45_672, percentage: 1.86 },
      { stage: "Visits", count: 42_105, percentage: 1.71 },
      { stage: "Add to Cart", count: 3_567, percentage: 0.15 },
      { stage: "Conversions", count: 1_234, percentage: 0.05 }
    ]
  end

  def generate_audience_breakdown_data
    {
      demographics: {
        "18-24" => 15,
        "25-34" => 35,
        "35-44" => 28,
        "45-54" => 15,
        "55+" => 7
      },
      interests: {
        "Technology" => 25,
        "Business" => 20,
        "Marketing" => 18,
        "Design" => 15,
        "Other" => 22
      },
      devices: {
        "Desktop" => 45,
        "Mobile" => 42,
        "Tablet" => 13
      }
    }
  end

  def generate_platform_performance_data
    [
      { platform: "Google Ads", impressions: 1_234_567, clicks: 23_456, conversions: 678, cost: 5_678.90 },
      { platform: "Facebook", impressions: 987_654, clicks: 18_765, conversions: 432, cost: 4_321.00 },
      { platform: "Instagram", impressions: 234_669, clicks: 3_451, conversions: 124, cost: 2_450.10 }
    ]
  end
end
