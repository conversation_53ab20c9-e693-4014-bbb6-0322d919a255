# frozen_string_literal: true

class Api::AiModelsController < ApplicationController
  before_action :authenticate_user!
  
  # GET /api/ai_models/status
  def status
    begin
      ai_model_service = AiModelService.new
      
      # Get fresh model data
      available_models = ai_model_service.available_models
      provider_stats = ai_model_service.provider_statistics
      recommended_models = ai_model_service.recommended_models_for_task('email_content')
      
      # Calculate totals
      total_available = available_models.count { |m| m[:status] == :available }
      total_models = available_models.count
      total_configured = available_models.count { |m| m[:status] != :not_configured }
      
      render json: {
        success: true,
        models: available_models,
        recommendedModels: recommended_models,
        providerStats: provider_stats,
        totals: {
          available: total_available,
          total: total_models,
          configured: total_configured,
          availability_percentage: total_models > 0 ? (total_available.to_f / total_models * 100).round(1) : 0
        },
        lastUpdated: Time.current.iso8601,
        refreshedAt: Time.current.to_i
      }
    rescue => e
      Rails.logger.error "AI Models Status Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      render json: {
        success: false,
        error: "Failed to refresh model status",
        message: e.message,
        timestamp: Time.current.iso8601
      }, status: :internal_server_error
    end
  end
  
  # GET /api/ai_models/:id/test
  def test_connection
    begin
      model_id = params[:id]
      ai_model_service = AiModelService.new
      
      result = ai_model_service.test_model_connection(model_id)
      
      if result[:success]
        render json: {
          success: true,
          model_id: model_id,
          status: 'available',
          response_time: result[:response_time],
          message: result[:message] || 'Connection successful',
          tested_at: Time.current.iso8601
        }
      else
        render json: {
          success: false,
          model_id: model_id,
          status: 'error',
          error: result[:error],
          tested_at: Time.current.iso8601
        }, status: :unprocessable_entity
      end
    rescue => e
      Rails.logger.error "AI Model Test Error: #{e.message}"
      
      render json: {
        success: false,
        model_id: params[:id],
        error: "Failed to test model connection",
        message: e.message,
        tested_at: Time.current.iso8601
      }, status: :internal_server_error
    end
  end
  
  # GET /api/ai_models/recommendations
  def recommendations
    begin
      task_type = params[:task_type] || 'email_content'
      prefer_cost_effective = params[:cost_effective] == 'true'
      
      ai_model_service = AiModelService.new
      recommended_models = ai_model_service.recommended_models_for_task(task_type)
      best_model = ai_model_service.best_model_for_task(task_type, prefer_cost_effective: prefer_cost_effective)
      
      render json: {
        success: true,
        task_type: task_type,
        recommended_models: recommended_models,
        best_model: best_model,
        prefer_cost_effective: prefer_cost_effective,
        generated_at: Time.current.iso8601
      }
    rescue => e
      Rails.logger.error "AI Model Recommendations Error: #{e.message}"
      
      render json: {
        success: false,
        error: "Failed to get model recommendations",
        message: e.message,
        task_type: params[:task_type],
        generated_at: Time.current.iso8601
      }, status: :internal_server_error
    end
  end
  
  # GET /api/ai_models/usage
  def usage_statistics
    begin
      time_period = params[:period]&.to_i&.days || 30.days
      
      ai_model_service = AiModelService.new
      usage_stats = ai_model_service.usage_statistics(time_period)
      
      render json: {
        success: true,
        usage_statistics: usage_stats,
        period_days: time_period.to_i / 1.day,
        generated_at: Time.current.iso8601
      }
    rescue => e
      Rails.logger.error "AI Model Usage Statistics Error: #{e.message}"
      
      render json: {
        success: false,
        error: "Failed to get usage statistics",
        message: e.message,
        generated_at: Time.current.iso8601
      }, status: :internal_server_error
    end
  end
  
  # POST /api/ai_models/:id/select
  def select_model
    begin
      model_id = params[:id]
      task_type = params[:task_type] || 'email_content'
      
      ai_model_service = AiModelService.new
      
      # Verify model is available
      unless ai_model_service.model_available?(model_id)
        return render json: {
          success: false,
          error: "Model not available",
          model_id: model_id,
          timestamp: Time.current.iso8601
        }, status: :unprocessable_entity
      end
      
      # Store user's model preference (you might want to implement this)
      # current_user.update(preferred_ai_model: model_id)
      
      # Track the selection
      track_model_selection(model_id, task_type)
      
      render json: {
        success: true,
        selected_model: model_id,
        task_type: task_type,
        message: "Model selected successfully",
        selected_at: Time.current.iso8601
      }
    rescue => e
      Rails.logger.error "AI Model Selection Error: #{e.message}"
      
      render json: {
        success: false,
        error: "Failed to select model",
        message: e.message,
        model_id: params[:id],
        selected_at: Time.current.iso8601
      }, status: :internal_server_error
    end
  end
  
  private
  
  def track_model_selection(model_id, task_type)
    # Track model selection for analytics
    # You can implement your analytics tracking here
    Rails.logger.info "Model selected: #{model_id} for task: #{task_type} by user: #{current_user.id}"
    
    # Example: Send to analytics service
    # AnalyticsService.track_event(
    #   user_id: current_user.id,
    #   event: 'ai_model_selected',
    #   properties: {
    #     model_id: model_id,
    #     task_type: task_type,
    #     timestamp: Time.current.iso8601
    #   }
    # )
  end
  
  def model_params
    params.permit(:task_type, :cost_effective, :period)
  end
end
