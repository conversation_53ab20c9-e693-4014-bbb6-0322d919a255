# frozen_string_literal: true

class Api::AiAttributionController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :find_attributable_object, only: [:details, :export]

  # GET /api/ai_attribution/:object_type/:object_id/details
  def details
    attribution_service = AiAttributionService.new(user: current_user, generatable: @object)
    
    render json: {
      success: true,
      attribution_summary: attribution_service.attribution_info,
      transparency_details: attribution_service.transparency_details,
      generation_events: @object.ai_generation_events.recent.limit(10).map(&:generation_details),
      last_updated: Time.current.iso8601
    }
  rescue => e
    Rails.logger.error "AI Attribution Details Error: #{e.message}"
    render json: {
      success: false,
      error: "Failed to load attribution details",
      message: e.message
    }, status: :internal_server_error
  end

  # GET /api/ai_attribution/:object_type/:object_id/export
  def export
    attribution_service = AiAttributionService.new(user: current_user, generatable: @object)
    export_type = params[:type] || 'full'
    
    case export_type
    when 'field'
      field = params[:field]
      return render_error("Field parameter required for field export") unless field
      
      export_data = attribution_service.transparency_details(field)
      filename = "ai_attribution_#{field}_#{@object.class.name.downcase}_#{@object.id}"
    when 'full'
      export_data = attribution_service.export_attribution_data
      filename = "ai_attribution_full_#{@object.class.name.downcase}_#{@object.id}"
    else
      return render_error("Invalid export type: #{export_type}")
    end

    return render_error("No attribution data available for export") unless export_data

    # Add export metadata
    export_data[:export_metadata] = {
      exported_by: current_user.email,
      exported_at: Time.current.iso8601,
      export_type: export_type,
      object_type: @object.class.name,
      object_id: @object.id,
      tenant_id: current_tenant.id
    }

    respond_to do |format|
      format.json do
        send_data export_data.to_json,
                  filename: "#{filename}_#{Date.current}.json",
                  type: 'application/json',
                  disposition: 'attachment'
      end
      format.csv do
        csv_data = generate_csv_export(export_data, export_type)
        send_data csv_data,
                  filename: "#{filename}_#{Date.current}.csv",
                  type: 'text/csv',
                  disposition: 'attachment'
      end
    end
  rescue => e
    Rails.logger.error "AI Attribution Export Error: #{e.message}"
    render json: {
      success: false,
      error: "Failed to export attribution data",
      message: e.message
    }, status: :internal_server_error
  end

  # GET /api/ai_attribution/usage_stats
  def usage_stats
    period = params[:period]&.to_i&.days || 30.days
    
    stats = AiGenerationEvent.usage_stats_for_tenant(current_tenant, period)
    
    # Add additional insights
    recent_events = current_tenant.ai_generation_events
                                 .includes(:user, :generatable)
                                 .recent
                                 .limit(20)
    
    render json: {
      success: true,
      period_days: period.to_i / 1.day,
      usage_stats: stats,
      recent_events: recent_events.map do |event|
        {
          id: event.id,
          content_type: event.content_type_display_name,
          model: event.model_display_name,
          user: event.user.email,
          object_type: event.generatable_type,
          object_id: event.generatable_id,
          cost: event.cost_formatted,
          tokens: event.token_count,
          created_at: event.created_at.iso8601
        }
      end,
      generated_at: Time.current.iso8601
    }
  rescue => e
    Rails.logger.error "AI Attribution Usage Stats Error: #{e.message}"
    render json: {
      success: false,
      error: "Failed to load usage statistics",
      message: e.message
    }, status: :internal_server_error
  end

  # POST /api/ai_attribution/mark_manual_edit
  def mark_manual_edit
    object_type = params[:object_type]
    object_id = params[:object_id]
    field = params[:field]
    
    return render_error("Missing required parameters") unless object_type && object_id && field
    
    object = find_object_by_type_and_id(object_type, object_id)
    return render_error("Object not found") unless object
    
    attribution_service = AiAttributionService.new(user: current_user, generatable: object)
    attribution_service.track_manual_edit(content_type: field)
    
    render json: {
      success: true,
      message: "Manual edit tracked successfully",
      field: field,
      updated_at: Time.current.iso8601
    }
  rescue => e
    Rails.logger.error "AI Attribution Manual Edit Error: #{e.message}"
    render json: {
      success: false,
      error: "Failed to track manual edit",
      message: e.message
    }, status: :internal_server_error
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def find_attributable_object
    object_type = params[:object_type]
    object_id = params[:object_id]
    
    @object = find_object_by_type_and_id(object_type, object_id)
    
    unless @object
      render json: {
        success: false,
        error: "Object not found",
        object_type: object_type,
        object_id: object_id
      }, status: :not_found
      return
    end
    
    unless @object.respond_to?(:ai_generated_content?)
      render json: {
        success: false,
        error: "Object does not support AI attribution",
        object_type: object_type
      }, status: :unprocessable_entity
      return
    end
  end

  def find_object_by_type_and_id(object_type, object_id)
    case object_type.downcase
    when 'campaign'
      current_tenant.campaigns.find_by(id: object_id)
    when 'emailcampaign'
      current_tenant.campaigns.joins(:email_campaign)
                    .find_by(email_campaigns: { id: object_id })
                    &.email_campaign
    else
      nil
    end
  end

  def generate_csv_export(data, export_type)
    require 'csv'
    
    CSV.generate(headers: true) do |csv|
      case export_type
      when 'field'
        csv << ['Field', 'AI Generated', 'Model', 'Generated At', 'Confidence', 'Cost', 'Tokens']
        if data[:ai_generated]
          csv << [
            data[:content_type],
            'Yes',
            data[:model_display_name],
            data[:generated_at],
            data[:confidence_score],
            data[:cost],
            data[:tokens]
          ]
        end
      when 'full'
        csv << ['Field', 'AI Generated', 'Model', 'Generated At', 'Cost', 'Tokens', 'Event ID']
        
        data[:ai_generated_fields].each do |field, field_data|
          csv << [
            field.humanize,
            'Yes',
            field_data[:model],
            field_data[:generated_at],
            field_data[:cost] || 'N/A',
            field_data[:tokens] || 'N/A',
            field_data[:generation_event_id] || 'N/A'
          ]
        end
        
        # Add summary row
        csv << []
        csv << ['Summary', '', '', '', '', '', '']
        csv << ['Total Cost', data[:total_ai_cost], '', '', '', '', '']
        csv << ['Total Tokens', data[:total_tokens], '', '', '', '', '']
      end
    end
  end

  def render_error(message, status = :bad_request)
    render json: {
      success: false,
      error: message
    }, status: status
  end
end
