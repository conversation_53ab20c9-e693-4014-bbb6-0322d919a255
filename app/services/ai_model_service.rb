# frozen_string_literal: true

# Service for managing AI model selection, availability, and configuration
class AiModelService
  class ModelNotAvailableError < StandardError; end
  class ConfigurationError < StandardError; end

  # Comprehensive model definitions with capabilities and pricing
  MODELS = {
    # OpenAI Models
    "gpt-4o" => {
      provider: "openai",
      name: "GPT-4o",
      description: "Most capable model for complex reasoning and creative tasks",
      capabilities: ["text", "vision", "function_calling"],
      context_window: 128_000,
      cost_per_1k_tokens: 0.005,
      performance_tier: "premium",
      strengths: ["Complex reasoning", "Creative writing", "Code generation"],
      best_for: ["complex_content", "creative_writing", "analysis"]
    },
    "gpt-4o-mini" => {
      provider: "openai",
      name: "GPT-4o Mini",
      description: "Fast and cost-effective model for most tasks",
      capabilities: ["text", "vision", "function_calling"],
      context_window: 128_000,
      cost_per_1k_tokens: 0.00015,
      performance_tier: "standard",
      strengths: ["Speed", "Cost efficiency", "General purpose"],
      best_for: ["general_content", "quick_tasks", "cost_sensitive"]
    },
    "gpt-3.5-turbo" => {
      provider: "openai",
      name: "GPT-3.5 Turbo",
      description: "Legacy model for basic content generation",
      capabilities: ["text", "function_calling"],
      context_window: 16_385,
      cost_per_1k_tokens: 0.0005,
      performance_tier: "basic",
      strengths: ["Speed", "Low cost"],
      best_for: ["simple_content", "basic_tasks"]
    },

    # Anthropic Models
    "claude-3-5-sonnet-20241022" => {
      provider: "anthropic",
      name: "Claude 3.5 Sonnet",
      description: "Advanced reasoning and analysis capabilities",
      capabilities: ["text", "vision"],
      context_window: 200_000,
      cost_per_1k_tokens: 0.003,
      performance_tier: "premium",
      strengths: ["Analysis", "Reasoning", "Safety", "Long context"],
      best_for: ["analysis", "complex_reasoning", "long_content"]
    },
    "claude-3-haiku-20240307" => {
      provider: "anthropic",
      name: "Claude 3 Haiku",
      description: "Fast and efficient for everyday tasks",
      capabilities: ["text", "vision"],
      context_window: 200_000,
      cost_per_1k_tokens: 0.00025,
      performance_tier: "standard",
      strengths: ["Speed", "Efficiency", "Cost-effective"],
      best_for: ["quick_tasks", "simple_content", "cost_sensitive"]
    },

    # Gemini Models
    "gemini-1.5-pro" => {
      provider: "gemini",
      name: "Gemini 1.5 Pro",
      description: "Google's most capable model with multimodal capabilities",
      capabilities: ["text", "vision", "audio"],
      context_window: 2_000_000,
      cost_per_1k_tokens: 0.00125,
      performance_tier: "premium",
      strengths: ["Multimodal", "Large context", "Reasoning"],
      best_for: ["multimodal_content", "large_context", "analysis"]
    },
    "gemini-1.5-flash" => {
      provider: "gemini",
      name: "Gemini 1.5 Flash",
      description: "Fast and efficient multimodal model",
      capabilities: ["text", "vision", "audio"],
      context_window: 1_000_000,
      cost_per_1k_tokens: 0.000075,
      performance_tier: "standard",
      strengths: ["Speed", "Multimodal", "Cost-effective"],
      best_for: ["quick_multimodal", "cost_sensitive", "general_content"]
    }
  }.freeze

  # Task-specific model recommendations
  TASK_RECOMMENDATIONS = {
    "email_content" => ["gpt-4o-mini", "claude-3-haiku-20240307", "gemini-1.5-flash"],
    "creative_content" => ["gpt-4o", "claude-3-5-sonnet-20241022", "gemini-1.5-pro"],
    "analysis" => ["claude-3-5-sonnet-20241022", "gpt-4o", "gemini-1.5-pro"],
    "cost_sensitive" => ["gpt-4o-mini", "claude-3-haiku-20240307", "gemini-1.5-flash"],
    "complex_reasoning" => ["gpt-4o", "claude-3-5-sonnet-20241022", "gemini-1.5-pro"]
  }.freeze

  def initialize
    @cache_duration = 5.minutes
  end

  # Get all available models with their current status
  def available_models
    Rails.cache.fetch("ai_models_status", expires_in: @cache_duration) do
      MODELS.map do |model_id, config|
        status = check_model_status(model_id, config)
        config.merge(
          id: model_id,
          status: status[:status],
          response_time: status[:response_time],
          error_message: status[:error_message],
          last_checked: Time.current
        )
      end
    end
  end

  # Get models filtered by availability status
  def available_models_by_status(status = :available)
    available_models.select { |model| model[:status] == status }
  end

  # Get models for a specific provider
  def models_for_provider(provider)
    available_models.select { |model| model[:provider] == provider }
  end

  # Get recommended models for a specific task
  def recommended_models_for_task(task_type)
    recommended_ids = TASK_RECOMMENDATIONS[task_type.to_s] || TASK_RECOMMENDATIONS["email_content"]
    available_models.select { |model| recommended_ids.include?(model[:id]) }
                   .sort_by { |model| recommended_ids.index(model[:id]) }
  end

  # Get the best available model for a task
  def best_model_for_task(task_type, prefer_cost_effective: false)
    candidates = recommended_models_for_task(task_type)
                  .select { |model| model[:status] == :available }

    return nil if candidates.empty?

    if prefer_cost_effective
      candidates.min_by { |model| model[:cost_per_1k_tokens] }
    else
      # Return the first recommended available model
      candidates.first
    end
  end

  # Check if a specific model is available
  def model_available?(model_id)
    model = available_models.find { |m| m[:id] == model_id }
    model&.dig(:status) == :available
  end

  # Get provider statistics
  def provider_statistics
    models_by_provider = available_models.group_by { |model| model[:provider] }
    
    models_by_provider.transform_values do |models|
      total = models.count
      available = models.count { |m| m[:status] == :available }
      configured = models.count { |m| m[:status] != :not_configured }
      
      {
        total: total,
        available: available,
        configured: configured,
        availability_percentage: total > 0 ? (available.to_f / total * 100).round(1) : 0,
        models: models
      }
    end
  end

  # Test connection to a specific model
  def test_model_connection(model_id)
    model_config = MODELS[model_id]
    return { success: false, error: "Model not found" } unless model_config

    check_model_status(model_id, model_config, force_check: true)
  end

  # Get cost estimate for content generation
  def estimate_cost(model_id, estimated_tokens)
    model = MODELS[model_id]
    return 0 unless model

    (estimated_tokens / 1000.0) * model[:cost_per_1k_tokens]
  end

  # Get usage statistics (would integrate with actual usage tracking)
  def usage_statistics(time_period = 30.days)
    # This would integrate with your actual usage tracking system
    # For now, returning mock data
    {
      total_requests: 1250,
      total_tokens: 2_500_000,
      total_cost: 12.50,
      by_model: {
        "gpt-4o-mini" => { requests: 800, tokens: 1_600_000, cost: 2.40 },
        "claude-3-haiku-20240307" => { requests: 300, tokens: 600_000, cost: 1.50 },
        "gpt-4o" => { requests: 150, tokens: 300_000, cost: 8.60 }
      },
      period: time_period
    }
  end

  private

  # Check the status of a specific model
  def check_model_status(model_id, config, force_check: false)
    cache_key = "model_status_#{model_id}"
    
    if force_check
      Rails.cache.delete(cache_key)
    end

    Rails.cache.fetch(cache_key, expires_in: @cache_duration) do
      start_time = Time.current
      
      begin
        case config[:provider]
        when "openai"
          check_openai_model(model_id)
        when "anthropic"
          check_anthropic_model(model_id)
        when "gemini"
          check_gemini_model(model_id)
        else
          { status: :not_supported, response_time: 0, error_message: "Provider not supported" }
        end
      rescue => e
        {
          status: :error,
          response_time: ((Time.current - start_time) * 1000).round(2),
          error_message: e.message
        }
      end
    end
  end

  # Check OpenAI model availability
  def check_openai_model(model_id)
    api_key = Rails.application.credentials.openai_api_key || ENV["OPENAI_API_KEY"]
    
    unless api_key.present?
      return { status: :not_configured, response_time: 0, error_message: "API key not configured" }
    end

    if api_key.include?("placeholder") || api_key.include?("your_")
      return { status: :placeholder, response_time: 0, error_message: "Placeholder API key detected" }
    end

    # In a real implementation, you would make a test API call here
    # For now, simulating based on API key presence
    { status: :available, response_time: rand(200..800), error_message: nil }
  end

  # Check Anthropic model availability
  def check_anthropic_model(model_id)
    api_key = Rails.application.credentials.anthropic_api_key || ENV["ANTHROPIC_API_KEY"]
    
    unless api_key.present?
      return { status: :not_configured, response_time: 0, error_message: "API key not configured" }
    end

    if api_key.include?("placeholder") || api_key.include?("your_")
      return { status: :placeholder, response_time: 0, error_message: "Placeholder API key detected" }
    end

    { status: :available, response_time: rand(300..900), error_message: nil }
  end

  # Check Gemini model availability
  def check_gemini_model(model_id)
    api_key = Rails.application.credentials.gemini_api_key || ENV["GEMINI_API_KEY"]
    
    unless api_key.present?
      return { status: :not_configured, response_time: 0, error_message: "API key not configured" }
    end

    if api_key.include?("placeholder") || api_key.include?("your_")
      return { status: :placeholder, response_time: 0, error_message: "Placeholder API key detected" }
    end

    { status: :available, response_time: rand(250..750), error_message: nil }
  end
end
