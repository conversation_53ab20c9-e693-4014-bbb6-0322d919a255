# frozen_string_literal: true

# Service for managing AI attribution and transparency
class AiAttributionService
  attr_reader :user, :generatable

  def initialize(user:, generatable:)
    @user = user
    @generatable = generatable
  end

  # Track AI generation for a specific content field
  def track_generation(content_type:, result:, input_prompt: nil)
    return unless result && user && generatable

    # Create the generation event record
    generation_event = AiGenerationEvent.create_from_generation_result(
      result,
      user: user,
      generatable: generatable,
      content_type: content_type,
      input_prompt: input_prompt
    )

    # Update attribution on the generatable object if it supports it
    if generatable.respond_to?(:mark_field_as_ai_generated)
      generatable.mark_field_as_ai_generated(
        content_type,
        model: result[:model_used] || result[:ai_model_name] || 'unknown',
        generation_event: generation_event,
        generated_at: generation_event.created_at
      )
    end

    generation_event
  rescue => e
    Rails.logger.error "Failed to track AI generation: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    nil
  end

  # Track manual editing of content
  def track_manual_edit(content_type:)
    return unless generatable.respond_to?(:mark_field_as_human_created)

    generatable.mark_field_as_human_created(content_type)
  end

  # Get attribution information for display
  def attribution_info(content_type = nil)
    return {} unless generatable.respond_to?(:ai_attribution_summary)

    summary = generatable.ai_attribution_summary

    if content_type
      field_data = summary[:fields][content_type.to_s] || {}
      {
        ai_generated: field_data[:ai_generated] || false,
        model: field_data[:model],
        generated_at: field_data[:generated_at],
        manually_edited_at: field_data[:manually_edited_at],
        display_info: format_attribution_display(field_data)
      }
    else
      summary.merge(
        display_summary: format_summary_display(summary)
      )
    end
  end

  # Generate visual indicators for UI
  def visual_indicators(content_type)
    info = attribution_info(content_type)
    return nil unless info[:ai_generated]

    {
      badge: {
        text: "AI Generated",
        color: "blue",
        icon: "sparkles"
      },
      model_info: {
        name: format_model_name(info[:model]),
        timestamp: info[:generated_at]
      },
      tooltip: format_tooltip_text(info)
    }
  end

  # Get detailed transparency information
  def transparency_details(content_type = nil)
    if content_type
      field_attribution_details(content_type)
    else
      full_attribution_details
    end
  end

  # Export attribution data for audit trails
  def export_attribution_data
    return nil unless generatable.respond_to?(:ai_transparency_report)

    report = generatable.ai_transparency_report
    return nil unless report

    {
      **report,
      export_metadata: {
        exported_by: user.email,
        exported_at: Time.current.iso8601,
        export_version: "1.0"
      }
    }
  end

  # Check if content has AI attribution
  def self.has_ai_attribution?(object, field = nil)
    return false unless object.respond_to?(:ai_generated_content?)
    
    object.ai_generated_content?(field)
  end

  # Bulk attribution check for multiple objects
  def self.bulk_attribution_check(objects)
    objects.map do |obj|
      {
        object: obj,
        has_ai_content: has_ai_attribution?(obj),
        ai_fields: detect_ai_fields(obj)
      }
    end
  end

  private

  def field_attribution_details(content_type)
    info = attribution_info(content_type)
    return nil unless info[:ai_generated]

    generation_event = generatable.ai_generation_event_for_field(content_type)
    
    {
      content_type: content_type,
      ai_generated: true,
      model: info[:model],
      model_display_name: format_model_name(info[:model]),
      generated_at: info[:generated_at],
      generation_event: generation_event&.generation_details,
      confidence_score: generation_event&.confidence_percentage,
      cost: generation_event&.cost_formatted,
      tokens: generation_event&.token_count,
      generation_time: generation_event&.generation_time_formatted,
      was_cached: generation_event&.was_cached?
    }
  end

  def full_attribution_details
    summary = attribution_info
    return nil unless summary[:ai_generated]

    ai_fields = summary[:fields].select { |_, data| data[:ai_generated] }
    
    {
      overall_ai_generated: true,
      ai_generated_fields: ai_fields.keys,
      field_details: ai_fields.transform_values { |data| 
        field_attribution_details(data[:content_type]) 
      }.compact,
      generation_summary: {
        total_events: generatable.ai_generation_events.count,
        total_cost: generatable.ai_generation_events.sum(:generation_cost) || 0.0,
        total_tokens: generatable.ai_generation_events.sum(:token_count) || 0,
        models_used: generatable.ai_generation_events.distinct.pluck(:ai_model_name),
        last_generation: summary[:last_ai_generation]
      }
    }
  end

  def format_attribution_display(field_data)
    return "Human created" unless field_data[:ai_generated]

    model_name = format_model_name(field_data[:model])
    timestamp = field_data[:generated_at]
    
    if timestamp
      time_ago = time_ago_in_words(Time.zone.parse(timestamp))
      "Generated by #{model_name} #{time_ago} ago"
    else
      "Generated by #{model_name}"
    end
  rescue
    "AI Generated"
  end

  def format_summary_display(summary)
    return "All content created manually" unless summary[:ai_generated]

    ai_fields = summary[:fields].select { |_, data| data[:ai_generated] }
    field_names = ai_fields.keys.map { |field| field.humanize.downcase }
    
    case field_names.length
    when 1
      "#{field_names.first.capitalize} generated by AI"
    when 2
      "#{field_names.join(' and ').capitalize} generated by AI"
    else
      "Multiple fields generated by AI"
    end
  end

  def format_model_name(model)
    return "Unknown AI Model" unless model

    case model.downcase
    when /gpt-4o/
      "GPT-4o"
    when /gpt-4/
      "GPT-4"
    when /gpt-3.5/
      "GPT-3.5 Turbo"
    when /claude-3.5-sonnet/
      "Claude 3.5 Sonnet"
    when /claude-3-haiku/
      "Claude 3 Haiku"
    when /gemini-1.5-pro/
      "Gemini 1.5 Pro"
    when /gemini-1.5-flash/
      "Gemini 1.5 Flash"
    else
      model.humanize
    end
  end

  def format_tooltip_text(info)
    model_name = format_model_name(info[:model])
    
    if info[:generated_at]
      timestamp = Time.zone.parse(info[:generated_at])
      "Generated by #{model_name} on #{timestamp.strftime('%B %d, %Y at %I:%M %p')}"
    else
      "Generated by #{model_name}"
    end
  rescue
    "AI Generated Content"
  end

  def time_ago_in_words(time)
    # Simple time ago implementation
    seconds = Time.current - time
    
    case seconds
    when 0..59
      "#{seconds.to_i} seconds"
    when 60..3599
      "#{(seconds / 60).to_i} minutes"
    when 3600..86399
      "#{(seconds / 3600).to_i} hours"
    else
      "#{(seconds / 86400).to_i} days"
    end
  end

  def self.detect_ai_fields(object)
    return [] unless object.respond_to?(:ai_attribution_summary)

    summary = object.ai_attribution_summary
    summary[:fields].select { |_, data| data[:ai_generated] }.keys
  end
end
