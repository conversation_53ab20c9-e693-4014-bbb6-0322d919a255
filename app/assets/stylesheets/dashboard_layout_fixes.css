/* Dashboard Layout Fixes */

/* Mobile Sidebar Open State */
.mobile-sidebar-open {
  transform: translateX(0) !important;
}

/* Sidebar Transform Fix */
[data-mobile-sidebar-target="sidebar"].open {
  transform: translateX(0) !important;
}

/* Status Badge Styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.completed {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.running {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.offline {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Activity Item Hover Effects */
.activity-item {
  transition: all 0.2s ease-in-out;
}

.activity-item:hover {
  background-color: #f9fafb;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Campaign Item Hover Effects */
.campaign-item {
  transition: all 0.2s ease-in-out;
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.campaign-item:hover {
  background-color: #f9fafb;
  transform: translateY(-1px);
}

/* AI Feature Card Enhancements */
.ai-feature-card {
  transition: all 0.3s ease-in-out;
  border: 1px solid transparent;
}

.ai-feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

/* Chat Interface Improvements */
.chat-input {
  transition: all 0.2s ease-in-out;
}

.chat-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.chat-send-btn {
  transition: all 0.2s ease-in-out;
}

.chat-send-btn:hover {
  transform: scale(1.05);
}

/* Responsive Grid Improvements */
@media (max-width: 768px) {
  .grid.grid-cols-1.md\:grid-cols-2 {
    gap: 1rem;
  }
  
  .dashboard-card {
    padding: 1rem;
  }
  
  .text-3xl {
    font-size: 1.875rem;
  }
}

/* Sidebar Overlay Z-index Fix */
[data-mobile-sidebar-target="overlay"] {
  z-index: 40;
}

[data-mobile-sidebar-target="sidebar"] {
  z-index: 50;
}

/* Dropdown Z-index Fix */
[data-dropdown-target="menu"] {
  z-index: 60;
}

/* Smooth Transitions for Layout Shifts */
.ml-0.lg\:ml-64 {
  transition: margin-left 0.3s ease-in-out;
}

/* Loading States */
.dashboard-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.dashboard-card.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Focus Management for Accessibility */
.dashboard-card:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Mobile Navigation Improvements */
@media (max-width: 1024px) {
  .lg\:hidden {
    display: block !important;
  }
  
  [data-mobile-sidebar-target="sidebar"] {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }
  
  [data-mobile-sidebar-target="sidebar"].open {
    transform: translateX(0);
  }
}

/* Performance Optimizations */
.dashboard-card {
  will-change: transform;
  backface-visibility: hidden;
}

/* Print Styles */
@media print {
  [data-mobile-sidebar-target="sidebar"],
  [data-mobile-sidebar-target="overlay"],
  .lg\:hidden {
    display: none !important;
  }
  
  .ml-0.lg\:ml-64 {
    margin-left: 0 !important;
  }
}