/*
 * This is a manifest file that'll be compiled into application.css.
 *
 * With Propshaft, assets are served efficiently without preprocessing steps. You can still include
 * application-wide styles in this file, but keep in mind that CSS precedence will follow the standard
 * cascading order, meaning styles declared later in the document or manifest will override earlier ones,
 * depending on specificity.
 *
 * Consider organizing styles into separate files for maintainability.
 */

/*= require sidebar */
/*= require campaigns */
/*= require email_content */
/*= require components/modern_navbar */
/*= require components/modern_dashboard */
/*= require dashboard_layout_fixes */

/* Dashboard Enhancements */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
  50% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Dashboard Card Animations */
.dashboard-card {
  animation: slide-in-up 0.6s ease-out;
}

.dashboard-card:nth-child(1) { animation-delay: 0.1s; }
.dashboard-card:nth-child(2) { animation-delay: 0.2s; }
.dashboard-card:nth-child(3) { animation-delay: 0.3s; }
.dashboard-card:nth-child(4) { animation-delay: 0.4s; }

/* Floating Icons */
.float-icon {
  animation: float 3s ease-in-out infinite;
}

/* Pulse Animation for Live Indicators */
.pulse-indicator {
  animation: pulse-glow 2s infinite;
}

/* Gradient Background Animation */
.animated-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

/* Hover Effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Progress Bar Animation */
.progress-bar {
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .dashboard-card {
    animation-delay: 0s;
  }
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Social Media Platform Brand Colors */
.bg-facebook { background-color: #1877F2; }
.bg-facebook-dark { background-color: #166FE5; }
.border-facebook { border-color: #1877F2; }
.text-facebook { color: #1877F2; }

.bg-twitter { background-color: #000000; }
.bg-twitter-dark { background-color: #1a1a1a; }
.border-twitter { border-color: #000000; }
.text-twitter { color: #000000; }

.bg-instagram { background-color: #E4405F; }
.bg-instagram-dark { background-color: #D73652; }
.border-instagram { border-color: #E4405F; }
.text-instagram { color: #E4405F; }

.bg-linkedin { background-color: #0A66C2; }
.bg-linkedin-dark { background-color: #004182; }
.border-linkedin { border-color: #0A66C2; }
.text-linkedin { color: #0A66C2; }

.bg-youtube { background-color: #FF0000; }
.bg-youtube-dark { background-color: #CC0000; }
.border-youtube { border-color: #FF0000; }
.text-youtube { color: #FF0000; }

.bg-tiktok { background-color: #000000; }
.bg-tiktok-dark { background-color: #1a1a1a; }
.border-tiktok { border-color: #000000; }
.text-tiktok { color: #000000; }

/* Instagram Gradient for Special Cases */
.bg-instagram-gradient {
  background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
}

/* Platform Icon Containers */
.platform-icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem; /* rounded-xl */
  padding: 0.75rem; /* p-3 */
  transition: all 0.2s ease-in-out;
}

.platform-icon-container:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Platform-specific focus rings for accessibility */
.focus\:ring-facebook:focus {
  --tw-ring-color: #1877F2;
}

.focus\:ring-twitter:focus {
  --tw-ring-color: #000000;
}

.focus\:ring-instagram:focus {
  --tw-ring-color: #E4405F;
}

.focus\:ring-linkedin:focus {
  --tw-ring-color: #0A66C2;
}

.focus\:ring-youtube:focus {
  --tw-ring-color: #FF0000;
}

.focus\:ring-tiktok:focus {
  --tw-ring-color: #000000;
}

@import "ai_agents.css";
