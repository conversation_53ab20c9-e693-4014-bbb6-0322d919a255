/* Enhanced Campaigns Page Styles */

/* Campaign Action Buttons */
.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-success {
  background-color: #059669;
  color: white;
}

.btn-success:hover {
  background-color: #047857;
}

.btn-warning {
  background-color: #d97706;
  color: white;
}

.btn-warning:hover {
  background-color: #b45309;
}

/* Button form styling for button_to helpers */
form[action*="/activate"],
form[action*="/pause"],
form[action*="/complete"] {
  display: inline-block;
  width: 100%;
}

form[action*="/activate"] input[type="submit"],
form[action*="/pause"] input[type="submit"],
form[action*="/complete"] input[type="submit"] {
  width: 100%;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Animated Background */
.campaigns-bg-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05), rgba(236, 72, 153, 0.05));
  animation: campaignsGradientShift 15s ease infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes campaignsGradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Header Card Styles */
.campaigns-header-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: campaignsFadeInUp 0.8s ease-out;
}

.campaigns-icon-wrapper {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 12px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Button Styles */
.campaigns-create-btn {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  position: relative;
  overflow: hidden;
}

.campaigns-create-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.campaigns-create-btn:hover::before {
  left: 100%;
}

.campaigns-analytics-btn {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

/* Stats Grid Styles */
.campaigns-stats-grid {
  animation: campaignsFadeInUp 0.8s ease-out 0.2s both;
}

.campaigns-stat-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.campaigns-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.campaigns-stat-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  transition: transform 0.3s ease;
}

.campaigns-stat-card:hover .campaigns-stat-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
}

.campaigns-stat-number {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
}

.campaigns-stat-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 8px;
}

.campaigns-stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Filters Card Styles */
.campaigns-filters-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: campaignsFadeInUp 0.8s ease-out 0.4s both;
}

.campaigns-search-wrapper {
  position: relative;
}

.campaigns-search-input {
  background: rgba(248, 250, 252, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  transition: all 0.3s ease;
}

.campaigns-search-input:focus {
  background: rgba(255, 255, 255, 0.9);
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.campaigns-filter-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.campaigns-filter-select {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.campaigns-filter-select:focus {
  background: rgba(255, 255, 255, 0.9);
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Quick Filter Pills */
.campaigns-quick-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
}

.campaigns-filter-pill {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(229, 231, 235, 0.6);
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
}

.campaigns-filter-pill:hover,
.campaigns-filter-pill.active {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border-color: transparent;
  transform: translateY(-1px);
}

/* View Toggle Styles */
.campaigns-view-toggle {
  display: flex;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 4px;
  border: 1px solid rgba(229, 231, 235, 0.6);
}

.campaigns-view-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 6px;
}

.campaigns-view-btn:hover {
  color: #374151;
  background: rgba(243, 244, 246, 0.8);
}

.campaigns-view-btn.active {
  color: #2563eb;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.campaigns-view-btn svg {
  transition: transform 0.2s ease;
}

.campaigns-view-btn:hover svg {
  transform: scale(1.05);
}

/* Campaign View Containers */
.campaign-view {
  transition: opacity 0.3s ease-in-out;
}

.campaign-view:not(.active) {
  display: none;
}

.campaign-view.active {
  display: block;
  animation: campaignsFadeInUp 0.4s ease-out;
}

.campaigns-view-toggle button {
  padding: 8px 16px;
  border-radius: 8px;
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  font-weight: 500;
}

.campaigns-view-toggle button.active {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Campaign Grid Styles */
.campaigns-grid {
  animation: campaignsFadeInUp 0.8s ease-out 0.6s both;
}

.campaigns-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.campaigns-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.campaigns-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.campaigns-card:hover::before {
  opacity: 1;
}

/* Campaign Type Badges */
.campaigns-type-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.campaigns-type-badge.email {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
}

.campaigns-type-badge.social {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: white;
}

.campaigns-type-badge.seo {
  background: linear-gradient(135deg, #8b5cf6, #a78bfa);
  color: white;
}

.campaigns-type-badge.multi_channel {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
  color: white;
}

/* Status Indicators */
.campaigns-status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.campaigns-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.campaigns-status-indicator.active .campaigns-status-dot {
  background: #10b981;
  animation: campaignsPulse 2s infinite;
}

.campaigns-status-indicator.draft .campaigns-status-dot {
  background: #3b82f6;
}

.campaigns-status-indicator.paused .campaigns-status-dot {
  background: #f59e0b;
}

.campaigns-status-indicator.completed .campaigns-status-dot {
  background: #8b5cf6;
}

.campaigns-status-indicator.cancelled .campaigns-status-dot {
  background: #ef4444;
}

/* Progress Bars */
.campaigns-progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(229, 231, 235, 0.6);
  border-radius: 4px;
  overflow: hidden;
  margin: 12px 0;
}

.campaigns-progress-fill {
  height: 100%;
  border-radius: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.8s ease;
  position: relative;
}

.campaigns-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: campaignsShimmer 2s infinite;
}

/* Action Buttons */
.campaigns-action-dropdown {
  position: relative;
  display: inline-block;
}

.campaigns-action-btn {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  border-radius: 8px;
  padding: 8px 12px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.campaigns-action-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: #3b82f6;
  color: #3b82f6;
}

.campaigns-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.3s ease;
}

.campaigns-dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.campaigns-dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: #374151;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
}

.campaigns-dropdown-item:last-child {
  border-bottom: none;
}

.campaigns-dropdown-item:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

/* Table View Styles */
.campaigns-table {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.campaigns-table-header {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);
}

.campaigns-table-header th {
  padding: 16px 20px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  text-align: left;
}

.campaigns-table-body tr {
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  transition: all 0.3s ease;
}

.campaigns-table-body tr:hover {
  background: rgba(59, 130, 246, 0.05);
}

.campaigns-table-body td {
  padding: 16px 20px;
  font-size: 0.875rem;
  color: #374151;
}

/* Empty State Styles */
.campaigns-empty-state {
  animation: campaignsFadeInUp 0.8s ease-out 0.6s both;
}

.campaigns-empty-icon {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  border: 2px solid rgba(59, 130, 246, 0.2);
  animation: campaignsFloat 3s ease-in-out infinite;
}

.campaigns-quick-start-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin: 48px 0;
}

.campaigns-quick-start-card {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
}

.campaigns-quick-start-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
}

.campaigns-create-primary-btn {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  position: relative;
  overflow: hidden;
}

.campaigns-create-primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.campaigns-create-primary-btn:hover::before {
  left: 100%;
}

.campaigns-create-secondary-btn,
.campaigns-tour-btn {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.6);
}

.campaigns-clear-filters-btn {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
}

/* Mobile View Specific Styles */
.campaigns-mobile-view {
  display: none;
}

.campaigns-mobile-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(229, 231, 235, 0.6);
  color: #6b7280;
  text-decoration: none;
  transition: all 0.2s ease;
}

.campaigns-mobile-action-btn:hover {
  background: rgba(243, 244, 246, 0.9);
  border-color: #3b82f6;
  color: #3b82f6;
}

.campaigns-mobile-status-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
  .campaigns-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .campaigns-filters-grid {
    grid-template-columns: 1fr;
  }

  .campaigns-grid {
    grid-template-columns: 1fr;
  }

  .campaigns-quick-start-grid {
    grid-template-columns: 1fr;
  }

  /* Hide desktop views on mobile */
  .campaigns-grid-view,
  .campaigns-table-view {
    display: none !important;
  }

  /* Show mobile view on mobile */
  .campaigns-mobile-view {
    display: block !important;
  }

  /* Adjust view toggle for mobile */
  .campaigns-view-btn:not([data-view="mobile"]) {
    display: none;
  }
}

@media (min-width: 769px) {
  /* Hide mobile view on desktop */
  .campaigns-mobile-view {
    display: none !important;
  }

  /* Hide mobile view toggle on desktop */
  .campaigns-view-btn[data-view="mobile"] {
    display: none;
  }
}

/* Animations */
@keyframes campaignsFadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes campaignsPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes campaignsShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes campaignsFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Loading States */
.campaigns-loading {
  position: relative;
  overflow: hidden;
}

.campaigns-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: campaignsShimmer 1.5s infinite;
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .campaigns-bg-overlay {
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1), rgba(236, 72, 153, 0.1));
  }
  
  .campaigns-header-card,
  .campaigns-filters-card,
  .campaigns-stat-card,
  .campaigns-card {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(75, 85, 99, 0.3);
  }
  
  .campaigns-search-input,
  .campaigns-filter-select {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(75, 85, 99, 0.3);
    color: #f9fafb;
  }
}
