/* AI Models Enhanced Styling */

/* Compact AI Models Component */
.ai-models-compact {
  max-width: 100%;
}

.ai-models-compact .model-item {
  transition: all 0.2s ease-in-out;
}

.ai-models-compact .model-item:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

.ai-models-compact .status-dot {
  position: relative;
}

.ai-models-compact .status-dot.available {
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Sidebar specific styling */
.sidebar .ai-models-compact {
  margin-bottom: 1.5rem;
}

.sidebar .ai-models-compact .model-item {
  padding: 0.75rem;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
}

.sidebar .ai-models-compact .model-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

.sidebar .ai-models-compact .model-cost {
  font-size: 0.75rem;
  color: #6b7280;
}

/* AI Attribution Components */
.ai-attribution-badge {
  transition: all 0.2s ease-in-out;
}

.ai-attribution-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ai-attribution-inline {
  transition: all 0.2s ease-in-out;
}

.ai-attribution-inline:hover {
  color: #7c3aed;
}

.ai-attribution-detailed {
  transition: all 0.3s ease-in-out;
}

.ai-attribution-detailed:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.ai-attribution-panel {
  transition: all 0.3s ease-in-out;
}

.ai-attribution-panel .ai-attribution-details {
  transition: all 0.3s ease-in-out;
}

.ai-attribution-panel .ai-attribution-details.hidden {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
}

.ai-attribution-panel .ai-attribution-details:not(.hidden) {
  opacity: 1;
  max-height: 1000px;
}

/* AI Attribution Modal */
.ai-attribution-modal {
  backdrop-filter: blur(4px);
}

.ai-attribution-modal .modal-content {
  max-height: 90vh;
  overflow-y: auto;
}

/* AI Attribution Status Dots */
.ai-status-dot {
  position: relative;
}

.ai-status-dot.available::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: inherit;
  animation: ai-pulse 2s infinite;
}

@keyframes ai-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* AI Attribution Tooltips */
.ai-attribution-tooltip {
  position: relative;
}

.ai-attribution-tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 50;
  margin-bottom: 0.25rem;
}

.ai-attribution-tooltip:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: #1f2937;
  z-index: 50;
}

/* Model Card Animations */
.ai-model-card {
  transition: all 0.2s ease-in-out;
}

.ai-model-card:hover {
  transform: translateY(-2px);
}

.ai-model-card.selected-model {
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.15), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

.ai-model-card.featured-model {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid transparent;
  background-clip: padding-box;
}

.ai-model-card.featured-model::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  z-index: -1;
}

/* Status Indicators */
.status-indicator {
  position: relative;
}

.status-indicator.available::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: inherit;
  animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Performance Indicators */
.performance-dots {
  display: flex;
  gap: 2px;
}

.performance-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.performance-dot.active {
  transform: scale(1.2);
  box-shadow: 0 0 8px currentColor;
}

/* Capability Badges */
.capability-badge {
  transition: all 0.2s ease;
}

.capability-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Provider Icons */
.provider-icon {
  position: relative;
  overflow: hidden;
}

.provider-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.provider-icon:hover::before {
  animation: shine 0.6s ease-in-out;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0;
  }
}

/* View Toggle Buttons */
.view-toggle-btn {
  position: relative;
  overflow: hidden;
}

.view-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.view-toggle-btn:hover::before {
  left: 100%;
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Notification Enhancements */
.notification-slide-in {
  animation: slideInRight 0.3s ease-out;
}

.notification-slide-out {
  animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Responsive Enhancements */
@media (max-width: 640px) {
  .ai-model-card {
    margin-bottom: 1rem;
  }
  
  .ai-model-card .grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .provider-icon {
    width: 2rem;
    height: 2rem;
  }
  
  .capability-badge {
    font-size: 0.625rem;
    padding: 0.125rem 0.375rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .ai-model-card {
    background: #1f2937;
    border-color: #374151;
  }
  
  .ai-model-card.featured-model {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }
  
  .loading-shimmer {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }
}

/* Accessibility Enhancements */
.ai-model-card:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.ai-model-card[aria-selected="true"] {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .ai-model-card {
    border-width: 2px;
  }
  
  .status-indicator {
    border: 2px solid currentColor;
  }
  
  .capability-badge {
    border: 1px solid currentColor;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .ai-model-card,
  .capability-badge,
  .provider-icon,
  .view-toggle-btn {
    transition: none;
  }
  
  .status-indicator.available::after {
    animation: none;
  }
  
  .loading-shimmer {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .ai-model-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .status-indicator {
    background: #000 !important;
  }
  
  .capability-badge {
    border: 1px solid #000;
    background: transparent !important;
    color: #000 !important;
  }
}
