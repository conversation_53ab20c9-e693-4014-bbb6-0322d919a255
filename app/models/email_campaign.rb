# frozen_string_literal: true

# == Schema Information
# Table name: email_campaigns
#
#  id           :bigint           not null, primary key
#  campaign_id  :bigint           not null
#  subject_line :string(150)      not null
#  preview_text :string(200)
#  content      :text             not null
#  from_name    :string           not null
#  from_email   :string           not null
#  settings     :jsonb            default({}), not null
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
# Indexes
#
#  index_email_campaigns_on_campaign_id  (campaign_id) UNIQUE
#  index_email_campaigns_on_from_email   (from_email)
#  index_email_campaigns_on_settings     (settings) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (campaign_id => campaigns.id)
#

class EmailCampaign < ApplicationRecord
  # Associations
  belongs_to :campaign, required: true
  has_many :ai_generation_events, as: :generatable, dependent: :destroy

  # File attachments using Active Storage
  has_many_attached :images do |attachable|
    attachable.variant :thumbnail, resize_to_limit: [150, 150]
    attachable.variant :medium, resize_to_limit: [400, 400]
    attachable.variant :large, resize_to_limit: [800, 600]
  end

  has_many_attached :documents
  has_many_attached :audio_files
  has_many_attached :video_files

  # Validations
  validates :subject_line, presence: true, length: { maximum: 150 }
  validates :content, presence: true
  validates :from_name, presence: true
  validates :from_email, presence: true, format: {
    with: URI::MailTo::EMAIL_REGEXP,
    message: "must be a valid email address"
  }
  validates :preview_text, length: { maximum: 200 }, allow_blank: true

  # Custom validation methods for file attachments
  validate :validate_images
  validate :validate_documents
  validate :validate_audio_files
  validate :validate_video_files

  # Scopes
  scope :ready_to_send, -> {
    joins(:campaign)
      .where(campaigns: { status: "active" })
      .where.not(subject_line: [ nil, "" ])
      .where.not(content: [ nil, "" ])
      .where.not(from_email: [ nil, "" ])
  }

  # Delegate tenant access through campaign
  def tenant
    campaign.tenant
  end

  # Instance methods
  def ready_to_send?
    valid? &&
    campaign&.active? &&
    subject_line.present? &&
    content.present? &&
    from_email.present? &&
    valid_email_format?
  end

  def preview_snippet(length = 100)
    return "" if content.blank?

    if content.length <= length
      content
    else
      content[0..length-1] + "..."
    end
  end

  def estimated_send_time
    recipient_count = settings.dig("recipient_count") || 0
    send_rate_per_minute = 1000 # emails per minute

    minutes = (recipient_count.to_f / send_rate_per_minute).ceil
    [ minutes, 1 ].max # minimum 1 minute
  end

  def personalize_content_for(user_data = {})
    personalized_content = content.dup

    user_data.each do |key, value|
      personalized_content.gsub!("{{#{key}}}", value.to_s)
    end

    # Remove any remaining merge tags
    personalized_content.gsub!(/\{\{[^}]+\}\}/, "")

    personalized_content
  end

  def recipient_count
    settings.dig("recipient_count") || 0
  end

  def a_b_test_enabled?
    settings.dig("a_b_test", "enabled") == true
  end

  def scheduled?
    settings.dig("delivery_options", "scheduled_at").present?
  end

  def scheduled_at
    scheduled_time = settings.dig("delivery_options", "scheduled_at")
    return nil if scheduled_time.blank?

    Time.zone.parse(scheduled_time)
  rescue ArgumentError
    nil
  end

  # Advanced A/B Testing Methods
  def a_b_test_variants
    return [] unless a_b_test_enabled?

    settings.dig("a_b_test", "variants") || []
  end

  def a_b_test_split_percentage
    return 0 unless a_b_test_enabled?

    settings.dig("a_b_test", "split_percentage") || 50
  end

  def a_b_test_winner
    return nil unless a_b_test_enabled?

    settings.dig("a_b_test", "winner")
  end

  # AI Attribution Methods
  def ai_generated_content?(field = nil)
    return false if ai_attribution.blank?

    if field
      ai_attribution.dig(field.to_s, 'ai_generated') == true
    else
      # Check if any field is AI generated
      %w[subject_line preview_text content].any? do |content_field|
        ai_attribution.dig(content_field, 'ai_generated') == true
      end
    end
  end

  def ai_model_for_field(field)
    ai_attribution.dig(field.to_s, 'model')
  end

  def ai_generation_timestamp(field)
    timestamp = ai_attribution.dig(field.to_s, 'generated_at')
    return nil unless timestamp

    Time.zone.parse(timestamp)
  rescue ArgumentError
    nil
  end

  def ai_generation_event_for_field(field)
    event_id = ai_attribution.dig(field.to_s, 'generation_event_id')
    return nil unless event_id

    ai_generation_events.find_by(id: event_id)
  end

  def mark_field_as_ai_generated(field, model:, generation_event: nil, generated_at: Time.current)
    self.ai_attribution ||= {}
    self.ai_attribution[field.to_s] = {
      'ai_generated' => true,
      'model' => model,
      'generated_at' => generated_at.iso8601,
      'generation_event_id' => generation_event&.id
    }.compact
    save! if persisted?
  end

  def mark_field_as_human_created(field)
    self.ai_attribution ||= {}
    self.ai_attribution[field.to_s] = {
      'ai_generated' => false,
      'manually_edited_at' => Time.current.iso8601
    }
    save! if persisted?
  end

  def ai_attribution_summary
    return { ai_generated: false, fields: {} } if ai_attribution.blank?

    fields = {}
    ai_generated_any = false

    %w[subject_line preview_text content].each do |field|
      field_data = ai_attribution[field] || {}
      is_ai_generated = field_data['ai_generated'] == true
      ai_generated_any ||= is_ai_generated

      fields[field] = {
        ai_generated: is_ai_generated,
        model: field_data['model'],
        generated_at: field_data['generated_at'],
        manually_edited_at: field_data['manually_edited_at']
      }.compact
    end

    {
      ai_generated: ai_generated_any,
      fields: fields,
      last_ai_generation: ai_generation_events.recent.first&.created_at
    }
  end

  def ai_transparency_report
    summary = ai_attribution_summary
    return nil unless summary[:ai_generated]

    {
      campaign_name: campaign.name,
      email_campaign_id: id,
      ai_generated_fields: summary[:fields].select { |_, data| data[:ai_generated] },
      generation_events: ai_generation_events.recent.limit(10).map(&:generation_details),
      total_ai_cost: ai_generation_events.sum(:generation_cost) || 0.0,
      total_tokens: ai_generation_events.sum(:token_count) || 0,
      generated_at: Time.current.iso8601
    }
  end

  def a_b_test_results
    return {} unless a_b_test_enabled?

    variants = a_b_test_variants
    return {} if variants.empty?

    results = {}
    variants.each do |variant|
      variant_metrics = get_variant_metrics(variant["name"])
      results[variant["name"]] = {
        subject: variant["subject_line"],
        sent_count: variant_metrics[:sent],
        open_rate: variant_metrics[:open_rate],
        click_rate: variant_metrics[:click_rate],
        conversion_rate: variant_metrics[:conversion_rate],
        statistical_significance: calculate_statistical_significance(variant["name"])
      }
    end

    results
  end

  def declare_a_b_winner(variant_name, confidence_level = 95)
    return false unless a_b_test_enabled?
    return false unless a_b_test_variants.any? { |v| v["name"] == variant_name }

    # Update settings with winner
    new_settings = settings.dup
    new_settings["a_b_test"]["winner"] = variant_name
    new_settings["a_b_test"]["winner_declared_at"] = Time.current.iso8601
    new_settings["a_b_test"]["confidence_level"] = confidence_level

    update(settings: new_settings)
  end

  # Deliverability Analytics
  def deliverability_score
    total_sent = campaign.campaign_metrics.sum { |m| m.custom_metrics.dig("email", "total_sent") || 0 }
    total_bounces = campaign.campaign_metrics.sum(:email_bounces)
    spam_complaints = campaign.campaign_metrics.sum { |m| m.custom_metrics.dig("email", "spam_complaints") || 0 }

    return 100.0 if total_sent.zero?

    delivery_rate = ((total_sent - total_bounces - spam_complaints).to_f / total_sent * 100)
    [ delivery_rate, 0.0 ].max.round(2)
  end

  def bounce_analysis
    metrics = campaign.campaign_metrics.includes(:campaign)
    total_sent = metrics.sum { |m| m.custom_metrics.dig("email", "total_sent") || 0 }
    hard_bounces = metrics.sum { |m| m.custom_metrics.dig("email", "hard_bounces") || 0 }
    soft_bounces = metrics.sum { |m| m.custom_metrics.dig("email", "soft_bounces") || 0 }

    {
      total_bounces: hard_bounces + soft_bounces,
      hard_bounces: hard_bounces,
      soft_bounces: soft_bounces,
      hard_bounce_rate: total_sent > 0 ? (hard_bounces.to_f / total_sent * 100).round(2) : 0.0,
      soft_bounce_rate: total_sent > 0 ? (soft_bounces.to_f / total_sent * 100).round(2) : 0.0,
      bounce_categories: categorize_bounces
    }
  end

  def spam_analysis
    metrics = campaign.campaign_metrics.includes(:campaign)
    total_sent = metrics.sum { |m| m.custom_metrics.dig("email", "total_sent") || 0 }
    spam_complaints = metrics.sum { |m| m.custom_metrics.dig("email", "spam_complaints") || 0 }
    unsubscribes = metrics.sum { |m| m.custom_metrics.dig("email", "unsubscribes") || 0 }

    {
      spam_complaints: spam_complaints,
      spam_rate: total_sent > 0 ? (spam_complaints.to_f / total_sent * 100).round(4) : 0.0,
      unsubscribes: unsubscribes,
      unsubscribe_rate: total_sent > 0 ? (unsubscribes.to_f / total_sent * 100).round(2) : 0.0,
      list_hygiene_score: calculate_list_hygiene_score
    }
  end

  def inbox_placement_analysis
    metrics = campaign.campaign_metrics.includes(:campaign)
    total_delivered = metrics.sum { |m|
      total_sent = m.custom_metrics.dig("email", "total_sent") || 0
      bounces = m.email_bounces || 0
      total_sent - bounces
    }

    inbox_delivered = metrics.sum { |m| m.custom_metrics.dig("email", "inbox_delivered") || 0 }
    spam_folder = metrics.sum { |m| m.custom_metrics.dig("email", "spam_folder") || 0 }

    {
      inbox_rate: total_delivered > 0 ? (inbox_delivered.to_f / total_delivered * 100).round(2) : 0.0,
      spam_folder_rate: total_delivered > 0 ? (spam_folder.to_f / total_delivered * 100).round(2) : 0.0,
      total_delivered: total_delivered,
      inbox_delivered: inbox_delivered,
      spam_folder: spam_folder
    }
  end

  # Email Performance Optimization
  def optimal_send_time_analysis
    metrics = campaign.campaign_metrics
                     .joins("LEFT JOIN email_sends ON email_sends.campaign_metric_id = campaign_metrics.id")
                     .group("EXTRACT(hour FROM email_sends.sent_at)")
                     .average(:email_opens)

    return {} if metrics.empty?

    best_hour = metrics.max_by { |hour, opens| opens }&.first

    {
      hourly_performance: metrics,
      recommended_send_hour: best_hour,
      performance_variance: calculate_time_variance(metrics)
    }
  end

  def device_engagement_analysis
    device_data = campaign.campaign_metrics.sum { |m| m.custom_metrics.dig("email", "device_breakdown") || {} }

    return {} if device_data.empty?

    total_opens = device_data.values.sum

    device_data.transform_values do |opens|
      {
        opens: opens,
        percentage: total_opens > 0 ? (opens.to_f / total_opens * 100).round(2) : 0.0
      }
    end
  end

  def content_engagement_heatmap
    metrics = campaign.campaign_metrics
    link_data = metrics.map { |m| m.custom_metrics.dig("email", "link_clicks") || {} }
                      .reduce({}) { |acc, links| acc.merge(links) { |k, v1, v2| v1 + v2 } }

    total_clicks = link_data.values.sum

    link_data.transform_values do |clicks|
      {
        clicks: clicks,
        click_percentage: total_clicks > 0 ? (clicks.to_f / total_clicks * 100).round(2) : 0.0
      }
    end
  end

  # File attachment helper methods
  def has_attachments?
    images.attached? || documents.attached? || audio_files.attached? || video_files.attached?
  end

  def total_attachment_count
    images.count + documents.count + audio_files.count + video_files.count
  end

  def total_attachment_size
    all_attachments = []
    all_attachments += images.blobs if images.attached?
    all_attachments += documents.blobs if documents.attached?
    all_attachments += audio_files.blobs if audio_files.attached?
    all_attachments += video_files.blobs if video_files.attached?

    all_attachments.sum(&:byte_size)
  end

  def attachment_summary
    {
      images: images.count,
      documents: documents.count,
      audio_files: audio_files.count,
      video_files: video_files.count,
      total_count: total_attachment_count,
      total_size: total_attachment_size,
      total_size_mb: (total_attachment_size / 1.megabyte).round(2)
    }
  end

  def get_file_type_icon(content_type)
    case content_type
    when /^image\//
      'photo'
    when /^video\//
      'video-camera'
    when /^audio\//
      'musical-note'
    when 'application/pdf'
      'document-text'
    when /word|doc/
      'document-text'
    when 'text/plain'
      'document-text'
    else
      'document'
    end
  end

  private

  def valid_email_format?
    return false if from_email.blank?
    from_email.match?(URI::MailTo::EMAIL_REGEXP)
  end

  # File attachment validation methods
  def validate_images
    return unless images.attached?

    images.each do |image|
      validate_file_content_type(image, %w[image/jpeg image/jpg image/png image/gif image/webp], 'must be a JPEG, PNG, GIF, or WebP image')
      validate_file_size(image, 10.megabytes, 'must be less than 10MB')
    end
  end

  def validate_documents
    return unless documents.attached?

    documents.each do |document|
      validate_file_content_type(document, %w[application/pdf application/msword application/vnd.openxmlformats-officedocument.wordprocessingml.document text/plain], 'must be a PDF, DOC, DOCX, or TXT file')
      validate_file_size(document, 25.megabytes, 'must be less than 25MB')
    end
  end

  def validate_audio_files
    return unless audio_files.attached?

    audio_files.each do |audio|
      validate_file_content_type(audio, %w[audio/mpeg audio/wav audio/mp4 audio/x-m4a], 'must be an MP3, WAV, or M4A file')
      validate_file_size(audio, 50.megabytes, 'must be less than 50MB')
    end
  end

  def validate_video_files
    return unless video_files.attached?

    video_files.each do |video|
      validate_file_content_type(video, %w[video/mp4 video/quicktime video/x-msvideo], 'must be an MP4, MOV, or AVI file')
      validate_file_size(video, 100.megabytes, 'must be less than 100MB')
    end
  end

  def validate_file_content_type(file, allowed_types, error_message)
    unless allowed_types.include?(file.content_type)
      errors.add(:base, "#{file.filename} #{error_message}")
    end
  end

  def validate_file_size(file, max_size, error_message)
    if file.byte_size > max_size
      errors.add(:base, "#{file.filename} #{error_message}")
    end
  end

  def get_variant_metrics(variant_name)
    metrics = campaign.campaign_metrics.includes(:campaign)

    variant_data = metrics.map { |m|
      m.custom_metrics.dig("a_b_test", "variants", variant_name) || {}
    }.reduce({}) { |acc, data| acc.merge(data) { |k, v1, v2| v1 + v2 } }

    {
      sent: variant_data["sent"] || 0,
      open_rate: variant_data["sent"] > 0 ? (variant_data["opens"].to_f / variant_data["sent"] * 100).round(2) : 0.0,
      click_rate: variant_data["sent"] > 0 ? (variant_data["clicks"].to_f / variant_data["sent"] * 100).round(2) : 0.0,
      conversion_rate: variant_data["sent"] > 0 ? (variant_data["conversions"].to_f / variant_data["sent"] * 100).round(2) : 0.0
    }
  end

  def calculate_statistical_significance(variant_name)
    return 0.0 unless a_b_test_variants.count >= 2

    control_variant = a_b_test_variants.first
    test_variant = a_b_test_variants.find { |v| v["name"] == variant_name }

    return 0.0 if control_variant.nil? || test_variant.nil?

    control_metrics = get_variant_metrics(control_variant["name"])
    test_metrics = get_variant_metrics(test_variant["name"])

    # Simple Z-test for conversion rate difference
    n1, n2 = control_metrics[:sent], test_metrics[:sent]
    p1 = control_metrics[:conversion_rate] / 100.0
    p2 = test_metrics[:conversion_rate] / 100.0

    return 0.0 if n1 == 0 || n2 == 0

    p_pool = ((p1 * n1) + (p2 * n2)) / (n1 + n2)
    se = Math.sqrt(p_pool * (1 - p_pool) * ((1.0/n1) + (1.0/n2)))

    return 0.0 if se == 0

    z_score = (p2 - p1).abs / se
    # Convert to confidence percentage (simplified)
    confidence = [ 95.0, z_score * 10 ].min
    confidence.round(2)
  end

  def categorize_bounces
    metrics = campaign.campaign_metrics.includes(:campaign)
    bounce_reasons = metrics.map { |m|
      m.custom_metrics.dig("email", "bounce_reasons") || {}
    }.reduce({}) { |acc, reasons| acc.merge(reasons) { |k, v1, v2| v1 + v2 } }

    {
      "invalid_email" => bounce_reasons["invalid_email"] || 0,
      "mailbox_full" => bounce_reasons["mailbox_full"] || 0,
      "server_error" => bounce_reasons["server_error"] || 0,
      "content_rejected" => bounce_reasons["content_rejected"] || 0,
      "other" => bounce_reasons["other"] || 0
    }
  end

  def calculate_list_hygiene_score
    total_sent = campaign.campaign_metrics.sum { |m| m.custom_metrics.dig("email", "total_sent") || 0 }
    return 100.0 if total_sent.zero?

    bounce_analysis_data = bounce_analysis
    spam_data = spam_analysis

    # Scoring factors (lower is better)
    hard_bounce_penalty = bounce_analysis_data[:hard_bounce_rate] * 2
    spam_penalty = spam_data[:spam_rate] * 10
    unsubscribe_penalty = spam_data[:unsubscribe_rate] * 0.5

    total_penalty = hard_bounce_penalty + spam_penalty + unsubscribe_penalty
    score = [ 100.0 - total_penalty, 0.0 ].max

    score.round(2)
  end

  def calculate_time_variance(hourly_metrics)
    return 0.0 if hourly_metrics.empty?

    values = hourly_metrics.values
    mean = values.sum.to_f / values.length
    variance = values.sum { |v| (v - mean) ** 2 } / values.length

    Math.sqrt(variance).round(2)
  end
end
