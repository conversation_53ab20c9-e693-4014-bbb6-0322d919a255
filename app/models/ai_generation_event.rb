# frozen_string_literal: true

# == Schema Information
# Table name: ai_generation_events
#
#  id                    :bigint           not null, primary key
#  tenant_id             :bigint           not null
#  user_id               :bigint           not null
#  generatable_type      :string           not null
#  generatable_id        :bigint           not null
#  content_type          :string           not null (subject_line, preview_text, content, full_email)
#  ai_model_name         :string           not null
#  ai_provider           :string           not null
#  generation_parameters :jsonb            default({}), not null
#  input_prompt          :text
#  generated_content     :text             not null
#  confidence_score      :decimal(5,4)
#  token_count           :integer
#  generation_cost       :decimal(10,6)
#  generation_time_ms    :integer
#  metadata              :jsonb            default({}), not null
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#
# Indexes
#
#  index_ai_generation_events_on_tenant_id                    (tenant_id)
#  index_ai_generation_events_on_user_id                      (user_id)
#  index_ai_generation_events_on_generatable                  (generatable_type,generatable_id)
#  index_ai_generation_events_on_content_type                 (content_type)
#  index_ai_generation_events_on_ai_model_name                (ai_model_name)
#  index_ai_generation_events_on_created_at                   (created_at)
#
# Foreign Keys
#
#  fk_rails_...  (tenant_id => tenants.id)
#  fk_rails_...  (user_id => users.id)
#

class AiGenerationEvent < ApplicationRecord
  # Associations
  belongs_to :tenant
  belongs_to :user
  belongs_to :generatable, polymorphic: true

  # Validations
  validates :content_type, presence: true, inclusion: { 
    in: %w[subject_line preview_text content full_email],
    message: "must be a valid content type"
  }
  validates :ai_model_name, presence: true
  validates :ai_provider, presence: true
  validates :generated_content, presence: true
  validates :confidence_score, numericality: { 
    greater_than_or_equal_to: 0.0, 
    less_than_or_equal_to: 1.0 
  }, allow_nil: true
  validates :token_count, numericality: { greater_than: 0 }, allow_nil: true
  validates :generation_cost, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :generation_time_ms, numericality: { greater_than: 0 }, allow_nil: true

  # Scopes
  scope :recent, -> { order(created_at: :desc) }
  scope :by_content_type, ->(type) { where(content_type: type) }
  scope :by_model, ->(model) { where(ai_model_name: model) }
  scope :by_provider, ->(provider) { where(ai_provider: provider) }
  scope :for_campaign, ->(campaign) { where(generatable: campaign) }
  scope :for_email_campaign, ->(email_campaign) { where(generatable: email_campaign) }
  scope :this_month, -> { where(created_at: Time.current.beginning_of_month..Time.current.end_of_month) }
  scope :with_cost, -> { where.not(generation_cost: nil) }

  # Class methods
  def self.total_cost_for_tenant(tenant, period = 1.month)
    where(tenant: tenant)
      .where(created_at: period.ago..Time.current)
      .sum(:generation_cost) || 0.0
  end

  def self.usage_stats_for_tenant(tenant, period = 1.month)
    events = where(tenant: tenant).where(created_at: period.ago..Time.current)
    
    {
      total_generations: events.count,
      total_cost: events.sum(:generation_cost) || 0.0,
      total_tokens: events.sum(:token_count) || 0,
      by_model: events.group(:ai_model_name).count,
      by_content_type: events.group(:content_type).count,
      by_provider: events.group(:ai_provider).count,
      average_confidence: events.where.not(confidence_score: nil).average(:confidence_score)&.round(4),
      average_generation_time: events.where.not(generation_time_ms: nil).average(:generation_time_ms)&.round(2)
    }
  end

  def self.create_from_generation_result(result, user:, generatable:, content_type:, input_prompt: nil)
    create!(
      tenant: user.tenant,
      user: user,
      generatable: generatable,
      content_type: content_type,
      ai_model_name: result[:model_used] || result[:ai_model_name] || 'unknown',
      ai_provider: extract_provider_from_model(result[:model_used] || result[:ai_model_name]),
      generation_parameters: result[:generation_parameters] || {},
      input_prompt: input_prompt,
      generated_content: extract_generated_content(result, content_type),
      confidence_score: result[:confidence_score],
      token_count: result[:token_count] || result.dig(:usage, :total_tokens),
      generation_cost: result[:generation_cost] || calculate_cost(result),
      generation_time_ms: result[:generation_time_ms],
      metadata: {
        source: result[:source] || 'generation',
        cached: result[:source] == 'cache',
        generation_id: result[:generation_id],
        model_version: result[:model_version],
        temperature: result.dig(:generation_parameters, :temperature),
        max_tokens: result.dig(:generation_parameters, :max_tokens),
        prompt_tokens: result.dig(:usage, :prompt_tokens),
        completion_tokens: result.dig(:usage, :completion_tokens)
      }.compact
    )
  end

  # Instance methods
  def model_display_name
    case ai_model_name
    when /gpt-4o/i
      'GPT-4o'
    when /gpt-4/i
      'GPT-4'
    when /gpt-3.5/i
      'GPT-3.5 Turbo'
    when /claude-3.5-sonnet/i
      'Claude 3.5 Sonnet'
    when /claude-3-haiku/i
      'Claude 3 Haiku'
    when /gemini-1.5-pro/i
      'Gemini 1.5 Pro'
    when /gemini-1.5-flash/i
      'Gemini 1.5 Flash'
    else
      ai_model_name.humanize
    end
  end

  def provider_display_name
    ai_provider.humanize
  end

  def content_type_display_name
    case content_type
    when 'subject_line'
      'Subject Line'
    when 'preview_text'
      'Preview Text'
    when 'content'
      'Email Body'
    when 'full_email'
      'Complete Email'
    else
      content_type.humanize
    end
  end

  def generation_summary
    "#{content_type_display_name} generated by #{model_display_name}"
  end

  def cost_formatted
    return 'N/A' unless generation_cost
    "$#{generation_cost.round(6)}"
  end

  def confidence_percentage
    return nil unless confidence_score
    (confidence_score * 100).round(1)
  end

  def generation_time_formatted
    return 'N/A' unless generation_time_ms
    if generation_time_ms < 1000
      "#{generation_time_ms}ms"
    else
      "#{(generation_time_ms / 1000.0).round(2)}s"
    end
  end

  def was_cached?
    metadata['cached'] == true || metadata['source'] == 'cache'
  end

  def generation_details
    {
      model: model_display_name,
      provider: provider_display_name,
      content_type: content_type_display_name,
      generated_at: created_at,
      user: user.full_name || user.email,
      cost: cost_formatted,
      tokens: token_count,
      confidence: confidence_percentage,
      generation_time: generation_time_formatted,
      cached: was_cached?,
      parameters: generation_parameters
    }
  end

  private

  def self.extract_provider_from_model(model_name)
    return 'unknown' unless model_name

    case model_name.downcase
    when /gpt|openai/
      'openai'
    when /claude|anthropic/
      'anthropic'
    when /gemini|google/
      'gemini'
    when /deepseek/
      'deepseek'
    else
      'unknown'
    end
  end

  def self.extract_generated_content(result, content_type)
    case content_type
    when 'subject_line'
      result.dig(:campaign_data, :subject_line) || result[:subject_line] || result[:content]
    when 'preview_text'
      result.dig(:campaign_data, :preview_text) || result[:preview_text] || result[:content]
    when 'content'
      result.dig(:campaign_data, :content) || result[:content]
    when 'full_email'
      result[:campaign_data]&.to_json || result[:content]
    else
      result[:content] || result.to_s
    end
  end

  def self.calculate_cost(result)
    return nil unless result[:token_count] && result[:model_used]

    # Basic cost calculation - this should be enhanced with actual model pricing
    token_count = result[:token_count]
    model = result[:model_used]

    case model.downcase
    when /gpt-4o/
      (token_count / 1000.0) * 0.005
    when /gpt-4/
      (token_count / 1000.0) * 0.03
    when /gpt-3.5/
      (token_count / 1000.0) * 0.0005
    when /claude-3.5-sonnet/
      (token_count / 1000.0) * 0.003
    when /claude-3-haiku/
      (token_count / 1000.0) * 0.00025
    when /gemini-1.5-pro/
      (token_count / 1000.0) * 0.00125
    when /gemini-1.5-flash/
      (token_count / 1000.0) * 0.000075
    else
      nil
    end
  end
end
