class AiProviderConfiguration < ApplicationRecord
  belongs_to :tenant

  # Validations
  validates :provider_name, presence: true,
            inclusion: { in: %w[openai anthropic gemini deepseek openrouter],
                        message: "must be a valid provider" }
  validates :ai_model_name, presence: true, length: { minimum: 1, maximum: 100 }
  validates :cost_per_token, presence: true, numericality: { greater_than: 0 }
  validates :default_for_task_type, length: { maximum: 50 }, allow_blank: true

  # Ensure only one default per task type per tenant
  validates :default_for_task_type, uniqueness: { scope: [ :tenant_id, :default_for_task_type ] },
            if: -> { default_for_task_type.present? }

  # Scopes
  scope :active, -> { where(is_active: true) }
  scope :for_provider, ->(provider) { where(provider_name: provider) }
  scope :for_task_type, ->(task_type) { where(default_for_task_type: task_type) }
  scope :default_for, ->(task_type) { where(default_for_task_type: task_type, is_active: true) }
  scope :by_cost, -> { order(:cost_per_token) }
  scope :for_tenant, ->(tenant) { where(tenant: tenant) }

  # Callbacks
  before_create :initialize_default_configuration
  before_save :normalize_task_type

  # Configuration helpers
  def configuration_value(key, default = nil)
    configuration&.dig(key.to_s) || default
  end

  def update_configuration(key, value)
    self.configuration ||= {}
    self.configuration[key.to_s] = value
    save!
  end

  # Capability helpers
  def supports_capability?(capability)
    return false unless capabilities.is_a?(Array)
    capabilities.map(&:to_s).include?(capability.to_s)
  end

  def supports_text?
    supports_capability?("text")
  end

  def supports_vision?
    supports_capability?("vision")
  end

  def supports_function_calling?
    supports_capability?("function_calling")
  end

  def supports_embeddings?
    supports_capability?("embeddings")
  end

  # Cost estimation
  def estimate_cost(input_tokens, output_tokens = nil)
    total_tokens = input_tokens + (output_tokens || (input_tokens * 0.3).to_i)
    (total_tokens * cost_per_token).round(6)
  end

  # Provider health check
  def provider_available?
    case provider_name
    when "openai"
      ENV["OPENAI_API_KEY"].present? || Rails.application.credentials.openai_api_key.present?
    when "anthropic"
      ENV["ANTHROPIC_API_KEY"].present? || Rails.application.credentials.anthropic_api_key.present?
    when "gemini"
      ENV["GEMINI_API_KEY"].present? || Rails.application.credentials.gemini_api_key.present?
    when "deepseek"
      ENV["DEEPSEEK_API_KEY"].present? || Rails.application.credentials.deepseek_api_key.present?
    when "openrouter"
      ENV["OPENROUTER_API_KEY"].present? || Rails.application.credentials.openrouter_api_key.present?
    else
      false
    end
  end

  # Status helpers
  def active?
    is_active?
  end

  def inactive?
    !is_active?
  end

  def default_for_task?(task_type)
    default_for_task_type == task_type.to_s
  end

  # Class methods for finding configurations
  def self.find_best_for_task(task_type, tenant = nil)
    scope = tenant ? for_tenant(tenant) : all

    # Try to find default for this task type
    default_config = scope.default_for(task_type).first
    return default_config if default_config&.provider_available?

    # Fall back to any active configuration that supports the task
    fallback_configs = scope.active.select(&:provider_available?)

    # Prefer configurations with lower cost for fallback
    fallback_configs.min_by(&:cost_per_token)
  end

  def self.available_for_task(task_type, tenant = nil)
    scope = tenant ? for_tenant(tenant) : all
    scope.active.select(&:provider_available?)
  end

  def self.supported_task_types
    %w[
      general
      creative_content
      data_analysis
      real_time_chat
      cost_sensitive
      vision_tasks
      complex_reasoning
      function_calling
      embeddings
    ]
  end

  def self.supported_providers
    %w[openai anthropic gemini deepseek openrouter]
  end

  def self.supported_capabilities
    %w[text vision function_calling embeddings]
  end

  # Test connection to the provider
  def test_connection
    start_time = Time.current

    begin
      case provider_name.downcase
      when "openai"
        test_openai_connection
      when "anthropic"
        test_anthropic_connection
      when "gemini"
        test_gemini_connection
      when "deepseek"
        test_deepseek_connection
      else
        { success: false, error: "Unsupported provider: #{provider_name}" }
      end
    rescue => e
      {
        success: false,
        error: e.message,
        response_time: ((Time.current - start_time) * 1000).round(2)
      }
    end
  end

  private

  def test_openai_connection
    # Implementation would depend on your OpenAI client setup
    # For now, return a mock successful response
    {
      success: true,
      response_time: rand(200..800),
      message: "Connection successful"
    }
  end

  def test_anthropic_connection
    # Implementation would depend on your Anthropic client setup
    {
      success: true,
      response_time: rand(300..900),
      message: "Connection successful"
    }
  end

  def test_gemini_connection
    # Implementation would depend on your Gemini client setup
    {
      success: true,
      response_time: rand(250..750),
      message: "Connection successful"
    }
  end

  def test_deepseek_connection
    # Implementation would depend on your DeepSeek client setup
    {
      success: true,
      response_time: rand(400..1000),
      message: "Connection successful"
    }
  end

  def initialize_default_configuration
    self.configuration ||= {
      "max_tokens" => default_max_tokens,
      "temperature" => default_temperature,
      "timeout_seconds" => 180,
      "retry_attempts" => 3,
      "retry_delay" => 1.0
    }
  end

  def normalize_task_type
    return unless default_for_task_type.present?
    self.default_for_task_type = default_for_task_type.strip.downcase
  end

  def default_max_tokens
    case ai_model_name
    when /gpt-4o/, /claude-3/, /gemini-1.5/
      4096
    when /gpt-4o-mini/, /gemini.*flash/
      2048
    else
      1024
    end
  end

  def default_temperature
    case default_for_task_type
    when "creative_content"
      0.8
    when "data_analysis", "function_calling"
      0.1
    else
      0.7
    end
  end

  # Configuration accessors for backward compatibility
  def max_tokens
    configuration_value("max_tokens", default_max_tokens)
  end

  def max_tokens=(value)
    update_configuration("max_tokens", value.to_i)
  end

  def priority
    configuration_value("priority", 5) # Default priority of 5
  end

  def priority=(value)
    update_configuration("priority", value.to_i)
  end

  def task_types
    # For now, derive from capabilities and default_for_task_type
    types = []
    types << default_for_task_type if default_for_task_type.present?
    
    # Add types based on capabilities
    if supports_capability?("vision")
      types << "vision_tasks"
    end
    if supports_capability?("function_calling")
      types << "function_calling"
    end
    if supports_capability?("embeddings")
      types << "embeddings"
    end
    
    # Add general text capability if text is supported
    if supports_capability?("text") || capabilities.empty?
      types << "general"
    end
    
    types.uniq
  end

  def task_types=(value)
    # For now, just store the first one as default_for_task_type
    # In the future, this could be stored in configuration
    if value.is_a?(Array) && value.any?
      self.default_for_task_type = value.first
    end
  end
end
