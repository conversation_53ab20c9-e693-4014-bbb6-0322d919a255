<% content_for :title, "Dashboard" %>

<!-- Modern AI Marketing Hub Dashboard -->
<div class="min-h-screen bg-gray-50" data-controller="mobile-sidebar">
  
  <!-- Mobile Sidebar Overlay -->
  <div class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden" 
       data-mobile-sidebar-target="overlay"
       data-action="click->mobile-sidebar#close"></div>
  
  <!-- Fixed Sidebar -->
  <div class="fixed left-0 top-0 h-full w-64 bg-white shadow-lg border-r border-gray-100 z-30 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out" 
       data-mobile-sidebar-target="sidebar">
    
    <!-- Sidebar Content -->
    <div class="flex flex-col h-full">
      <!-- Logo Section -->
      <div class="flex items-center justify-between p-6 border-b border-gray-100">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-lg font-bold text-gray-900">AI Marketing</h1>
            <p class="text-xs text-gray-500">Hub</p>
          </div>
        </div>
        
        <!-- Mobile Close Button -->
        <button type="button" 
                class="lg:hidden p-1 text-gray-400 hover:text-gray-600 rounded-md"
                data-action="click->mobile-sidebar#close">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Navigation -->
      <nav class="flex-1 p-6">
        <div class="space-y-2">
          <% [
               { path: dashboard_path, label: 'Dashboard', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z', active: true },
               { path: campaigns_path, label: 'Campaigns', icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10' },
               { path: audiences_path, label: 'Audiences', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
               { path: ai_agents_path, label: 'AI Agents', icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z' },
               { path: vibe_analytics_path, label: 'Vibe Analytics', icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z' },
               { path: '#', label: 'Analytics', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
               { path: account_settings_path, label: 'Settings', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z' }
             ].each do |nav_item| %>
            <%= link_to nav_item[:path], 
                class: "flex items-center space-x-3 px-3 py-2.5 rounded-xl transition-colors duration-200 group #{ nav_item[:active] ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }" do %>
              <svg class="w-5 h-5 flex-shrink-0 #{ nav_item[:active] ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600' }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= nav_item[:icon] %>"></path>
              </svg>
              <span class="font-medium"><%= nav_item[:label] %></span>
            <% end %>
          <% end %>
        </div>
      </nav>

      <!-- User Profile Section -->
      <div class="p-6 border-t border-gray-100">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-600 rounded-lg flex items-center justify-center text-white font-semibold">
            <%= current_user.first_name.first %><%= current_user.last_name.first %>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate"><%= current_user.first_name %></p>
            <p class="text-xs text-gray-500 truncate"><%= current_user.email %></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="lg:ml-64">
    <!-- Header -->
    <div class="bg-white border-b border-gray-100 px-4 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <!-- Mobile Menu Button -->
        <button type="button" 
                class="lg:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                data-action="click->mobile-sidebar#toggle"
                aria-label="Toggle menu">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
        
        <div class="flex-1 lg:flex-none">
          <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p class="text-gray-600">Welcome back, <%= current_user.first_name %></p>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- Time Range Dropdown -->
          <div class="relative" data-controller="dropdown">
            <button type="button" 
                    class="flex items-center space-x-2 px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
                    data-action="click->dropdown#toggle"
                    data-dropdown-target="button">
              <span>Last 30 days</span>
              <svg class="w-4 h-4 text-gray-400 transition-transform duration-200" data-dropdown-target="arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 opacity-0 invisible transform scale-95 transition-all duration-200 z-50"
                 data-dropdown-target="menu">
              <div class="py-2">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Last 7 days</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Last 30 days</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Last 90 days</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Last year</a>
              </div>
            </div>
          </div>

          <!-- Create Button -->
          <%= link_to new_campaign_path, 
              class: "flex items-center space-x-2 px-6 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors" do %>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span>Create</span>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="p-4 lg:p-8">
      <!-- Overview Section -->
      <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-gray-900">Overview</h2>
          <div class="relative" data-controller="dropdown">
            <button type="button" 
                    class="flex items-center space-x-2 px-3 py-1.5 text-sm text-gray-500 hover:text-gray-700 transition-colors"
                    data-action="click->dropdown#toggle"
                    data-dropdown-target="button">
              <span>Last month</span>
              <svg class="w-4 h-4 transition-transform duration-200" data-dropdown-target="arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            <div class="absolute right-0 mt-2 w-36 bg-white rounded-lg shadow-lg border border-gray-100 opacity-0 invisible transform scale-95 transition-all duration-200 z-50"
                 data-dropdown-target="menu">
              <div class="py-2">
                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-50">Last week</a>
                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-50">Last month</a>
                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-50">Last quarter</a>
              </div>
            </div>
          </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <!-- Campaigns -->
          <div class="dashboard-card bg-white rounded-2xl p-6 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                </div>
                <span class="text-gray-600 font-medium">Campaigns</span>
              </div>
            </div>
            <div class="flex items-end space-x-4">
              <div>
                <div class="text-3xl font-bold text-gray-900"><%= @campaign_stats[:total] || 12 %></div>
                <div class="flex items-center space-x-1 text-sm">
                  <span class="text-green-600 font-medium">↗ +36.8%</span>
                  <span class="text-gray-500">vs last month</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Revenue -->
          <div class="dashboard-card bg-white rounded-2xl p-6 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <span class="text-gray-600 font-medium">Revenue</span>
              </div>
            </div>
            <div class="flex items-end space-x-4">
              <div>
                <div class="text-3xl font-bold text-gray-900">$<%= number_with_delimiter((@budget_stats && @budget_stats[:total_budget]) || 25600) %></div>
                <div class="flex items-center space-x-1 text-sm">
                  <span class="text-green-600 font-medium">↗ +36.8%</span>
                  <span class="text-gray-500">vs last month</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- New Leads Today -->
        <div class="dashboard-card bg-white rounded-2xl p-6 border border-gray-100 mb-8">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900"><%= rand(50..150) %> new leads today!</h3>
            <span class="text-sm text-gray-500">Auto-generated by AI</span>
          </div>
          
          <!-- Customer Avatars -->
          <div class="flex items-center space-x-4">
            <% 5.times do |i| %>
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                  <%= ['A', 'B', 'C', 'D', 'E'][i] %>
                </div>
                <p class="text-xs text-gray-600 text-center mt-2">User <%= i + 1 %></p>
              </div>
            <% end %>
            <div class="flex items-center space-x-2 ml-4">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
              <span class="text-blue-600 font-medium text-sm">View all</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Two Column Layout -->
      <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <!-- Left Column - Campaign Performance -->
        <div class="xl:col-span-2">
          <!-- Campaign Performance Chart -->
          <div class="dashboard-card bg-white rounded-2xl p-6 border border-gray-100 mb-8">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900">Campaign performance</h3>
              <div class="relative" data-controller="dropdown">
                <button type="button" 
                        class="flex items-center space-x-2 px-3 py-1.5 text-sm text-gray-500 hover:text-gray-700 transition-colors"
                        data-action="click->dropdown#toggle"
                        data-dropdown-target="button">
                  <span>Last 7 days</span>
                  <svg class="w-4 h-4 transition-transform duration-200" data-dropdown-target="arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                
                <div class="absolute right-0 mt-2 w-36 bg-white rounded-lg shadow-lg border border-gray-100 opacity-0 invisible transform scale-95 transition-all duration-200 z-50"
                     data-dropdown-target="menu">
                  <div class="py-2">
                    <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-50">Last 7 days</a>
                    <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-50">Last 30 days</a>
                    <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-50">Last 90 days</a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Chart Area -->
            <div class="h-64 bg-gray-50 rounded-xl flex items-center justify-center mb-4">
              <div class="text-center">
                <div class="text-4xl font-bold text-green-600 mb-2">$10.2m</div>
                <div class="text-gray-500">Total campaign revenue</div>
                <div class="mt-4 w-16 h-2 bg-green-500 rounded-full mx-auto"></div>
              </div>
            </div>
          </div>

          <!-- AI Agents Activity -->
          <div class="dashboard-card bg-white rounded-2xl p-6 border border-gray-100">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900">AI Agent activity</h3>
              <span class="text-sm text-blue-600 cursor-pointer hover:text-blue-800">See all</span>
            </div>

            <div class="space-y-4">
              <% [
                   { action: 'Campaign optimization', model: 'GPT-4o', status: 'completed', time: '2 min ago', amount: '+$1,250.14', type: 'revenue' },
                   { action: 'Email sequence A/B test', model: 'Claude-3.5', status: 'running', time: '5 min ago', amount: '+$890.25', type: 'revenue' },
                   { action: 'Audience segmentation', model: 'GPT-4o', status: 'completed', time: '8 min ago', amount: '+$2,150.00', type: 'revenue' },
                   { action: 'Content generation', model: 'Claude-3.5', status: 'completed', time: '12 min ago', amount: '+$750.50', type: 'revenue' }
                 ].each do |activity| %>
                <div class="activity-item flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div class="flex items-center space-x-4">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900"><%= activity[:action] %></div>
                      <div class="text-sm text-gray-500"><%= activity[:model] %> • <%= activity[:time] %></div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="font-semibold text-green-600"><%= activity[:amount] %></div>
                    <div class="text-xs text-gray-500 capitalize">
                      <span class="status-badge <%= activity[:status] %>">
                        <%= activity[:status] %>
                      </span>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-8">
          <!-- Top Campaigns -->
          <div class="dashboard-card bg-white rounded-2xl p-6 border border-gray-100">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900">Top campaigns</h3>
              <span class="text-sm text-blue-600 cursor-pointer hover:text-blue-800">See all</span>
            </div>

            <div class="space-y-4">
              <% [
                   { name: 'Email Nurture Pro', revenue: '$3,250.00', status: 'Active', color: 'from-blue-400 to-blue-600' },
                   { name: 'Social Media Blast', revenue: '$7,890.00', status: 'Active', color: 'from-purple-400 to-purple-600' },
                   { name: 'SEO Content Push', revenue: '$1,500.00', status: 'Offline', color: 'from-gray-400 to-gray-600' },
                   { name: 'Retargeting Ads', revenue: '$9,999.99', status: 'Active', color: 'from-green-400 to-green-600' },
                   { name: 'Webinar Funnel', revenue: '$4,750.00', status: 'Active', color: 'from-pink-400 to-pink-600' }
                 ].each do |campaign| %>
                <div class="campaign-item flex items-center space-x-4">
                  <div class="w-12 h-12 bg-gradient-to-br <%= campaign[:color] %> rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                    <%= campaign[:name].split.map(&:first).join %>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="font-medium text-gray-900 text-sm"><%= campaign[:name] %></div>
                    <div class="text-xs text-gray-500">AI-optimized campaign</div>
                  </div>
                  <div class="text-right">
                    <div class="font-semibold text-sm"><%= campaign[:revenue] %></div>
                    <div class="text-xs">
                      <span class="status-badge <%= campaign[:status].downcase %>">
                        <%= campaign[:status] %>
                      </span>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>

            <button class="w-full mt-4 py-2 text-sm text-gray-600 hover:text-gray-900 border-t border-gray-100 pt-4">
              All campaigns
            </button>
          </div>

          <!-- Vibe Index -->
          <div class="dashboard-card bg-white rounded-2xl p-6 border border-gray-100">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900">Vibe index</h3>
              <span class="text-sm text-blue-600 cursor-pointer hover:text-blue-800">See all</span>
            </div>

            <!-- Circular Progress -->
            <div class="flex items-center justify-center mb-6">
              <div class="relative w-32 h-32">
                <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                  <path class="text-gray-200" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                  <path class="text-green-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="82, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="text-center">
                    <div class="text-3xl font-bold text-gray-900">82</div>
                    <div class="text-sm text-gray-500">Positive</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="text-center text-sm text-gray-600">
              Your campaigns have a <span class="font-semibold text-green-600">positive emotional resonance</span> with audiences
            </div>
          </div>

          <!-- AI Model Performance -->
          <div class="dashboard-card bg-white rounded-2xl p-6 border border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Neural AI</h3>
            
            <div class="space-y-4">
              <div class="ai-feature-card bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-900 mb-2">Campaign Optimization</h4>
                <p class="text-sm text-gray-600 mb-3">Automate campaign performance based on user-defined criteria, using AI algorithms to optimize results optimally.</p>
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                  <span class="text-sm font-medium text-green-700">Active & Learning</span>
                </div>
              </div>

              <div class="ai-feature-card purple bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-900 mb-2">Real-Time Analytics</h4>
                <p class="text-sm text-gray-600 mb-3">Get real-time alerts about campaign changes, such as sudden increases or decreases in metrics.</p>
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                  </div>
                  <span class="text-sm font-medium text-purple-700">Live Monitoring</span>
                </div>
              </div>
            </div>

            <!-- AI Chat Interface -->
            <div class="mt-6 pt-4 border-t border-gray-100">
              <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <input type="text" 
                       placeholder="Ask AI anything..." 
                       class="chat-input flex-1 bg-transparent border-none outline-none text-sm text-gray-700 placeholder-gray-500">
                <button class="chat-send-btn w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
