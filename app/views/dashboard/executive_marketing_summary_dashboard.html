<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Executive Marketing Summary Dashboard</title>
    <link rel="stylesheet" href="../css/main.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-background font-inter">
    <!-- Header Navigation -->
    <header class="bg-surface border-b border-border sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <svg class="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                    </svg>
                    <h1 class="text-xl font-semibold text-text-primary">MarketingHub</h1>
                </div>
                
                <!-- Navigation -->
                <nav class="hidden md:flex items-center space-x-6">
                    <a href="marketing_performance_overview_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary hover:text-primary transition-micro">Overview</a>
                    <a href="campaign_analytics_deep_dive_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary hover:text-primary transition-micro">Analytics</a>
                    <a href="real_time_campaign_monitoring_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary hover:text-primary transition-micro">Real-time</a>
                    <a href="executive_marketing_summary_dashboard.html" class="px-3 py-2 text-sm font-medium text-primary bg-primary-50 rounded-md">Executive</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button class="md:hidden p-2 rounded-md text-text-secondary hover:text-primary" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div class="md:hidden mt-4 pb-4 border-t border-border hidden" id="mobile-menu">
                <div class="flex flex-col space-y-2 pt-4">
                    <a href="marketing_performance_overview_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary">Overview</a>
                    <a href="campaign_analytics_deep_dive_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary">Analytics</a>
                    <a href="real_time_campaign_monitoring_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary">Real-time</a>
                    <a href="executive_marketing_summary_dashboard.html" class="px-3 py-2 text-sm font-medium text-primary bg-primary-50 rounded-md">Executive</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-6 py-8">
        <!-- Page Header with Executive Controls -->
        <section class="mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
                <div>
                    <h2 class="text-fluid-3xl font-semibold text-text-primary mb-2">Executive Marketing Summary</h2>
                    <p class="text-text-secondary">Strategic insights and performance overview for leadership decision-making</p>
                </div>
                
                <!-- Last Updated Timestamp -->
                <div class="flex items-center space-x-2 text-sm text-text-secondary">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Last updated: Today, 9:15 AM</span>
                </div>
            </div>

            <!-- Executive Controls -->
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 p-6 bg-surface rounded-lg border border-border">
                <!-- Period Toggle -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-text-primary">Reporting Period</label>
                    <div class="flex bg-secondary-100 rounded-md p-1">
                        <button class="flex-1 px-3 py-2 text-sm font-medium bg-primary text-white rounded-md transition-micro" id="quarterly-btn">
                            Quarterly
                        </button>
                        <button class="flex-1 px-3 py-2 text-sm font-medium text-text-secondary hover:text-primary transition-micro" id="monthly-btn">
                            Monthly
                        </button>
                    </div>
                </div>

                <!-- Business Unit Selector -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-text-primary">Business Unit</label>
                    <select class="w-full px-3 py-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>All Units</option>
                        <option>North America</option>
                        <option>Europe</option>
                        <option>Asia Pacific</option>
                        <option>Enterprise</option>
                    </select>
                </div>

                <!-- Manual Refresh -->
                <div class="flex items-end">
                    <button class="w-full px-4 py-2 bg-secondary-100 text-text-secondary rounded-md text-sm font-medium hover:bg-secondary-200 transition-micro flex items-center justify-center space-x-2" id="refresh-btn">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Refresh</span>
                    </button>
                </div>

                <!-- Print Layout Button -->
                <div class="flex items-end">
                    <button class="w-full px-4 py-2 bg-primary text-white rounded-md text-sm font-medium hover:bg-primary-700 transition-micro flex items-center justify-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                        </svg>
                        <span>Export Report</span>
                    </button>
                </div>
            </div>
        </section>

        <!-- Hero KPI Cards (3 Large Cards - 4 cols each) -->
        <section class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Marketing ROI Card -->
            <div class="data-card p-8 animate-fade-in">
                <div class="flex items-center justify-between mb-6">
                    <div class="p-3 bg-success-50 rounded-lg">
                        <svg class="w-8 h-8 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <div class="flex items-center space-x-2 text-success text-lg font-medium">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>+18.5%</span>
                    </div>
                </div>
                <div>
                    <h3 class="text-4xl font-semibold text-text-primary text-data mb-2">4.2x</h3>
                    <p class="text-text-primary font-medium text-lg mb-1">Marketing ROI</p>
                    <p class="text-text-secondary text-sm">Return on marketing investment this quarter</p>
                    <!-- Executive Insight -->
                    <div class="mt-4 p-3 bg-success-50 rounded-lg">
                        <p class="text-sm text-success font-medium">Strong performance across all channels</p>
                    </div>
                </div>
            </div>

            <!-- Customer Acquisition Cost Card -->
            <div class="data-card p-8 animate-fade-in" style="animation-delay: 100ms;">
                <div class="flex items-center justify-between mb-6">
                    <div class="p-3 bg-primary-50 rounded-lg">
                        <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="flex items-center space-x-2 text-success text-lg font-medium">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>-12.3%</span>
                    </div>
                </div>
                <div>
                    <h3 class="text-4xl font-semibold text-text-primary text-data mb-2">$127</h3>
                    <p class="text-text-primary font-medium text-lg mb-1">Customer Acquisition Cost</p>
                    <p class="text-text-secondary text-sm">Average cost to acquire new customers</p>
                    <div class="mt-4 p-3 bg-primary-50 rounded-lg">
                        <p class="text-sm text-primary font-medium">Efficiency improved through optimization</p>
                    </div>
                </div>
            </div>

            <!-- Revenue Attribution Card -->
            <div class="data-card p-8 animate-fade-in" style="animation-delay: 200ms;">
                <div class="flex items-center justify-between mb-6">
                    <div class="p-3 bg-accent-50 rounded-lg">
                        <svg class="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="flex items-center space-x-2 text-success text-lg font-medium">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>+24.7%</span>
                    </div>
                </div>
                <div>
                    <h3 class="text-4xl font-semibold text-text-primary text-data mb-2">$2.8M</h3>
                    <p class="text-text-primary font-medium text-lg mb-1">Revenue Attribution</p>
                    <p class="text-text-secondary text-sm">Marketing-driven revenue this quarter</p>
                    <div class="mt-4 p-3 bg-accent-50 rounded-lg">
                        <p class="text-sm text-accent font-medium">Exceeding quarterly targets</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Visualization and Supporting Metrics -->
        <section class="grid grid-cols-1 lg:grid-cols-12 gap-6 mb-8">
            <!-- Primary Visualization (8 cols) -->
            <div class="lg:col-span-8">
                <div class="data-card p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h3 class="text-lg font-semibold text-text-primary">Marketing Contribution to Revenue Pipeline</h3>
                            <p class="text-sm text-text-secondary mt-1">Quarterly trend analysis with milestone annotations</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-primary rounded-full"></div>
                                <span class="text-sm text-text-secondary">Marketing Revenue</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-accent rounded-full"></div>
                                <span class="text-sm text-text-secondary">Pipeline Value</span>
                            </div>
                        </div>
                    </div>
                    <div class="h-96">
                        <canvas id="revenue-pipeline-chart" class="w-full h-full"></canvas>
                    </div>
                    <!-- Key Milestones -->
                    <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center p-3 bg-secondary-50 rounded-lg">
                            <p class="text-sm text-text-secondary">Q1 Campaign Launch</p>
                            <p class="font-medium text-text-primary">+$450K</p>
                        </div>
                        <div class="text-center p-3 bg-secondary-50 rounded-lg">
                            <p class="text-sm text-text-secondary">Product Launch</p>
                            <p class="font-medium text-text-primary">+$720K</p>
                        </div>
                        <div class="text-center p-3 bg-secondary-50 rounded-lg">
                            <p class="text-sm text-text-secondary">Holiday Campaign</p>
                            <p class="font-medium text-text-primary">+$890K</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Supporting Metrics Panel (4 cols) -->
            <div class="lg:col-span-4 space-y-6">
                <!-- Channel Effectiveness Ranking -->
                <div class="data-card p-6">
                    <h3 class="text-lg font-semibold text-text-primary mb-4">Channel Effectiveness</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">1</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-text-primary">Search Marketing</p>
                                    <p class="text-xs text-text-secondary">ROI: 5.2x</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-text-primary">$1.2M</p>
                                <p class="text-xs text-success">+28%</p>
                            </div>
                        </div>
                        <div class="w-full bg-secondary-100 rounded-full h-2">
                            <div class="bg-primary h-2 rounded-full" style="width: 85%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">2</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-text-primary">Social Media</p>
                                    <p class="text-xs text-text-secondary">ROI: 4.1x</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-text-primary">$890K</p>
                                <p class="text-xs text-success">+22%</p>
                            </div>
                        </div>
                        <div class="w-full bg-secondary-100 rounded-full h-2">
                            <div class="bg-accent h-2 rounded-full" style="width: 72%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-success rounded-lg flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">3</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-text-primary">Email Marketing</p>
                                    <p class="text-xs text-text-secondary">ROI: 3.8x</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-text-primary">$650K</p>
                                <p class="text-xs text-success">+15%</p>
                            </div>
                        </div>
                        <div class="w-full bg-secondary-100 rounded-full h-2">
                            <div class="bg-success h-2 rounded-full" style="width: 58%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-warning rounded-lg flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">4</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-text-primary">Display Ads</p>
                                    <p class="text-xs text-text-secondary">ROI: 2.9x</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-text-primary">$420K</p>
                                <p class="text-xs text-warning">+8%</p>
                            </div>
                        </div>
                        <div class="w-full bg-secondary-100 rounded-full h-2">
                            <div class="bg-warning h-2 rounded-full" style="width: 38%"></div>
                        </div>
                    </div>
                </div>

                <!-- Market Share Indicators -->
                <div class="data-card p-6">
                    <h3 class="text-lg font-semibold text-text-primary mb-4">Market Position</h3>
                    <div class="space-y-4">
                        <div class="text-center">
                            <div class="relative w-24 h-24 mx-auto mb-3">
                                <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-secondary-200" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="text-primary" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="67, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="text-lg font-semibold text-text-primary">67%</span>
                                </div>
                            </div>
                            <p class="text-sm font-medium text-text-primary">Brand Awareness</p>
                            <p class="text-xs text-success">+5% vs last quarter</p>
                        </div>

                        <div class="grid grid-cols-2 gap-4 pt-4 border-t border-border">
                            <div class="text-center">
                                <p class="text-2xl font-semibold text-text-primary text-data">23%</p>
                                <p class="text-xs text-text-secondary">Market Share</p>
                                <p class="text-xs text-success">+2.1%</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-semibold text-text-primary text-data">8.9</p>
                                <p class="text-xs text-text-secondary">NPS Score</p>
                                <p class="text-xs text-success">+0.7</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Strategic Insights Table -->
        <section class="data-card p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-lg font-semibold text-text-primary">Strategic Insights & Recommendations</h3>
                    <p class="text-sm text-text-secondary mt-1">Key performance drivers and executive action items</p>
                </div>
                <button class="px-4 py-2 bg-primary text-white rounded-md text-sm font-medium hover:bg-primary-700 transition-micro">
                    Generate Full Report
                </button>
            </div>

            <!-- Desktop Table -->
            <div class="hidden md:block overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-border">
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Insight Category</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Key Finding</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Business Impact</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Recommended Action</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Priority</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-border">
                        <tr class="hover:bg-secondary-50 transition-micro">
                            <td class="py-4 px-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-success rounded-full"></div>
                                    <span class="font-medium text-text-primary">Channel Performance</span>
                                </div>
                            </td>
                            <td class="py-4 px-4 text-text-secondary">Search marketing ROI increased 28% QoQ, outperforming all other channels</td>
                            <td class="py-4 px-4">
                                <span class="text-success font-medium">+$340K revenue</span>
                            </td>
                            <td class="py-4 px-4 text-text-secondary">Increase search budget allocation by 25% for Q4</td>
                            <td class="py-4 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success">High</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-secondary-50 transition-micro">
                            <td class="py-4 px-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-warning rounded-full"></div>
                                    <span class="font-medium text-text-primary">Customer Acquisition</span>
                                </div>
                            </td>
                            <td class="py-4 px-4 text-text-secondary">CAC decreased 12% while maintaining lead quality scores above 85%</td>
                            <td class="py-4 px-4">
                                <span class="text-success font-medium">$180K cost savings</span>
                            </td>
                            <td class="py-4 px-4 text-text-secondary">Scale successful optimization tactics across all channels</td>
                            <td class="py-4 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning">Medium</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-secondary-50 transition-micro">
                            <td class="py-4 px-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-primary rounded-full"></div>
                                    <span class="font-medium text-text-primary">Market Opportunity</span>
                                </div>
                            </td>
                            <td class="py-4 px-4 text-text-secondary">Untapped potential in enterprise segment with 40% higher LTV</td>
                            <td class="py-4 px-4">
                                <span class="text-primary font-medium">$2.1M potential</span>
                            </td>
                            <td class="py-4 px-4 text-text-secondary">Launch dedicated enterprise marketing initiative</td>
                            <td class="py-4 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success">High</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Mobile Cards -->
            <div class="md:hidden space-y-4">
                <div class="border border-border rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-success rounded-full"></div>
                            <h4 class="font-medium text-text-primary">Channel Performance</h4>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success">High</span>
                    </div>
                    <p class="text-sm text-text-secondary mb-2">Search marketing ROI increased 28% QoQ, outperforming all other channels</p>
                    <div class="text-sm">
                        <span class="text-text-secondary">Impact:</span>
                        <span class="ml-1 font-medium text-success">+$340K revenue</span>
                    </div>
                    <div class="text-sm mt-1">
                        <span class="text-text-secondary">Action:</span>
                        <span class="ml-1 text-text-secondary">Increase search budget allocation by 25% for Q4</span>
                    </div>
                </div>

                <div class="border border-border rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-warning rounded-full"></div>
                            <h4 class="font-medium text-text-primary">Customer Acquisition</h4>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning">Medium</span>
                    </div>
                    <p class="text-sm text-text-secondary mb-2">CAC decreased 12% while maintaining lead quality scores above 85%</p>
                    <div class="text-sm">
                        <span class="text-text-secondary">Impact:</span>
                        <span class="ml-1 font-medium text-success">$180K cost savings</span>
                    </div>
                    <div class="text-sm mt-1">
                        <span class="text-text-secondary">Action:</span>
                        <span class="ml-1 text-text-secondary">Scale successful optimization tactics across all channels</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Automated Insights Engine -->
        <section class="data-card p-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-accent-50 rounded-lg">
                        <svg class="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-text-primary">AI-Powered Insights</h3>
                        <p class="text-sm text-text-secondary">Automated anomaly detection and opportunity identification</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2 text-sm text-text-secondary">
                    <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                    <span>Live Analysis</span>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Anomalies Detected -->
                <div class="space-y-4">
                    <h4 class="font-medium text-text-primary flex items-center space-x-2">
                        <svg class="w-4 h-4 text-warning" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Anomalies Detected</span>
                    </h4>
                    <div class="space-y-3">
                        <div class="p-3 bg-warning-50 rounded-lg border-l-4 border-warning">
                            <p class="text-sm font-medium text-warning">Unusual spike in mobile traffic</p>
                            <p class="text-xs text-text-secondary mt-1">Mobile conversion rate increased 45% in the last 48 hours, investigate potential cause</p>
                        </div>
                        <div class="p-3 bg-secondary-50 rounded-lg border-l-4 border-secondary-300">
                            <p class="text-sm font-medium text-text-primary">Email engagement plateau</p>
                            <p class="text-xs text-text-secondary mt-1">Open rates have remained flat for 3 weeks, consider A/B testing new subject lines</p>
                        </div>
                    </div>
                </div>

                <!-- Opportunities Identified -->
                <div class="space-y-4">
                    <h4 class="font-medium text-text-primary flex items-center space-x-2">
                        <svg class="w-4 h-4 text-success" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Opportunities Identified</span>
                    </h4>
                    <div class="space-y-3">
                        <div class="p-3 bg-success-50 rounded-lg border-l-4 border-success">
                            <p class="text-sm font-medium text-success">Cross-sell opportunity</p>
                            <p class="text-xs text-text-secondary mt-1">High-value customers show 73% interest in premium features, potential $450K revenue</p>
                        </div>
                        <div class="p-3 bg-primary-50 rounded-lg border-l-4 border-primary">
                            <p class="text-sm font-medium text-primary">Geographic expansion</p>
                            <p class="text-xs text-text-secondary mt-1">Strong organic interest from APAC region suggests market readiness for expansion</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-surface border-t border-border mt-16">
        <div class="max-w-7xl mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <svg class="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                    </svg>
                    <span class="text-text-primary font-medium">MarketingHub</span>
                </div>
                <p class="text-text-secondary text-sm">© 2025 MarketingHub. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Period toggle functionality
        document.getElementById('quarterly-btn').addEventListener('click', function() {
            this.classList.add('bg-primary', 'text-white');
            this.classList.remove('text-text-secondary');
            document.getElementById('monthly-btn').classList.remove('bg-primary', 'text-white');
            document.getElementById('monthly-btn').classList.add('text-text-secondary');
        });

        document.getElementById('monthly-btn').addEventListener('click', function() {
            this.classList.add('bg-primary', 'text-white');
            this.classList.remove('text-text-secondary');
            document.getElementById('quarterly-btn').classList.remove('bg-primary', 'text-white');
            document.getElementById('quarterly-btn').classList.add('text-text-secondary');
        });

        // Refresh button animation
        document.getElementById('refresh-btn').addEventListener('click', function() {
            const icon = this.querySelector('svg');
            icon.classList.add('animate-spin');
            setTimeout(() => {
                icon.classList.remove('animate-spin');
            }, 1000);
        });

        // Initialize Revenue Pipeline Chart
        function initializeRevenuePipelineChart() {
            const ctx = document.getElementById('revenue-pipeline-chart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Q1 Jan', 'Q1 Feb', 'Q1 Mar', 'Q2 Apr', 'Q2 May', 'Q2 Jun', 'Q3 Jul', 'Q3 Aug', 'Q3 Sep', 'Q4 Oct', 'Q4 Nov', 'Q4 Dec'],
                    datasets: [{
                        label: 'Marketing Revenue',
                        data: [450, 520, 680, 750, 820, 890, 950, 1100, 1250, 1350, 1450, 1580],
                        borderColor: '#1e40af',
                        backgroundColor: 'rgba(30, 64, 175, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#1e40af',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }, {
                        label: 'Pipeline Value',
                        data: [1200, 1350, 1500, 1650, 1800, 1950, 2100, 2300, 2500, 2700, 2850, 3000],
                        borderColor: '#0ea5e9',
                        backgroundColor: 'rgba(14, 165, 233, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#0ea5e9',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            titleColor: '#0f172a',
                            bodyColor: '#475569',
                            borderColor: '#e2e8f0',
                            borderWidth: 1,
                            cornerRadius: 6,
                            displayColors: true,
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': $' + context.parsed.y + 'K';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#64748b'
                            }
                        },
                        y: {
                            grid: {
                                color: '#f1f5f9'
                            },
                            ticks: {
                                color: '#64748b',
                                callback: function(value) {
                                    return '$' + value + 'K';
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    },
                    elements: {
                        point: {
                            hoverRadius: 8
                        }
                    }
                }
            });
        }

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeRevenuePipelineChart();
        });

        // Simulate real-time updates for AI insights
        setInterval(() => {
            const liveIndicator = document.querySelector('.animate-pulse');
            if (liveIndicator) {
                liveIndicator.style.opacity = '0.5';
                setTimeout(() => {
                    liveIndicator.style.opacity = '1';
                }, 500);
            }
        }, 5000);
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
<script id="dhws-elementInspector" src="../public/dhws-web-inspector.js"></script>
<script id="dhws-errorTracker" src="../public/dhws-error-tracker.js"></script>
</body>
</html>