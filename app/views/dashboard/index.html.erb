<% content_for :title, "Dashboard" %>

<!-- Dashboard Header -->
<div class="mb-6">
  <div class="flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
      <p class="text-gray-600">Overview of your marketing campaigns and performance</p>
    </div>
    
    <div class="flex items-center space-x-4">
      <!-- Notifications dropdown -->
      <div class="relative">
        <button type="button" class="relative rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
          <span class="sr-only">View notifications</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
            </svg>
            <% if @real_time_metrics[:recent_ai_requests] > 0 %>
              <span class="absolute -top-0.5 -right-0.5 h-4 w-4 rounded-full bg-red-400 flex items-center justify-center">
                <span class="text-xs font-medium text-white"><%= @real_time_metrics[:recent_ai_requests] %></span>
              </span>
            <% end %>
          </button>
        </div>
        
        <!-- Profile dropdown -->
        <div class="relative">
          <button type="button" class="flex max-w-xs items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
            <span class="sr-only">Open user menu</span>
            <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
          </button>
        </div>
        
        <!-- Create button -->
        <button type="button" class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
          <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
          </svg>
          Create
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main content -->
<div class="flex-1 overflow-auto bg-gray-50">
  <div class="p-6">
    <!-- Overview Stats -->
    <div class="mb-8">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Overview</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Campaigns Card -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Campaigns</p>
              <p class="text-3xl font-bold text-gray-900"><%= @campaign_stats[:total] || 0 %></p>
              <p class="text-sm text-gray-500 mt-1"><%= @campaign_stats[:active] || 0 %> active</p>
            </div>
            <div class="p-3 bg-blue-50 rounded-full">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Budget Card -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Budget</p>
              <p class="text-3xl font-bold text-gray-900">$<%= number_with_delimiter(@budget_stats[:total_budget] || 0) %></p>
              <p class="text-sm text-gray-500 mt-1">$<%= number_with_delimiter(@budget_stats[:spent_budget] || 0) %> spent</p>
            </div>
            <div class="p-3 bg-green-50 rounded-full">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Audiences Card -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Audiences</p>
              <p class="text-3xl font-bold text-gray-900"><%= @audience_stats[:total_audiences] || 0 %></p>
              <p class="text-sm <%= (@audience_stats[:growth_rate] || 0) >= 0 ? 'text-green-600' : 'text-red-600' %> mt-1">
                <%= (@audience_stats[:growth_rate] || 0) >= 0 ? '+' : '' %><%= @audience_stats[:growth_rate] || 0 %>% growth
              </p>
            </div>
            <div class="p-3 bg-purple-50 rounded-full">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- AI Agents Card -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">AI Agents</p>
              <p class="text-3xl font-bold text-gray-900"><%= @ai_agent_stats[:total_agents] || 0 %></p>
              <p class="text-sm text-gray-500 mt-1"><%= @ai_agent_stats[:active_agents] || 0 %> active</p>
            </div>
            <div class="p-3 bg-indigo-50 rounded-full">
              <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Real-time Campaign Monitoring -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-medium text-gray-900">Real-Time Campaign Monitoring</h2>
        <div class="flex items-center space-x-4">
          <!-- Live Status Indicator -->
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span class="text-sm font-medium text-green-600">LIVE</span>
          </div>
          <!-- Emergency Pause Button -->
          <button type="button" class="inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600" id="emergency-pause-btn">
            Emergency Pause All
          </button>
          <!-- View Full Monitor -->
          <a href="<%= url_for(controller: 'dashboard', action: 'real_time_campaign_monitoring_dashboard') %>" class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
            View Full Monitor
          </a>
        </div>
      </div>

      <!-- Live Metrics Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
        <!-- Current Spend Rate -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div class="flex items-center justify-between mb-2">
            <div class="p-2 bg-blue-50 rounded-lg">
              <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div class="flex items-center space-x-1 text-green-600 text-xs">
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>+2.3%</span>
            </div>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900" id="spend-rate">$<%= (@budget_stats[:spent_budget] || 0) / 24 %>/hr</h3>
            <p class="text-gray-600 text-xs">Spend Rate</p>
          </div>
        </div>

        <!-- Impression Velocity -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div class="flex items-center justify-between mb-2">
            <div class="p-2 bg-cyan-50 rounded-lg">
              <svg class="w-4 h-4 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            </div>
            <div class="flex items-center space-x-1 text-green-600 text-xs">
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>+5.7%</span>
            </div>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900" id="impression-velocity"><%= number_to_human(@real_time_metrics[:todays_impressions] || 0, format: '%n%u', units: { thousand: 'K', million: 'M' }, precision: 1) %>/day</h3>
            <p class="text-gray-600 text-xs">Impressions</p>
          </div>
        </div>

        <!-- Click-Through Rate -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div class="flex items-center justify-between mb-2">
            <div class="p-2 bg-yellow-50 rounded-lg">
              <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
              </svg>
            </div>
            <div class="flex items-center space-x-1 text-red-600 text-xs">
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>-0.8%</span>
            </div>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900" id="ctr"><%= @performance_metrics[:conversion_rate] || 0 %>%</h3>
            <p class="text-gray-600 text-xs">CTR</p>
          </div>
        </div>

        <!-- Conversion Tracking -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div class="flex items-center justify-between mb-2">
            <div class="p-2 bg-green-50 rounded-lg">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="flex items-center space-x-1 text-green-600 text-xs">
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>+12.1%</span>
            </div>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900" id="conversions"><%= @real_time_metrics[:todays_conversions] %></h3>
            <p class="text-gray-600 text-xs">Conversions</p>
          </div>
        </div>

        <!-- Active Campaigns -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div class="flex items-center justify-between mb-2">
            <div class="p-2 bg-gray-100 rounded-lg">
              <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
            <div class="flex items-center space-x-1 text-gray-600 text-xs">
              <span>Live</span>
            </div>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900" id="active-campaigns-count"><%= @campaign_stats[:active] || 0 %></h3>
            <p class="text-gray-600 text-xs">Active</p>
          </div>
        </div>

        <!-- Connection Status -->
        <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div class="flex items-center justify-between mb-2">
            <div class="p-2 bg-green-50 rounded-lg">
              <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
              </svg>
            </div>
            <div class="flex items-center space-x-1 text-green-600 text-xs">
              <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>Connected</span>
            </div>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">WebSocket</h3>
            <p class="text-gray-600 text-xs">Status</p>
          </div>
        </div>
      </div>

      <!-- Live Campaign Status & Alert Feed -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Live Spend Chart -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Live Spend Pacing</h3>
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <span class="text-sm text-gray-600">Actual</span>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-cyan-600 rounded-full"></div>
                  <span class="text-sm text-gray-600">Budget</span>
                </div>
              </div>
            </div>
            <div class="h-48">
              <canvas id="liveSpendChart" class="w-full h-full"></canvas>
            </div>
          </div>
        </div>

        <!-- Real-time Alert Feed -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Live Alerts</h3>
              <button class="text-indigo-600 text-sm hover:text-indigo-500 transition-colors">Clear All</button>
            </div>
            
            <!-- Connection Status -->
            <div class="flex items-center space-x-2 mb-4 p-2 bg-green-50 rounded-md">
              <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span class="text-xs text-green-600 font-medium">Real-time monitoring active</span>
            </div>
            
            <!-- Alert Feed -->
            <div class="space-y-3 max-h-48 overflow-y-auto" id="alert-feed">
              <div class="p-3 bg-red-50 border-l-4 border-red-400 rounded-md">
                <div class="flex items-center justify-between mb-1">
                  <span class="text-xs font-medium text-red-600">CRITICAL</span>
                  <span class="text-xs text-gray-500">2:47 PM</span>
                </div>
                <p class="text-sm text-gray-900">Q4 Product Launch exceeded 85% budget</p>
              </div>

              <div class="p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded-md">
                <div class="flex items-center justify-between mb-1">
                  <span class="text-xs font-medium text-yellow-600">WARNING</span>
                  <span class="text-xs text-gray-500">2:43 PM</span>
                </div>
                <p class="text-sm text-gray-900">Holiday Promotion CTR dropped below 2%</p>
              </div>

              <div class="p-3 bg-green-50 border-l-4 border-green-400 rounded-md">
                <div class="flex items-center justify-between mb-1">
                  <span class="text-xs font-medium text-green-600">SUCCESS</span>
                  <span class="text-xs text-gray-500">2:38 PM</span>
                </div>
                <p class="text-sm text-gray-900">Retargeting Campaign hit conversion goal</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Real-time Metrics Summary -->
    <div class="mb-8">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Today's Performance Summary</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Today's Revenue</p>
              <p class="text-2xl font-bold text-gray-900">$<%= number_with_delimiter(@real_time_metrics[:todays_revenue]) %></p>
            </div>
            <div class="p-2 bg-green-50 rounded-full">
              <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Impressions</p>
              <p class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@real_time_metrics[:todays_impressions]) %></p>
            </div>
            <div class="p-2 bg-yellow-50 rounded-full">
              <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Budget Utilization</p>
              <p class="text-2xl font-bold text-gray-900"><%= @budget_stats[:total_budget] > 0 ? ((@budget_stats[:spent_budget].to_f / @budget_stats[:total_budget].to_f) * 100).round(1) : 0 %>%</p>
            </div>
            <div class="p-2 bg-blue-50 rounded-full">
              <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">AI Requests</p>
              <p class="text-2xl font-bold text-gray-900"><%= @real_time_metrics[:recent_ai_requests] %></p>
            </div>
            <div class="p-2 bg-purple-50 rounded-full">
              <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Campaign Performance Chart -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Campaign Performance</h3>
        <div class="h-64">
          <canvas id="campaignChart"></canvas>
        </div>
      </div>

      <!-- Budget Utilization Chart -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Budget Utilization</h3>
        <div class="h-64">
          <canvas id="budgetChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Additional Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- AI Agent Performance -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">AI Agent Performance</h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Task Completion Rate</span>
            <span class="text-sm font-medium text-gray-900"><%= @ai_agent_stats[:task_completion_rate] %>%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-green-600 h-2 rounded-full" style="width: <%= @ai_agent_stats[:task_completion_rate] %>%"></div>
          </div>
          
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Average Efficiency</span>
            <span class="text-sm font-medium text-gray-900"><%= @ai_agent_stats[:avg_efficiency] %>%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full" style="width: <%= @ai_agent_stats[:avg_efficiency] %>%"></div>
          </div>
          
          <div class="pt-2 border-t border-gray-200">
            <div class="text-sm text-gray-600">Tasks This Month</div>
            <div class="text-lg font-semibold text-gray-900"><%= @ai_agent_stats[:completed_tasks_this_month] %>/<%= @ai_agent_stats[:total_tasks_this_month] %></div>
          </div>
        </div>
      </div>

      <!-- Platform Health -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Platform Health</h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Overall Health</span>
            <span class="text-sm font-medium text-gray-900"><%= @platform_health_stats[:health_percentage] %>%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="<%= (@platform_health_stats[:health_percentage] || 0) >= 80 ? 'bg-green-600' : (@platform_health_stats[:health_percentage] || 0) >= 60 ? 'bg-yellow-600' : 'bg-red-600' %> h-2 rounded-full" style="width: <%= @platform_health_stats[:health_percentage] || 0 %>%"></div>
          </div>
          
          <div class="grid grid-cols-2 gap-4 pt-2 border-t border-gray-200">
            <div>
              <div class="text-sm text-gray-600">Connected</div>
              <div class="text-lg font-semibold text-gray-900"><%= @platform_health_stats[:connected_platforms] %></div>
            </div>
            <div>
              <div class="text-sm text-gray-600">Active</div>
              <div class="text-lg font-semibold text-gray-900"><%= @platform_health_stats[:active_platforms] %></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Vibe Analytics -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Vibe Analytics</h3>
        <div class="space-y-4">
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900"><%= @real_time_metrics[:recent_vibe_scores] %></div>
            <div class="text-sm text-gray-600">Average Vibe Score</div>
          </div>
          
          <div class="space-y-2">
            <% @vibe_metrics.each do |metric, value| %>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 capitalize"><%= metric.to_s.humanize %></span>
                <span class="text-sm font-medium text-gray-900"><%= value.is_a?(Numeric) ? value.round(2) : value %></span>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- AI Usage Chart -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
      <h3 class="text-lg font-medium text-gray-900 mb-4">AI Usage & Performance</h3>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="h-64">
          <canvas id="aiUsageChart"></canvas>
        </div>
        <div class="h-64">
          <canvas id="aiPerformanceChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Top Campaigns -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Top Performing Campaigns</h3>
      </div>
      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ROI</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @recent_campaigns.each do |campaign| %>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900"><%= campaign.name %></div>
                  <div class="text-sm text-gray-500"><%= campaign.campaign_type.humanize %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-<%= campaign.status == 'active' ? 'green' : campaign.status == 'paused' ? 'yellow' : 'gray' %>-100 text-<%= campaign.status == 'active' ? 'green' : campaign.status == 'paused' ? 'yellow' : 'gray' %>-800">
                    <%= campaign.status.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  $<%= number_with_delimiter(campaign.budget_cents / 100) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= campaign.performance_score || 'N/A' %>%
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <%= campaign.roi_percentage || 'N/A' %>%
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js Scripts -->
<script>
// Campaign Performance Chart
const campaignCtx = document.getElementById('campaignChart').getContext('2d');
const campaignChart = new Chart(campaignCtx, {
  type: 'line',
  data: {
    labels: <%= raw(['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7'].to_json) %>,
    datasets: [{
      label: 'Conversion Rate',
      data: <%= raw(Array.new(7) { (@performance_metrics[:conversion_rate] || 0) + rand(-1.5..1.5) }.to_json) %>,
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4
    }, {
      label: 'Engagement Rate',
      data: <%= raw(Array.new(7) { (@performance_metrics[:engagement_rate] || 0) + rand(-2.0..2.0) }.to_json) %>,
      borderColor: 'rgb(16, 185, 129)',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        max: 100
      }
    }
  }
});

// Budget Utilization Chart
const budgetCtx = document.getElementById('budgetChart').getContext('2d');
const budgetChart = new Chart(budgetCtx, {
  type: 'doughnut',
  data: {
    labels: ['Spent', 'Remaining'],
    datasets: [{
      data: [<%= @budget_stats[:spent_budget] || 0 %>, <%= @budget_stats[:remaining_budget] || 0 %>],
      backgroundColor: ['rgb(239, 68, 68)', 'rgb(34, 197, 94)'],
      borderWidth: 0
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom'
      }
    }
  }
});

// AI Usage Chart
const aiUsageCtx = document.getElementById('aiUsageChart').getContext('2d');
const aiUsageChart = new Chart(aiUsageCtx, {
  type: 'bar',
  data: {
    labels: <%= raw(Array.new(7) { |i| (7-i).days.ago.strftime('%m/%d') }).to_json %>,
    datasets: [{
      label: 'Requests',
      data: <%= raw(Array.new(7) { (@real_time_metrics[:recent_ai_requests] || 0) + rand(5..20) }).to_json %>,
      backgroundColor: 'rgba(147, 51, 234, 0.8)'
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true
      }
    }
  }
});

// AI Performance Chart
const aiPerformanceCtx = document.getElementById('aiPerformanceChart').getContext('2d');
const aiPerformanceChart = new Chart(aiPerformanceCtx, {
  type: 'line',
  data: {
    labels: <%= raw(Array.new(7) { |i| (7-i).days.ago.strftime('%m/%d') }).to_json %>,
    datasets: [{
      label: 'Response Time (ms)',
      data: <%= raw(Array.new(7) { 200 + rand(50..150) }).to_json %>,
      borderColor: 'rgb(245, 158, 11)',
      backgroundColor: 'rgba(245, 158, 11, 0.1)',
      tension: 0.4
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true
      }
    }
  }
});

// Live Spend Chart Initialization
function initializeLiveSpendChart() {
  const ctx = document.getElementById('liveSpendChart');
  if (!ctx) return;
  
  // Generate time labels for today
  const now = new Date();
  const labels = [];
  for (let i = 23; i >= 0; i--) {
    const time = new Date(now.getTime() - (i * 60 * 60 * 1000));
    labels.push(time.getHours() + ':00');
  }

  new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Actual Spend',
        data: [0, 45, 120, 180, 250, 320, 410, 480, 560, 640, 720, 810, 890, 980, 1070, 1160, 1250, 1340, 1430, 1520, 1610, 1700, 1790, 1847],
        borderColor: '#2563eb',
        backgroundColor: 'rgba(37, 99, 235, 0.1)',
        tension: 0.4,
        fill: false,
        pointRadius: 3,
        pointHoverRadius: 5
      }, {
        label: 'Budget Pace',
        data: [0, 42, 83, 125, 167, 208, 250, 292, 333, 375, 417, 458, 500, 542, 583, 625, 667, 708, 750, 792, 833, 875, 917, 958],
        borderColor: '#06b6d4',
        backgroundColor: 'rgba(6, 182, 212, 0.1)',
        tension: 0.4,
        fill: false,
        borderDash: [5, 5],
        pointRadius: 2
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: { display: false },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          grid: { display: false },
          title: {
            display: true,
            text: 'Time (24h)'
          }
        },
        y: {
          grid: { color: '#f1f5f9' },
          title: {
            display: true,
            text: 'Spend ($)'
          },
          ticks: {
            callback: function(value) {
              return '$' + value;
            }
          }
        }
      },
      interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
      }
    }
  });
}

// Emergency Pause Modal Functionality
function initializeEmergencyPause() {
  const emergencyBtn = document.getElementById('emergency-pause-btn');
  const emergencyModal = document.getElementById('emergency-modal');
  const cancelBtn = document.getElementById('cancel-emergency');
  const confirmBtn = document.getElementById('confirm-emergency');
  
  if (!emergencyBtn || !emergencyModal) return;
  
  emergencyBtn.addEventListener('click', function() {
    emergencyModal.classList.remove('hidden');
    emergencyModal.classList.add('flex');
  });
  
  if (cancelBtn) {
    cancelBtn.addEventListener('click', function() {
      emergencyModal.classList.add('hidden');
      emergencyModal.classList.remove('flex');
    });
  }
  
  if (confirmBtn) {
    confirmBtn.addEventListener('click', function() {
      // Simulate emergency pause
      alert('All campaigns have been paused successfully');
      emergencyModal.classList.add('hidden');
      emergencyModal.classList.remove('flex');
      
      // Update UI to reflect paused state
      const activeCount = document.querySelector('#active-campaigns-count');
      if (activeCount) {
        activeCount.textContent = '0';
      }
    });
  }
}

// Real-time Metric Updates
function updateRealTimeMetrics() {
  const spendRate = document.getElementById('spend-rate');
  const impressionVelocity = document.getElementById('impression-velocity');
  const ctr = document.getElementById('ctr');
  const conversions = document.getElementById('conversions');

  if (spendRate) {
    const currentSpend = parseInt(spendRate.textContent.replace('$', '').replace('/hr', ''));
    const newSpend = currentSpend + Math.floor(Math.random() * 20) - 10;
    spendRate.textContent = `$${newSpend}/hr`;
  }

  if (impressionVelocity) {
    const currentImpressions = parseFloat(impressionVelocity.textContent.replace('K/min', ''));
    const newImpressions = (currentImpressions + (Math.random() * 0.4) - 0.2).toFixed(1);
    impressionVelocity.textContent = `${newImpressions}K/min`;
  }

  if (ctr) {
    const currentCTR = parseFloat(ctr.textContent.replace('%', ''));
    const newCTR = (currentCTR + (Math.random() * 0.1) - 0.05).toFixed(2);
    ctr.textContent = `${newCTR}%`;
  }

  if (conversions) {
    const currentConversions = parseInt(conversions.textContent);
    if (Math.random() > 0.7) { // 30% chance of new conversion
      conversions.textContent = currentConversions + 1;
    }
  }
}

// Add New Alert to Feed
function addNewAlert() {
  const alertFeed = document.getElementById('alert-feed');
  if (!alertFeed) return;
  
  const alerts = [
    { type: 'warning', message: 'Display Campaign impression volume low', level: 'WARNING' },
    { type: 'info', message: 'Email Campaign scheduled to start', level: 'INFO' },
    { type: 'success', message: 'Social Campaign exceeded CTR goal', level: 'SUCCESS' }
  ];

  if (Math.random() > 0.8) { // 20% chance of new alert
    const alert = alerts[Math.floor(Math.random() * alerts.length)];
    const alertElement = document.createElement('div');
    const now = new Date();
    const timeStr = now.getHours() + ':' + now.getMinutes().toString().padStart(2, '0') + ' PM';
    
    const colorClass = alert.type === 'warning' ? 'yellow' : alert.type === 'info' ? 'blue' : 'green';
    
    alertElement.className = `p-3 bg-${colorClass}-50 border-l-4 border-${colorClass}-400 rounded-md`;
    alertElement.innerHTML = `
      <div class="flex items-center justify-between mb-1">
        <span class="text-xs font-medium text-${colorClass}-600">${alert.level}</span>
        <span class="text-xs text-gray-500">${timeStr}</span>
      </div>
      <p class="text-sm text-gray-900">${alert.message}</p>
    `;
    
    alertFeed.insertBefore(alertElement, alertFeed.firstChild);
    
    // Remove oldest alert if more than 5
    if (alertFeed.children.length > 5) {
      alertFeed.removeChild(alertFeed.lastChild);
    }
  }
}

// Initialize Real-time Dashboard
document.addEventListener('turbo:load', function() {
  // Initialize live spend chart
  initializeLiveSpendChart();
  
  // Initialize emergency pause functionality
  initializeEmergencyPause();
  
  // Start real-time updates
  setInterval(updateRealTimeMetrics, 3000); // Update every 3 seconds
  setInterval(addNewAlert, 15000); // Check for new alerts every 15 seconds
  
  // Refresh dashboard data every 30 seconds
  setInterval(function() {
    fetch(window.location.href, {
      headers: {
        'Accept': 'text/vnd.turbo-stream.html'
      }
    })
    .then(response => response.text())
    .then(html => {
      // Update specific sections without full page reload
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      // Update real-time metrics
      const realTimeSection = doc.querySelector('[data-turbo-target="real-time-metrics"]');
      if (realTimeSection) {
        document.querySelector('[data-turbo-target="real-time-metrics"]').innerHTML = realTimeSection.innerHTML;
      }
    })
    .catch(error => console.log('Auto-refresh failed:', error));
  }, 30000);
});
</script>

<!-- Emergency Pause Confirmation Modal -->
<div class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50" id="emergency-modal">
  <div class="bg-white rounded-lg p-6 max-w-md mx-4">
    <div class="flex items-center space-x-3 mb-4">
      <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
      </svg>
      <h3 class="text-lg font-semibold text-gray-900">Emergency Pause All Campaigns</h3>
    </div>
    <p class="text-gray-600 mb-6">This will immediately pause all active campaigns. This action cannot be undone automatically.</p>
    <div class="flex items-center justify-end space-x-3">
      <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors" id="cancel-emergency">
        Cancel
      </button>
      <button class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 transition-colors" id="confirm-emergency">
              Pause All Campaigns
      </button>
    </div>
  </div>
</div>
