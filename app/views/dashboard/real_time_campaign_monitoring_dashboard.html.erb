<% content_for :title, "Real-Time Campaign Monitoring" %>

<!-- Dashboard Header -->
<div class="mb-6">
  <div class="flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Real-Time Campaign Monitoring</h1>
      <p class="text-gray-600">Live performance tracking and campaign monitoring</p>
    </div>
    
    <div class="flex items-center space-x-4">
      <!-- Live Status Indicator -->
      <div class="flex items-center space-x-2">
        <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
        <span class="text-sm font-medium text-green-600">LIVE</span>
        </div>
        
        <!-- Alert Threshold -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">Alert Threshold:</span>
          <select class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>80% Budget</option>
            <option>90% Budget</option>
            <option>95% Budget</option>
          </select>
        </div>
        
        <!-- Emergency Pause Button -->
        <button type="button" class="inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600" id="emergency-pause-btn">
          Emergency Pause All
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main content -->
<div class="flex-1 overflow-auto bg-gray-50">
  <div class="p-6">
    <!-- Page Header -->
    <div class="mb-8">
      <p class="text-gray-600 mb-6">Live oversight and instant control of active campaigns</p>
    </div>

    <!-- Critical Alerts Banner -->
    <% if @campaigns.any? { |c| (c.budget_cents / 100.0) > 0 && ((c.budget_cents / 100.0) * 0.85) < @real_time_metrics[:todays_revenue] } %>
    <div class="mb-6" id="alerts-banner">
      <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-md">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <div>
              <% alert_campaign = @campaigns.find { |c| (c.budget_cents / 100.0) > 0 && ((c.budget_cents / 100.0) * 0.85) < @real_time_metrics[:todays_revenue] } %>
              <h3 class="text-sm font-medium text-red-800">Budget Alert: <%= alert_campaign&.name || 'Campaign' %></h3>
              <p class="text-sm text-red-700">Campaign has exceeded 85% of daily budget at <%= Time.current.strftime('%l:%M %p') %></p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <button class="px-3 py-1 bg-red-600 text-white rounded text-xs font-medium hover:bg-red-700 transition-colors">
              Pause Campaign
            </button>
            <button class="px-3 py-1 bg-yellow-600 text-white rounded text-xs font-medium hover:bg-yellow-700 transition-colors">
              Increase Budget
            </button>
            <button class="p-1 text-red-600 hover:text-red-700 transition-colors" title="Dismiss">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    <% end %>

    <!-- Live Metrics Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-8">
      <!-- Current Spend Rate -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-blue-50 rounded-lg">
            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 <%= @spend_rate_change > 0 ? 'text-green-600' : 'text-red-600' %> text-xs">
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <% if @spend_rate_change > 0 %>
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              <% else %>
              <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              <% end %>
            </svg>
            <span><%= @spend_rate_change > 0 ? '+' : '' %><%= @spend_rate_change %>%</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900" id="spend-rate">$<%= number_with_precision(@real_time_metrics[:todays_revenue] / 24.0, precision: 0) %>/hr</h3>
          <p class="text-gray-600 text-xs">Spend Rate</p>
        </div>
      </div>

      <!-- Impression Velocity -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-cyan-50 rounded-lg">
            <svg class="w-4 h-4 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 <%= @impression_velocity_change > 0 ? 'text-green-600' : 'text-red-600' %> text-xs">
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <% if @impression_velocity_change > 0 %>
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              <% else %>
              <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              <% end %>
            </svg>
            <span><%= @impression_velocity_change > 0 ? '+' : '' %><%= @impression_velocity_change %>%</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900" id="impression-velocity"><%= number_with_delimiter((@real_time_metrics[:todays_impressions] / 1440.0).round) %>/min</h3>
          <p class="text-gray-600 text-xs">Impressions</p>
        </div>
      </div>

      <!-- Click-Through Rate -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-yellow-50 rounded-lg">
            <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 <%= @ctr_change > 0 ? 'text-green-600' : 'text-red-600' %> text-xs">
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <% if @ctr_change > 0 %>
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              <% else %>
              <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              <% end %>
            </svg>
            <span><%= @ctr_change > 0 ? '+' : '' %><%= @ctr_change %>%</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900" id="ctr"><%= number_with_precision(@click_through_rate, precision: 2) %>%</h3>
          <p class="text-gray-600 text-xs">CTR</p>
        </div>
      </div>

      <!-- Conversion Tracking -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-green-50 rounded-lg">
            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 <%= @conversion_change > 0 ? 'text-green-600' : 'text-red-600' %> text-xs">
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <% if @conversion_change > 0 %>
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              <% else %>
              <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              <% end %>
            </svg>
            <span><%= @conversion_change > 0 ? '+' : '' %><%= @conversion_change %>%</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900" id="conversions"><%= @total_conversions %></h3>
          <p class="text-gray-600 text-xs">Conversions</p>
        </div>
      </div>

      <!-- Active Campaigns -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-gray-100 rounded-lg">
            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-gray-600 text-xs">
            <span>Live</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900"><%= @active_campaigns %></h3>
          <p class="text-gray-600 text-xs">Active</p>
        </div>
      </div>

      <!-- Connection Status -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-green-50 rounded-lg">
            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-green-600 text-xs">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>Connected</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900">WebSocket</h3>
          <p class="text-gray-600 text-xs">Status</p>
        </div>
      </div>
    </div>

    <!-- Main Monitoring Area and Alert Panel -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Live Charts Area -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Live Spend Pacing</h3>
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-blue-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Actual Spend</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-cyan-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Budget Pace</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-yellow-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Projected</span>
              </div>
            </div>
          </div>
          <div class="h-80">
            <canvas id="live-spend-chart" class="w-full h-full"></canvas>
          </div>
          
          <!-- Budget Utilization Warning -->
          <% total_budget = @campaigns.sum { |c| c.budget_cents / 100.0 } %>
          <% budget_utilization = total_budget > 0 ? ((@real_time_metrics[:todays_revenue] / total_budget) * 100).round : 0 %>
          <% if budget_utilization > 80 %>
          <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm font-medium text-yellow-800">Budget Utilization: <%= budget_utilization %>% - <%= budget_utilization > 90 ? 'Projected to exceed daily budget soon' : 'Approaching budget limit' %></span>
            </div>
          </div>
          <% end %>
        </div>
      </div>

      <!-- Alert Feed Panel -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Live Alert Feed</h3>
            <button class="text-indigo-600 text-sm hover:text-indigo-500 transition-colors">Clear All</button>
          </div>
          
          <!-- Connection Status -->
          <div class="flex items-center space-x-2 mb-4 p-2 bg-green-50 rounded-md">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span class="text-xs text-green-600 font-medium">Real-time monitoring active</span>
          </div>
          
          <!-- Alert Feed -->
          <div class="space-y-3 max-h-80 overflow-y-auto" id="alert-feed">
            <% 
            # Generate dynamic alerts based on campaign data
            alert_campaigns = @campaigns.select { |c| (c.budget_cents / 100.0) > 0 }
            critical_campaigns = alert_campaigns.select { |c| ((c.budget_cents / 100.0) * 0.85) < (@real_time_metrics[:todays_revenue] / alert_campaigns.count) }
            warning_campaigns = alert_campaigns.select { |c| @click_through_rate < 2.0 }
            %>
            
            <% critical_campaigns.first(2).each do |campaign| %>
            <div class="p-3 bg-red-50 border-l-4 border-red-400 rounded-md">
              <div class="flex items-center justify-between mb-1">
                <span class="text-xs font-medium text-red-600">CRITICAL</span>
                <span class="text-xs text-gray-500"><%= Time.current.strftime('%l:%M %p') %></span>
              </div>
              <p class="text-sm text-gray-900"><%= campaign.name %> exceeded 85% budget</p>
              <button class="text-xs text-red-600 hover:text-red-700 mt-1">Acknowledge</button>
            </div>
            <% end %>

            <% warning_campaigns.first(2).each do |campaign| %>
            <div class="p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded-md">
              <div class="flex items-center justify-between mb-1">
                <span class="text-xs font-medium text-yellow-600">WARNING</span>
                <span class="text-xs text-gray-500"><%= (Time.current - 4.minutes).strftime('%l:%M %p') %></span>
              </div>
              <p class="text-sm text-gray-900"><%= campaign.name %> CTR dropped below 2%</p>
              <button class="text-xs text-yellow-600 hover:text-yellow-700 mt-1">Acknowledge</button>
            </div>
            <% end %>

            <% if @campaigns.where(status: 'active').count > 0 %>
            <div class="p-3 bg-cyan-50 border-l-4 border-cyan-400 rounded-md">
              <div class="flex items-center justify-between mb-1">
                <span class="text-xs font-medium text-cyan-600">INFO</span>
                <span class="text-xs text-gray-500"><%= (Time.current - 6.minutes).strftime('%l:%M %p') %></span>
              </div>
              <p class="text-sm text-gray-900"><%= @campaigns.where(status: 'active').first&.name || 'Campaign' %> resumed</p>
              <button class="text-xs text-cyan-600 hover:text-cyan-700 mt-1">Acknowledge</button>
            </div>
            <% end %>

            <% if @total_conversions > 0 %>
            <div class="p-3 bg-green-50 border-l-4 border-green-400 rounded-md">
              <div class="flex items-center justify-between mb-1">
                <span class="text-xs font-medium text-green-600">SUCCESS</span>
                <span class="text-xs text-gray-500"><%= (Time.current - 9.minutes).strftime('%l:%M %p') %></span>
              </div>
              <p class="text-sm text-gray-900">Campaign hit conversion goal (< conversions today)</p>
              <button class="text-xs text-green-600 hover:text-green-700 mt-1">Acknowledge</button>
            </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Campaign Status Grid -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Campaign Status Monitor</h3>
            <div class="flex items-center space-x-6 mt-2">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                <span class="text-sm text-gray-600">Healthy</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                <span class="text-sm text-gray-600">Warning</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                <span class="text-sm text-gray-600">Critical</span>
              </div>
            </div>
          </div>
          <button class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-500 transition-colors">
            Bulk Actions
          </button>
        </div>
      </div>

      <!-- Desktop Table -->
      <div class="hidden md:block overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input type="checkbox" class="rounded border-gray-300">
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Used</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spend Rate</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quick Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @campaigns.limit(10).each do |campaign| %>
            <%
              # Calculate campaign metrics
              campaign_budget = campaign.budget_cents / 100.0
              campaign_metrics = campaign.campaign_metrics.where(metric_date: Date.current).first
              daily_spend = campaign_metrics&.cost_cents ? campaign_metrics.cost_cents / 100.0 : 0
              budget_used_percent = campaign_budget > 0 ? [(daily_spend / campaign_budget * 100), 100].min.round : 0
              hourly_spend = daily_spend / 24.0
              
              # Determine status
              status_color = case 
                when budget_used_percent >= 85 then 'red'
                when budget_used_percent >= 70 then 'yellow'  
                else 'green'
              end
              
              status_text = case status_color
                when 'red' then 'Critical'
                when 'yellow' then 'Warning'
                else 'Healthy'
              end
              
              # Calculate CTR and conversion rate
              campaign_impressions = campaign_metrics&.impressions || 0
              campaign_clicks = campaign_metrics&.clicks || 0
              campaign_conversions = campaign_metrics&.conversions || 0
              
              campaign_ctr = campaign_impressions > 0 ? ((campaign_clicks.to_f / campaign_impressions) * 100).round(1) : 0
              campaign_conv_rate = campaign_clicks > 0 ? ((campaign_conversions.to_f / campaign_clicks) * 100).round(1) : 0
              
              # Determine campaign type/platform
              platform = if campaign.email_campaign.present?
                'Email • Campaign'
              elsif campaign.social_campaign.present?
                'Social • Multi-Platform'
              elsif campaign.seo_campaign.present?
                'SEO • Organic'
              else
                'Multi-Channel'
              end
            %>
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="rounded border-gray-300">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-<%= status_color %>-400 rounded-full"></div>
                  <span class="text-sm font-medium text-<%= status_color %>-600"><%= status_text %></span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="font-medium text-gray-900"><%= campaign.name %></div>
                <div class="text-sm text-gray-500"><%= platform %></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-2">
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-<%= status_color %>-600 h-2 rounded-full" style="width: <%= budget_used_percent %>%"></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900"><%= budget_used_percent %>%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-900">$<%= number_with_precision(hourly_spend, precision: 0) %>/hr</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm">
                  <div>CTR: <span class="font-medium text-<%= campaign_ctr < 2.0 ? 'red' : 'green' %>-600"><%= campaign_ctr %>%</span></div>
                  <div>Conv: <span class="font-medium text-<%= campaign_conv_rate < 20.0 ? 'yellow' : 'green' %>-600"><%= campaign_conv_rate %>%</span></div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-2">
                  <% if status_color == 'red' %>
                  <button class="px-2 py-1 bg-red-600 text-white rounded text-xs font-medium hover:bg-red-700 transition-colors">
                    Pause
                  </button>
                  <button class="px-2 py-1 bg-yellow-600 text-white rounded text-xs font-medium hover:bg-yellow-700 transition-colors">
                    Adjust
                  </button>
                  <% elsif status_color == 'yellow' %>
                  <button class="px-2 py-1 bg-yellow-600 text-white rounded text-xs font-medium hover:bg-yellow-700 transition-colors">
                    Review
                  </button>
                  <button class="px-2 py-1 bg-indigo-600 text-white rounded text-xs font-medium hover:bg-indigo-700 transition-colors">
                    Adjust
                  </button>
                  <% else %>
                  <button class="px-2 py-1 bg-indigo-600 text-white rounded text-xs font-medium hover:bg-indigo-700 transition-colors">
                    Optimize
                  </button>
                  <button class="px-2 py-1 bg-cyan-600 text-white rounded text-xs font-medium hover:bg-cyan-700 transition-colors">
                    Scale
                  </button>
                  <% end %>
                </div>
              </td>
            </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Emergency Pause Confirmation Modal -->
<div class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50" id="emergency-modal">
  <div class="bg-white rounded-lg p-6 max-w-md mx-4">
    <div class="flex items-center space-x-3 mb-4">
      <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
      </svg>
      <h3 class="text-lg font-semibold text-gray-900">Emergency Pause All Campaigns</h3>
    </div>
    <p class="text-gray-600 mb-6">This will immediately pause all active campaigns. This action cannot be undone automatically.</p>
    <div class="flex items-center justify-end space-x-3">
      <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors" id="cancel-emergency">
        Cancel
      </button>
      <button class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 transition-colors" id="confirm-emergency">
        Pause All Campaigns
      </button>
    </div>
  </div>
</div>

<script>
// Emergency pause modal
document.getElementById('emergency-pause-btn').addEventListener('click', function() {
  document.getElementById('emergency-modal').classList.remove('hidden');
  document.getElementById('emergency-modal').classList.add('flex');
});

document.getElementById('cancel-emergency').addEventListener('click', function() {
  document.getElementById('emergency-modal').classList.add('hidden');
  document.getElementById('emergency-modal').classList.remove('flex');
});

document.getElementById('confirm-emergency').addEventListener('click', function() {
  // Simulate emergency pause
  alert('All campaigns have been paused');
  document.getElementById('emergency-modal').classList.add('hidden');
  document.getElementById('emergency-modal').classList.remove('flex');
});

// Initialize Live Charts
function initializeLiveCharts() {
  const ctx = document.getElementById('live-spend-chart').getContext('2d');
  
  // Generate time labels for today
  const now = new Date();
  const labels = [];
  for (let i = 23; i >= 0; i--) {
    const time = new Date(now.getTime() - (i * 60 * 60 * 1000));
    labels.push(time.getHours() + ':00');
  }

  // Use dynamic data from the controller
  const actualSpendData = <%= @hourly_spend_data.to_json.html_safe %>;
  const budgetPaceData = <%= @hourly_budget_pace.to_json.html_safe %>;
  
  // Calculate projected end-of-day spend based on current trajectory
  const currentHour = new Date().getHours();
  const currentSpend = actualSpendData[currentHour] || 0;
  const projectedEndSpend = currentSpend > 0 ? (currentSpend / (currentHour + 1)) * 24 : null;
  
  // Create projected data array (null for past hours, projected value for end of day)
  const projectedData = new Array(24).fill(null);
  if (projectedEndSpend) {
    projectedData[23] = Math.round(projectedEndSpend);
  }

  new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Actual Spend',
        data: actualSpendData,
        borderColor: '#2563eb',
        backgroundColor: 'rgba(37, 99, 235, 0.1)',
        tension: 0.4,
        fill: false,
        pointRadius: 3,
        pointHoverRadius: 5
      }, {
        label: 'Budget Pace',
        data: budgetPaceData,
        borderColor: '#06b6d4',
        backgroundColor: 'rgba(6, 182, 212, 0.1)',
        tension: 0.4,
        fill: false,
        borderDash: [5, 5],
        pointRadius: 2
      }, {
        label: 'Projected',
        data: projectedData,
        borderColor: '#d97706',
        backgroundColor: 'rgba(217, 119, 6, 0.1)',
        tension: 0.4,
        fill: false,
        pointRadius: 4,
        pointStyle: 'triangle'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: { display: false },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          grid: { display: false },
          title: {
            display: true,
            text: 'Time (24h)'
          }
        },
        y: {
          grid: { color: '#f1f5f9' },
          title: {
            display: true,
            text: 'Spend ($)'
          },
          ticks: {
            callback: function(value) {
              return '$' + value;
            }
          }
        }
      },
      interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
      }
    }
  });
}

// Real-time data updates
function updateRealTimeMetrics() {
  // Simulate real-time updates
  const spendRate = document.getElementById('spend-rate');
  const impressionVelocity = document.getElementById('impression-velocity');
  const ctr = document.getElementById('ctr');
  const conversions = document.getElementById('conversions');

  // Add small random variations
  const currentSpend = parseInt(spendRate.textContent.replace('$', '').replace('/hr', ''));
  const newSpend = currentSpend + Math.floor(Math.random() * 20) - 10;
  spendRate.textContent = `$${newSpend}/hr`;

  const currentImpressions = parseFloat(impressionVelocity.textContent.replace('K/min', ''));
  const newImpressions = (currentImpressions + (Math.random() * 0.4) - 0.2).toFixed(1);
  impressionVelocity.textContent = `${newImpressions}K/min`;

  const currentCTR = parseFloat(ctr.textContent.replace('%', ''));
  const newCTR = (currentCTR + (Math.random() * 0.1) - 0.05).toFixed(2);
  ctr.textContent = `${newCTR}%`;

  const currentConversions = parseInt(conversions.textContent);
  if (Math.random() > 0.7) { // 30% chance of new conversion
    conversions.textContent = currentConversions + 1;
  }
}

// Initialize everything when page loads
document.addEventListener('DOMContentLoaded', function() {
  initializeLiveCharts();
  
  // Start real-time updates
  setInterval(updateRealTimeMetrics, 3000); // Update every 3 seconds
});
</script>