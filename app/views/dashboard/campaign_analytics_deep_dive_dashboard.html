<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Campaign Analytics Deep Dive Dashboard</title>
    <link rel="stylesheet" href="../css/main.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-background font-inter">
    <!-- Header Navigation -->
    <header class="bg-surface border-b border-border sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <svg class="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                    </svg>
                    <h1 class="text-xl font-semibold text-text-primary">MarketingHub</h1>
                </div>
                
                <!-- Navigation -->
                <nav class="hidden md:flex items-center space-x-6">
                    <a href="marketing_performance_overview_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary hover:text-primary transition-micro">Overview</a>
                    <a href="campaign_analytics_deep_dive_dashboard.html" class="px-3 py-2 text-sm font-medium text-primary bg-primary-50 rounded-md">Analytics</a>
                    <a href="real_time_campaign_monitoring_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary hover:text-primary transition-micro">Real-time</a>
                    <a href="executive_marketing_summary_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary hover:text-primary transition-micro">Executive</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button class="md:hidden p-2 rounded-md text-text-secondary hover:text-primary" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div class="md:hidden mt-4 pb-4 border-t border-border hidden" id="mobile-menu">
                <div class="flex flex-col space-y-2 pt-4">
                    <a href="marketing_performance_overview_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary">Overview</a>
                    <a href="campaign_analytics_deep_dive_dashboard.html" class="px-3 py-2 text-sm font-medium text-primary bg-primary-50 rounded-md">Analytics</a>
                    <a href="real_time_campaign_monitoring_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary">Real-time</a>
                    <a href="executive_marketing_summary_dashboard.html" class="px-3 py-2 text-sm font-medium text-text-secondary">Executive</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-6 py-8">
        <!-- Page Header with Advanced Filters -->
        <section class="mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
                <div>
                    <h2 class="text-fluid-3xl font-semibold text-text-primary mb-2">Campaign Analytics Deep Dive</h2>
                    <p class="text-text-secondary">Detailed performance analysis and optimization insights</p>
                </div>
                
                <!-- Bookmark & Export Actions -->
                <div class="flex items-center space-x-3">
                    <button class="flex items-center space-x-2 px-4 py-2 border border-border rounded-md text-sm font-medium text-text-secondary hover:text-primary transition-micro">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                        </svg>
                        <span>Bookmark</span>
                    </button>
                    <button class="px-4 py-2 bg-primary text-white rounded-md text-sm font-medium hover:bg-primary-700 transition-micro">
                        Export Report
                    </button>
                </div>
            </div>

            <!-- Advanced Filtering Controls -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 p-6 bg-surface rounded-lg border border-border">
                <!-- Campaign Selector with Search -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-text-primary">Campaign</label>
                    <div class="relative">
                        <input type="text" placeholder="Search campaigns..." class="w-full pl-10 pr-4 py-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent">
                        <svg class="w-4 h-4 text-text-secondary absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>

                <!-- Audience Segment Picker -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-text-primary">Audience Segment</label>
                    <select class="w-full px-3 py-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>All Segments</option>
                        <option>New Visitors</option>
                        <option>Returning Customers</option>
                        <option>High-Value Users</option>
                        <option>Mobile Users</option>
                    </select>
                </div>

                <!-- Attribution Model Toggle -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-text-primary">Attribution Model</label>
                    <select class="w-full px-3 py-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>Multi-Touch</option>
                        <option>First-Touch</option>
                        <option>Last-Touch</option>
                        <option>Linear</option>
                        <option>Time-Decay</option>
                    </select>
                </div>

                <!-- Comparison Mode -->
                <div class="space-y-2">
                    <label class="text-sm font-medium text-text-primary">Comparison</label>
                    <select class="w-full px-3 py-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>No Comparison</option>
                        <option>Previous Period</option>
                        <option>Previous Year</option>
                        <option>A/B Test Groups</option>
                        <option>Custom Range</option>
                    </select>
                </div>

                <!-- Apply Filters Button -->
                <div class="flex items-end">
                    <button class="w-full px-4 py-2 bg-primary text-white rounded-md text-sm font-medium hover:bg-primary-700 transition-micro">
                        Apply Filters
                    </button>
                </div>
            </div>
        </section>

        <!-- Primary Metrics Strip -->
        <section class="grid grid-cols-1 lg:grid-cols-16 gap-6 mb-8">
            <!-- Conversion Funnel Visualization (4 cols) -->
            <div class="lg:col-span-4">
                <div class="data-card p-6 h-full">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-text-primary">Conversion Funnel</h3>
                        <button class="text-text-secondary hover:text-primary transition-micro" title="Expand">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div class="relative">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-text-secondary">Impressions</span>
                                <span class="text-sm font-medium text-data">2.4M</span>
                            </div>
                            <div class="w-full bg-secondary-100 rounded-full h-3">
                                <div class="bg-primary h-3 rounded-full" style="width: 100%"></div>
                            </div>
                        </div>
                        <div class="relative">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-text-secondary">Clicks</span>
                                <span class="text-sm font-medium text-data">96K</span>
                            </div>
                            <div class="w-full bg-secondary-100 rounded-full h-3">
                                <div class="bg-accent h-3 rounded-full" style="width: 75%"></div>
                            </div>
                            <div class="text-xs text-text-secondary mt-1">4.0% CTR</div>
                        </div>
                        <div class="relative">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-text-secondary">Leads</span>
                                <span class="text-sm font-medium text-data">12.8K</span>
                            </div>
                            <div class="w-full bg-secondary-100 rounded-full h-3">
                                <div class="bg-warning h-3 rounded-full" style="width: 45%"></div>
                            </div>
                            <div class="text-xs text-text-secondary mt-1">13.3% Conv.</div>
                        </div>
                        <div class="relative">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-text-secondary">Customers</span>
                                <span class="text-sm font-medium text-data">3.2K</span>
                            </div>
                            <div class="w-full bg-secondary-100 rounded-full h-3">
                                <div class="bg-success h-3 rounded-full" style="width: 25%"></div>
                            </div>
                            <div class="text-xs text-text-secondary mt-1">25.0% Close</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Acquisition Cost Trends (6 cols) -->
            <div class="lg:col-span-6">
                <div class="data-card p-6 h-full">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-text-primary">CAC Trends</h3>
                        <div class="flex items-center space-x-2">
                            <div class="flex items-center space-x-1 text-success text-sm">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span>-12.3%</span>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="text-2xl font-semibold text-text-primary text-data">$68.42</div>
                        <div class="text-sm text-text-secondary">Average CAC</div>
                    </div>
                    <div class="h-32">
                        <canvas id="cac-trend-chart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>

            <!-- Lifetime Value Projections (6 cols) -->
            <div class="lg:col-span-6">
                <div class="data-card p-6 h-full">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-text-primary">LTV Projections</h3>
                        <div class="flex items-center space-x-2">
                            <div class="flex items-center space-x-1 text-success text-sm">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span>+18.7%</span>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="text-2xl font-semibold text-text-primary text-data">$1,247</div>
                        <div class="text-sm text-text-secondary">Projected 12-month LTV</div>
                    </div>
                    <div class="h-32">
                        <canvas id="ltv-projection-chart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Analysis Area with Tabbed Interface -->
        <section class="data-card p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-6">
                    <button class="tab-button active px-4 py-2 text-sm font-medium border-b-2 border-primary text-primary" data-tab="attribution">
                        Channel Attribution
                    </button>
                    <button class="tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-text-secondary hover:text-primary transition-micro" data-tab="segments">
                        Audience Segments
                    </button>
                    <button class="tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-text-secondary hover:text-primary transition-micro" data-tab="comparison">
                        Performance Comparison
                    </button>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="text-sm text-text-secondary">Last updated: 2 min ago</span>
                    <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                </div>
            </div>

            <!-- Attribution Waterfall Chart Tab -->
            <div id="attribution-tab" class="tab-content">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="lg:col-span-2">
                        <h4 class="text-lg font-medium text-text-primary mb-4">Attribution Waterfall</h4>
                        <div class="h-80">
                            <canvas id="attribution-waterfall-chart" class="w-full h-full"></canvas>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <h4 class="text-lg font-medium text-text-primary">Channel Contribution</h4>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-primary rounded-full"></div>
                                    <span class="text-sm font-medium">Google Ads</span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-data">$284K</div>
                                    <div class="text-xs text-text-secondary">38.2%</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-accent rounded-full"></div>
                                    <span class="text-sm font-medium">Facebook</span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-data">$196K</div>
                                    <div class="text-xs text-text-secondary">26.4%</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-success rounded-full"></div>
                                    <span class="text-sm font-medium">Organic</span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-data">$148K</div>
                                    <div class="text-xs text-text-secondary">19.9%</div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-warning rounded-full"></div>
                                    <span class="text-sm font-medium">Email</span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-data">$115K</div>
                                    <div class="text-xs text-text-secondary">15.5%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Audience Segments Heatmap Tab -->
            <div id="segments-tab" class="tab-content hidden">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div class="lg:col-span-3">
                        <h4 class="text-lg font-medium text-text-primary mb-4">Segment Performance Heatmap</h4>
                        <div class="h-80 bg-secondary-50 rounded-lg flex items-center justify-center">
                            <div class="text-center">
                                <svg class="w-16 h-16 text-secondary-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <p class="text-text-secondary">Interactive heatmap visualization</p>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <h4 class="text-lg font-medium text-text-primary">Top Segments</h4>
                        <div class="space-y-3">
                            <div class="p-3 border border-border rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium">High-Value Mobile</span>
                                    <span class="text-xs bg-success-100 text-success px-2 py-1 rounded-full">Hot</span>
                                </div>
                                <div class="text-xs text-text-secondary">Conv. Rate: 34.2%</div>
                                <div class="text-xs text-text-secondary">Avg. Order: $287</div>
                            </div>
                            <div class="p-3 border border-border rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium">Returning Desktop</span>
                                    <span class="text-xs bg-warning-100 text-warning px-2 py-1 rounded-full">Warm</span>
                                </div>
                                <div class="text-xs text-text-secondary">Conv. Rate: 28.7%</div>
                                <div class="text-xs text-text-secondary">Avg. Order: $195</div>
                            </div>
                            <div class="p-3 border border-border rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium">New Visitors</span>
                                    <span class="text-xs bg-secondary-100 text-secondary px-2 py-1 rounded-full">Cool</span>
                                </div>
                                <div class="text-xs text-text-secondary">Conv. Rate: 12.4%</div>
                                <div class="text-xs text-text-secondary">Avg. Order: $89</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Comparison Tab -->
            <div id="comparison-tab" class="tab-content hidden">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="lg:col-span-2">
                        <h4 class="text-lg font-medium text-text-primary mb-4">Time-Series Comparison</h4>
                        <div class="h-80">
                            <canvas id="comparison-chart" class="w-full h-full"></canvas>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <h4 class="text-lg font-medium text-text-primary">Statistical Significance</h4>
                        <div class="space-y-3">
                            <div class="p-3 border border-border rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium">Conversion Rate</span>
                                    <span class="text-xs bg-success-100 text-success px-2 py-1 rounded-full">95% Conf.</span>
                                </div>
                                <div class="text-xs text-text-secondary">+2.3% improvement</div>
                                <div class="text-xs text-text-secondary">p-value: 0.023</div>
                            </div>
                            <div class="p-3 border border-border rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium">Cost Per Lead</span>
                                    <span class="text-xs bg-warning-100 text-warning px-2 py-1 rounded-full">80% Conf.</span>
                                </div>
                                <div class="text-xs text-text-secondary">-$4.20 reduction</div>
                                <div class="text-xs text-text-secondary">p-value: 0.089</div>
                            </div>
                            <div class="p-3 border border-border rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium">ROAS</span>
                                    <span class="text-xs bg-success-100 text-success px-2 py-1 rounded-full">99% Conf.</span>
                                </div>
                                <div class="text-xs text-text-secondary">+0.8x improvement</div>
                                <div class="text-xs text-text-secondary">p-value: 0.001</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Interactive Cohort Analysis Table -->
        <section class="data-card p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-text-primary">Cohort Analysis</h3>
                <div class="flex items-center space-x-3">
                    <select class="px-3 py-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>Weekly Cohorts</option>
                        <option>Monthly Cohorts</option>
                        <option>Quarterly Cohorts</option>
                    </select>
                    <button class="px-4 py-2 bg-primary text-white rounded-md text-sm font-medium hover:bg-primary-700 transition-micro">
                        Drill Down
                    </button>
                </div>
            </div>

            <!-- Cohort Table -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-border">
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Cohort</th>
                            <th class="text-center py-3 px-4 text-sm font-medium text-text-secondary">Size</th>
                            <th class="text-center py-3 px-4 text-sm font-medium text-text-secondary">Week 0</th>
                            <th class="text-center py-3 px-4 text-sm font-medium text-text-secondary">Week 1</th>
                            <th class="text-center py-3 px-4 text-sm font-medium text-text-secondary">Week 2</th>
                            <th class="text-center py-3 px-4 text-sm font-medium text-text-secondary">Week 3</th>
                            <th class="text-center py-3 px-4 text-sm font-medium text-text-secondary">Week 4</th>
                            <th class="text-center py-3 px-4 text-sm font-medium text-text-secondary">Week 8</th>
                            <th class="text-center py-3 px-4 text-sm font-medium text-text-secondary">Week 12</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-border">
                        <tr class="hover:bg-secondary-50 transition-micro">
                            <td class="py-3 px-4 font-medium text-text-primary">Nov 2024</td>
                            <td class="py-3 px-4 text-center text-data">2,847</td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-success rounded text-white text-xs leading-6">100%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-success rounded text-white text-xs leading-6">68%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-warning rounded text-white text-xs leading-6">45%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-warning rounded text-white text-xs leading-6">38%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-accent rounded text-white text-xs leading-6">32%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-accent rounded text-white text-xs leading-6">24%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-secondary rounded text-white text-xs leading-6">18%</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-secondary-50 transition-micro">
                            <td class="py-3 px-4 font-medium text-text-primary">Oct 2024</td>
                            <td class="py-3 px-4 text-center text-data">3,192</td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-success rounded text-white text-xs leading-6">100%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-success rounded text-white text-xs leading-6">72%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-warning rounded text-white text-xs leading-6">48%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-warning rounded text-white text-xs leading-6">41%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-accent rounded text-white text-xs leading-6">35%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-accent rounded text-white text-xs leading-6">27%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-secondary rounded text-white text-xs leading-6">21%</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-secondary-50 transition-micro">
                            <td class="py-3 px-4 font-medium text-text-primary">Sep 2024</td>
                            <td class="py-3 px-4 text-center text-data">2,956</td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-success rounded text-white text-xs leading-6">100%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-success rounded text-white text-xs leading-6">65%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-warning rounded text-white text-xs leading-6">42%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-warning rounded text-white text-xs leading-6">36%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-accent rounded text-white text-xs leading-6">29%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-accent rounded text-white text-xs leading-6">22%</span>
                            </td>
                            <td class="py-3 px-4 text-center">
                                <span class="inline-block w-12 h-6 bg-secondary rounded text-white text-xs leading-6">16%</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Advanced Features Section -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Predictive Trend Modeling -->
            <div class="data-card p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-text-primary">Predictive Trends</h3>
                    <div class="flex items-center space-x-2">
                        <span class="text-xs bg-accent-100 text-accent px-2 py-1 rounded-full">AI-Powered</span>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="predictive-chart" class="w-full h-full"></canvas>
                </div>
                <div class="mt-4 p-3 bg-secondary-50 rounded-lg">
                    <div class="text-sm font-medium text-text-primary mb-1">Next 30 Days Forecast</div>
                    <div class="text-xs text-text-secondary">Expected leads: 4,200 ± 320 (95% confidence)</div>
                    <div class="text-xs text-text-secondary">Projected spend: $89,500 ± $7,200</div>
                </div>
            </div>

            <!-- Automated Report Scheduling -->
            <div class="data-card p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-text-primary">Report Automation</h3>
                    <button class="text-primary text-sm hover:text-primary-700 transition-micro">Configure</button>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 border border-border rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-text-primary">Weekly Performance</div>
                            <div class="text-xs text-text-secondary">Every Monday at 9:00 AM</div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-success rounded-full"></span>
                            <span class="text-xs text-success">Active</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 border border-border rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-text-primary">Monthly Deep Dive</div>
                            <div class="text-xs text-text-secondary">1st of each month</div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-success rounded-full"></span>
                            <span class="text-xs text-success">Active</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 border border-border rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-text-primary">Executive Summary</div>
                            <div class="text-xs text-text-secondary">Quarterly</div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-warning rounded-full"></span>
                            <span class="text-xs text-warning">Paused</span>
                        </div>
                    </div>
                </div>
                <button class="w-full mt-4 px-4 py-2 border border-primary text-primary rounded-md text-sm font-medium hover:bg-primary-50 transition-micro">
                    + Add New Schedule
                </button>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-surface border-t border-border mt-16">
        <div class="max-w-7xl mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <svg class="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                    </svg>
                    <span class="text-text-primary font-medium">MarketingHub</span>
                </div>
                <p class="text-text-secondary text-sm">© 2025 MarketingHub. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Tab functionality
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');
                
                // Remove active class from all tabs
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active', 'border-primary', 'text-primary');
                    btn.classList.add('border-transparent', 'text-text-secondary');
                });
                
                // Add active class to clicked tab
                this.classList.add('active', 'border-primary', 'text-primary');
                this.classList.remove('border-transparent', 'text-text-secondary');
                
                // Hide all tab contents
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                
                // Show selected tab content
                document.getElementById(tabId + '-tab').classList.remove('hidden');
            });
        });

        // Initialize Charts
        function initializeCharts() {
            // CAC Trend Chart
            const cacCtx = document.getElementById('cac-trend-chart').getContext('2d');
            new Chart(cacCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        data: [78, 72, 69, 65, 68, 64],
                        borderColor: '#1e40af',
                        backgroundColor: 'rgba(30, 64, 175, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    }
                }
            });

            // LTV Projection Chart
            const ltvCtx = document.getElementById('ltv-projection-chart').getContext('2d');
            new Chart(ltvCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        data: [1050, 1120, 1180, 1200, 1230, 1247],
                        borderColor: '#059669',
                        backgroundColor: 'rgba(5, 150, 105, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    }
                }
            });

            // Attribution Waterfall Chart
            const attributionCtx = document.getElementById('attribution-waterfall-chart').getContext('2d');
            new Chart(attributionCtx, {
                type: 'bar',
                data: {
                    labels: ['Google Ads', 'Facebook', 'Organic', 'Email', 'LinkedIn', 'Direct'],
                    datasets: [{
                        label: 'Attribution Value',
                        data: [284000, 196000, 148000, 115000, 89000, 67000],
                        backgroundColor: ['#1e40af', '#0ea5e9', '#059669', '#d97706', '#7c3aed', '#dc2626'],
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { grid: { display: false } },
                        y: { grid: { color: '#f1f5f9' } }
                    }
                }
            });

            // Comparison Chart
            const comparisonCtx = document.getElementById('comparison-chart').getContext('2d');
            new Chart(comparisonCtx, {
                type: 'line',
                data: {
                    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'],
                    datasets: [{
                        label: 'Current Period',
                        data: [24.5, 26.2, 25.8, 27.1, 28.3, 29.1],
                        borderColor: '#1e40af',
                        backgroundColor: 'rgba(30, 64, 175, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Previous Period',
                        data: [22.1, 23.8, 24.2, 24.9, 25.1, 25.7],
                        borderColor: '#64748b',
                        backgroundColor: 'rgba(100, 116, 139, 0.1)',
                        tension: 0.4,
                        borderDash: [5, 5]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { 
                        legend: { 
                            display: true,
                            position: 'top'
                        } 
                    },
                    scales: {
                        x: { grid: { display: false } },
                        y: { grid: { color: '#f1f5f9' } }
                    }
                }
            });

            // Predictive Chart
            const predictiveCtx = document.getElementById('predictive-chart').getContext('2d');
            new Chart(predictiveCtx, {
                type: 'line',
                data: {
                    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Forecast 1', 'Forecast 2', 'Forecast 3', 'Forecast 4'],
                    datasets: [{
                        label: 'Actual',
                        data: [3200, 3450, 3380, 3520, null, null, null, null],
                        borderColor: '#1e40af',
                        backgroundColor: 'rgba(30, 64, 175, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Predicted',
                        data: [null, null, null, 3520, 3680, 3850, 4020, 4200],
                        borderColor: '#0ea5e9',
                        backgroundColor: 'rgba(14, 165, 233, 0.1)',
                        tension: 0.4,
                        borderDash: [5, 5]
                    }, {
                        label: 'Confidence Band',
                        data: [null, null, null, null, 3360, 3530, 3700, 3880],
                        borderColor: 'rgba(14, 165, 233, 0.3)',
                        backgroundColor: 'rgba(14, 165, 233, 0.1)',
                        fill: '+1'
                    }, {
                        label: 'Confidence Band Upper',
                        data: [null, null, null, null, 4000, 4170, 4340, 4520],
                        borderColor: 'rgba(14, 165, 233, 0.3)',
                        backgroundColor: 'rgba(14, 165, 233, 0.1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { 
                        legend: { 
                            display: true,
                            position: 'top'
                        } 
                    },
                    scales: {
                        x: { grid: { display: false } },
                        y: { grid: { color: '#f1f5f9' } }
                    }
                }
            });
        }

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', initializeCharts);
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
<script id="dhws-elementInspector" src="../public/dhws-web-inspector.js"></script>
<script id="dhws-errorTracker" src="../public/dhws-error-tracker.js"></script>
</body>
</html>