<% content_for :title, "Executive Marketing Summary" %>

<!-- Header -->
<div class="bg-white shadow-sm border-b border-gray-200">
  <div class="px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 justify-between items-center">
      <div class="flex items-center">
        <button type="button" class="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500" onclick="toggleMobileSidebar()">
          <span class="sr-only">Open sidebar</span>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
        </button>
        <h1 class="ml-4 lg:ml-0 text-2xl font-semibold text-gray-900">Executive Marketing Summary</h1>
      </div>
      
      <div class="flex items-center space-x-4">
        <!-- Last Updated Timestamp -->
        <div class="flex items-center space-x-2 text-sm text-gray-500">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span>Last updated: Today, 9:15 AM</span>
        </div>
        
        <!-- Export Report button -->
        <button type="button" class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
          <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13 7H7v6h6V7z" />
            <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm12 7V5H5v7h10z" clip-rule="evenodd" />
          </svg>
          Export Report
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main content -->
<div class="flex-1 overflow-auto bg-gray-50">
  <div class="p-6">
    <!-- Page Header -->
    <div class="mb-8">
      <p class="text-gray-600 mb-6">Strategic insights and performance overview for leadership decision-making</p>
      
      <!-- Executive Controls -->
      <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Period Toggle -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Reporting Period</label>
          <div class="flex bg-gray-100 rounded-md p-1">
            <button class="flex-1 px-3 py-2 text-sm font-medium bg-indigo-600 text-white rounded-md transition-colors" id="quarterly-btn">
              Quarterly
            </button>
            <button class="flex-1 px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors" id="monthly-btn">
              Monthly
            </button>
          </div>
        </div>

        <!-- Business Unit Selector -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Business Unit</label>
          <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>All Units</option>
            <option>North America</option>
            <option>Europe</option>
            <option>Asia Pacific</option>
            <option>Enterprise</option>
          </select>
        </div>

        <!-- Manual Refresh -->
        <div class="flex items-end">
          <button class="w-full px-4 py-2 bg-gray-100 text-gray-600 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2" id="refresh-btn">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span>Refresh</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Hero KPI Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Marketing ROI Card -->
      <div class="bg-white rounded-lg shadow-sm p-8 border border-gray-200">
        <div class="flex items-center justify-between mb-6">
          <div class="p-3 bg-green-50 rounded-lg">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-2 text-green-600 text-lg font-medium">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>+18.5%</span>
          </div>
        </div>
        <div>
          <h3 class="text-4xl font-semibold text-gray-900 mb-2">4.2x</h3>
          <p class="text-gray-900 font-medium text-lg mb-1">Marketing ROI</p>
          <p class="text-gray-600 text-sm">Return on marketing investment this quarter</p>
          <!-- Executive Insight -->
          <div class="mt-4 p-3 bg-green-50 rounded-lg">
            <p class="text-sm text-green-600 font-medium">Strong performance across all channels</p>
          </div>
        </div>
      </div>

      <!-- Customer Acquisition Cost Card -->
      <div class="bg-white rounded-lg shadow-sm p-8 border border-gray-200">
        <div class="flex items-center justify-between mb-6">
          <div class="p-3 bg-blue-50 rounded-lg">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-2 text-green-600 text-lg font-medium">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>-12.3%</span>
          </div>
        </div>
        <div>
          <h3 class="text-4xl font-semibold text-gray-900 mb-2">$127</h3>
          <p class="text-gray-900 font-medium text-lg mb-1">Customer Acquisition Cost</p>
          <p class="text-gray-600 text-sm">Average cost to acquire new customers</p>
          <div class="mt-4 p-3 bg-blue-50 rounded-lg">
            <p class="text-sm text-blue-600 font-medium">Efficiency improved through optimization</p>
          </div>
        </div>
      </div>

      <!-- Revenue Attribution Card -->
      <div class="bg-white rounded-lg shadow-sm p-8 border border-gray-200">
        <div class="flex items-center justify-between mb-6">
          <div class="p-3 bg-cyan-50 rounded-lg">
            <svg class="w-8 h-8 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-2 text-green-600 text-lg font-medium">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>+24.7%</span>
          </div>
        </div>
        <div>
          <h3 class="text-4xl font-semibold text-gray-900 mb-2">$2.8M</h3>
          <p class="text-gray-900 font-medium text-lg mb-1">Revenue Attribution</p>
          <p class="text-gray-600 text-sm">Marketing-driven revenue this quarter</p>
          <div class="mt-4 p-3 bg-cyan-50 rounded-lg">
            <p class="text-sm text-cyan-600 font-medium">Exceeding quarterly targets</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Visualization and Supporting Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Primary Visualization -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Marketing Contribution to Revenue Pipeline</h3>
              <p class="text-sm text-gray-600 mt-1">Quarterly trend analysis with milestone annotations</p>
            </div>
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-blue-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Marketing Revenue</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-cyan-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Pipeline Value</span>
              </div>
            </div>
          </div>
          <div class="h-96">
            <canvas id="revenue-pipeline-chart" class="w-full h-full"></canvas>
          </div>
          <!-- Key Milestones -->
          <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-3 bg-gray-50 rounded-lg">
              <p class="text-sm text-gray-600">Q1 Campaign Launch</p>
              <p class="font-medium text-gray-900">+$450K</p>
            </div>
            <div class="text-center p-3 bg-gray-50 rounded-lg">
              <p class="text-sm text-gray-600">Product Launch</p>
              <p class="font-medium text-gray-900">+$720K</p>
            </div>
            <div class="text-center p-3 bg-gray-50 rounded-lg">
              <p class="text-sm text-gray-600">Holiday Campaign</p>
              <p class="font-medium text-gray-900">+$890K</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Supporting Metrics Panel -->
      <div class="space-y-6">
        <!-- Channel Effectiveness Ranking -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Channel Effectiveness</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span class="text-white text-sm font-medium">1</span>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">Search Marketing</p>
                  <p class="text-xs text-gray-500">ROI: 5.2x</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">$1.2M</p>
                <p class="text-xs text-green-600">+28%</p>
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-cyan-600 rounded-lg flex items-center justify-center">
                  <span class="text-white text-sm font-medium">2</span>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">Social Media</p>
                  <p class="text-xs text-gray-500">ROI: 4.1x</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">$890K</p>
                <p class="text-xs text-green-600">+22%</p>
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-cyan-600 h-2 rounded-full" style="width: 72%"></div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                  <span class="text-white text-sm font-medium">3</span>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">Email Marketing</p>
                  <p class="text-xs text-gray-500">ROI: 3.8x</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">$650K</p>
                <p class="text-xs text-green-600">+15%</p>
              </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-green-600 h-2 rounded-full" style="width: 58%"></div>
            </div>
          </div>
        </div>

        <!-- Market Share Indicators -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Market Position</h3>
          <div class="space-y-4">
            <div class="text-center">
              <div class="relative w-24 h-24 mx-auto mb-3">
                <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                  <path class="text-gray-200" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                  <path class="text-blue-600" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="67, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <span class="text-lg font-semibold text-gray-900">67%</span>
                </div>
              </div>
              <p class="text-sm font-medium text-gray-900">Brand Awareness</p>
              <p class="text-xs text-green-600">+5% vs last quarter</p>
            </div>

            <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div class="text-center">
                <p class="text-2xl font-semibold text-gray-900">23%</p>
                <p class="text-xs text-gray-500">Market Share</p>
                <p class="text-xs text-green-600">+2.1%</p>
              </div>
              <div class="text-center">
                <p class="text-2xl font-semibold text-gray-900">8.9</p>
                <p class="text-xs text-gray-500">NPS Score</p>
                <p class="text-xs text-green-600">+0.7</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Strategic Insights Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Strategic Insights & Recommendations</h3>
            <p class="text-sm text-gray-600 mt-1">Key performance drivers and executive action items</p>
          </div>
          <button class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-500 transition-colors">
            Generate Full Report
          </button>
        </div>
      </div>

      <!-- Desktop Table -->
      <div class="hidden md:block overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Insight Category</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Key Finding</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Impact</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recommended Action</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-3">
                  <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span class="font-medium text-gray-900">Channel Performance</span>
                </div>
              </td>
              <td class="px-6 py-4 text-gray-600">Search marketing ROI increased 28% QoQ, outperforming all other channels</td>
              <td class="px-6 py-4">
                <span class="text-green-600 font-medium">+$340K revenue</span>
              </td>
              <td class="px-6 py-4 text-gray-600">Increase search budget allocation by 25% for Q4</td>
              <td class="px-6 py-4">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">High</span>
              </td>
            </tr>
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-3">
                  <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <span class="font-medium text-gray-900">Customer Acquisition</span>
                </div>
              </td>
              <td class="px-6 py-4 text-gray-600">CAC decreased 12% while maintaining lead quality scores above 85%</td>
              <td class="px-6 py-4">
                <span class="text-green-600 font-medium">$180K cost savings</span>
              </td>
              <td class="px-6 py-4 text-gray-600">Scale successful optimization tactics across all channels</td>
              <td class="px-6 py-4">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Medium</span>
              </td>
            </tr>
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-3">
                  <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
                  <span class="font-medium text-gray-900">Market Opportunity</span>
                </div>
              </td>
              <td class="px-6 py-4 text-gray-600">Untapped potential in enterprise segment with 40% higher LTV</td>
              <td class="px-6 py-4">
                <span class="text-blue-600 font-medium">$2.1M potential</span>
              </td>
              <td class="px-6 py-4 text-gray-600">Launch dedicated enterprise marketing initiative</td>
              <td class="px-6 py-4">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">High</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- AI-Powered Insights -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-cyan-50 rounded-lg">
            <svg class="w-6 h-6 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">AI-Powered Insights</h3>
            <p class="text-sm text-gray-600">Automated anomaly detection and opportunity identification</p>
          </div>
        </div>
        <div class="flex items-center space-x-2 text-sm text-gray-500">
          <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span>Live Analysis</span>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Anomalies Detected -->
        <div class="space-y-4">
          <h4 class="font-medium text-gray-900 flex items-center space-x-2">
            <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Anomalies Detected</span>
          </h4>
          <div class="space-y-3">
            <div class="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
              <p class="text-sm font-medium text-yellow-800">Unusual spike in mobile traffic</p>
              <p class="text-xs text-gray-600 mt-1">Mobile conversion rate increased 45% in the last 48 hours, investigate potential cause</p>
            </div>
            <div class="p-3 bg-gray-50 rounded-lg border-l-4 border-gray-300">
              <p class="text-sm font-medium text-gray-900">Email engagement plateau</p>
              <p class="text-xs text-gray-600 mt-1">Open rates have remained flat for 3 weeks, consider A/B testing new subject lines</p>
            </div>
          </div>
        </div>

        <!-- Opportunities Identified -->
        <div class="space-y-4">
          <h4 class="font-medium text-gray-900 flex items-center space-x-2">
            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span>Opportunities Identified</span>
          </h4>
          <div class="space-y-3">
            <div class="p-3 bg-green-50 rounded-lg border-l-4 border-green-400">
              <p class="text-sm font-medium text-green-800">Cross-sell opportunity</p>
              <p class="text-xs text-gray-600 mt-1">High-value customers show 73% interest in premium features, potential $450K revenue</p>
            </div>
            <div class="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
              <p class="text-sm font-medium text-blue-800">Geographic expansion</p>
              <p class="text-xs text-gray-600 mt-1">Strong organic interest from APAC region suggests market readiness for expansion</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Period toggle functionality
document.getElementById('quarterly-btn').addEventListener('click', function() {
  this.classList.add('bg-indigo-600', 'text-white');
  this.classList.remove('text-gray-600');
  document.getElementById('monthly-btn').classList.remove('bg-indigo-600', 'text-white');
  document.getElementById('monthly-btn').classList.add('text-gray-600');
});

document.getElementById('monthly-btn').addEventListener('click', function() {
  this.classList.add('bg-indigo-600', 'text-white');
  this.classList.remove('text-gray-600');
  document.getElementById('quarterly-btn').classList.remove('bg-indigo-600', 'text-white');
  document.getElementById('quarterly-btn').classList.add('text-gray-600');
});

// Refresh button animation
document.getElementById('refresh-btn').addEventListener('click', function() {
  const icon = this.querySelector('svg');
  icon.classList.add('animate-spin');
  setTimeout(() => {
    icon.classList.remove('animate-spin');
  }, 1000);
});

// Initialize Revenue Pipeline Chart
function initializeRevenuePipelineChart() {
  const ctx = document.getElementById('revenue-pipeline-chart').getContext('2d');
  new Chart(ctx, {
    type: 'line',
    data: {
      labels: ['Q1 Jan', 'Q1 Feb', 'Q1 Mar', 'Q2 Apr', 'Q2 May', 'Q2 Jun', 'Q3 Jul', 'Q3 Aug', 'Q3 Sep', 'Q4 Oct', 'Q4 Nov', 'Q4 Dec'],
      datasets: [{
        label: 'Marketing Revenue',
        data: [450, 520, 680, 750, 820, 890, 950, 1100, 1250, 1350, 1450, 1580],
        borderColor: '#2563eb',
        backgroundColor: 'rgba(37, 99, 235, 0.1)',
        tension: 0.4,
        fill: true,
        pointBackgroundColor: '#2563eb',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 6
      }, {
        label: 'Pipeline Value',
        data: [1200, 1350, 1500, 1650, 1800, 1950, 2100, 2300, 2500, 2700, 2850, 3000],
        borderColor: '#06b6d4',
        backgroundColor: 'rgba(6, 182, 212, 0.1)',
        tension: 0.4,
        fill: true,
        pointBackgroundColor: '#06b6d4',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 6
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: { display: false },
        tooltip: {
          mode: 'index',
          intersect: false,
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          titleColor: '#0f172a',
          bodyColor: '#475569',
          borderColor: '#e2e8f0',
          borderWidth: 1,
          cornerRadius: 6,
          callbacks: {
            label: function(context) {
              return context.dataset.label + ': $' + context.parsed.y + 'K';
            }
          }
        }
      },
      scales: {
        x: {
          grid: { display: false },
          ticks: { color: '#64748b' }
        },
        y: {
          grid: { color: '#f1f5f9' },
          ticks: {
            color: '#64748b',
            callback: function(value) {
              return '$' + value + 'K';
            }
          }
        }
      },
      interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
      },
      elements: {
        point: { hoverRadius: 8 }
      }
    }
  });
}

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
  initializeRevenuePipelineChart();
});

// Simulate real-time updates for AI insights
setInterval(() => {
  const liveIndicator = document.querySelector('.animate-pulse');
  if (liveIndicator) {
    liveIndicator.style.opacity = '0.5';
    setTimeout(() => {
      liveIndicator.style.opacity = '1';
    }, 500);
  }
}, 5000);
</script>