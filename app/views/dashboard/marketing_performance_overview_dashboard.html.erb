<% content_for :title, "Marketing Performance Overview" %>

<!-- Header -->
<div class="bg-white shadow-sm border-b border-gray-200">
  <div class="px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 justify-between items-center">
      <div class="flex items-center">
        <button type="button" class="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500" onclick="toggleMobileSidebar()">
          <span class="sr-only">Open sidebar</span>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
        </button>
        <h1 class="ml-4 lg:ml-0 text-2xl font-semibold text-gray-900">Marketing Performance Overview</h1>
      </div>
      
      <div class="flex items-center space-x-4">
        <!-- Auto-refresh Toggle -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">Auto-refresh</span>
          <button class="relative inline-flex h-6 w-11 items-center rounded-full bg-indigo-600 transition-colors" id="auto-refresh-toggle">
            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-6"></span>
          </button>
        </div>
        
        <!-- Export button -->
        <button type="button" class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
          <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
          Export
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main content -->
<div class="flex-1 overflow-auto bg-gray-50">
  <div class="p-6">
    <!-- Page Header -->
    <div class="mb-8">
      <p class="text-gray-600 mb-6">Monitor campaign effectiveness and ROI across all channels</p>
      
      <!-- Global Filters -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Date Range Picker -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Date Range</label>
          <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>Last 7 days</option>
            <option>Last 30 days</option>
            <option>Last 90 days</option>
            <option>MTD</option>
            <option>QTD</option>
            <option>Custom Range</option>
          </select>
        </div>

        <!-- Campaign Type Selector -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Campaign Type</label>
          <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>All Types</option>
            <option>Search Ads</option>
            <option>Display</option>
            <option>Social Media</option>
            <option>Email</option>
          </select>
        </div>

        <!-- Channel Multi-select -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Channels</label>
          <div class="relative">
            <button class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-left bg-white flex items-center justify-between" id="channel-dropdown">
              <span>All Channels</span>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Apply Filters Button -->
        <div class="flex items-end">
          <button class="w-full px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-500 transition-colors">
            Apply Filters
          </button>
        </div>
      </div>
    </div>

    <!-- KPI Cards Row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Spend Card -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <div class="p-2 bg-blue-50 rounded-lg">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-green-600 text-sm">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>+12.5%</span>
          </div>
        </div>
        <div>
          <h3 class="text-2xl font-semibold text-gray-900">$847,392</h3>
          <p class="text-gray-600 text-sm mt-1">Total Spend</p>
          <!-- Mini Sparkline -->
          <div class="mt-3 h-8">
            <canvas id="spend-sparkline" class="w-full h-full"></canvas>
          </div>
        </div>
      </div>

      <!-- Leads Generated Card -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <div class="p-2 bg-cyan-50 rounded-lg">
            <svg class="w-6 h-6 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-green-600 text-sm">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>+8.3%</span>
          </div>
        </div>
        <div>
          <h3 class="text-2xl font-semibold text-gray-900">12,847</h3>
          <p class="text-gray-600 text-sm mt-1">Leads Generated</p>
          <div class="mt-3 h-8">
            <canvas id="leads-sparkline" class="w-full h-full"></canvas>
          </div>
        </div>
      </div>

      <!-- Cost Per Lead Card -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <div class="p-2 bg-yellow-50 rounded-lg">
            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-red-600 text-sm">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>-3.2%</span>
          </div>
        </div>
        <div>
          <h3 class="text-2xl font-semibold text-gray-900">$65.94</h3>
          <p class="text-gray-600 text-sm mt-1">Cost Per Lead</p>
          <div class="mt-3 h-8">
            <canvas id="cpl-sparkline" class="w-full h-full"></canvas>
          </div>
        </div>
      </div>

      <!-- Conversion Rate Card -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <div class="p-2 bg-green-50 rounded-lg">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-green-600 text-sm">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>+15.7%</span>
          </div>
        </div>
        <div>
          <h3 class="text-2xl font-semibold text-gray-900">24.8%</h3>
          <p class="text-gray-600 text-sm mt-1">Conversion Rate</p>
          <div class="mt-3 h-8">
            <canvas id="conversion-sparkline" class="w-full h-full"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Visualization and Sidebar -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Main Chart Area -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Performance Trends</h3>
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-blue-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Spend</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-cyan-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Leads</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Conversions</span>
              </div>
            </div>
          </div>
          <div class="h-80">
            <canvas id="main-chart" class="w-full h-full"></canvas>
          </div>
        </div>
      </div>

      <!-- Right Sidebar -->
      <div class="space-y-6">
        <!-- Channel Performance -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Channel Performance</h3>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-blue-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Google Ads</span>
              </div>
              <span class="text-sm font-medium text-gray-900">42.3%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full" style="width: 42.3%"></div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-cyan-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Facebook Ads</span>
              </div>
              <span class="text-sm font-medium text-gray-900">28.7%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-cyan-600 h-2 rounded-full" style="width: 28.7%"></div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-green-600 rounded-full"></div>
                <span class="text-sm text-gray-600">LinkedIn Ads</span>
              </div>
              <span class="text-sm font-medium text-gray-900">18.9%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-green-600 h-2 rounded-full" style="width: 18.9%"></div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-3 h-3 bg-yellow-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Email</span>
              </div>
              <span class="text-sm font-medium text-gray-900">10.1%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-yellow-600 h-2 rounded-full" style="width: 10.1%"></div>
            </div>
          </div>
        </div>

        <!-- Campaign List -->
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Active Campaigns</h3>
            <button class="text-indigo-600 text-sm hover:text-indigo-500 transition-colors">View All</button>
          </div>
          <div class="space-y-4 max-h-64 overflow-y-auto">
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                <div>
                  <p class="text-sm font-medium text-gray-900">Q4 Product Launch</p>
                  <p class="text-xs text-gray-500">Google Ads • Active</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">$12.4K</p>
                <p class="text-xs text-green-600">+8.2%</p>
              </div>
            </div>

            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                <div>
                  <p class="text-sm font-medium text-gray-900">Holiday Promotion</p>
                  <p class="text-xs text-gray-500">Facebook • Active</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">$8.9K</p>
                <p class="text-xs text-green-600">+15.3%</p>
              </div>
            </div>

            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <div>
                  <p class="text-sm font-medium text-gray-900">B2B Lead Gen</p>
                  <p class="text-xs text-gray-500">LinkedIn • Paused</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">$5.2K</p>
                <p class="text-xs text-red-600">-2.1%</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Campaign Performance Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Top Performing Campaigns</h3>
      </div>
      <div class="overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Channel</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spend</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Leads</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CPL</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conv. Rate</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="font-medium text-gray-900">Q4 Product Launch</div>
                <div class="text-sm text-gray-500">Search campaign targeting product keywords</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Google Ads</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap font-medium">$12,450</td>
              <td class="px-6 py-4 whitespace-nowrap font-medium">189</td>
              <td class="px-6 py-4 whitespace-nowrap font-medium">$65.87</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-green-600 font-medium">24.8%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center space-x-2">
                  <button class="p-1 text-gray-400 hover:text-indigo-600 transition-colors" title="Edit">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </button>
                  <button class="p-1 text-gray-400 hover:text-yellow-600 transition-colors" title="Pause">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js Scripts -->
<script>
// Auto-refresh toggle
document.getElementById('auto-refresh-toggle').addEventListener('click', function() {
  this.classList.toggle('bg-indigo-600');
  this.classList.toggle('bg-gray-300');
  const span = this.querySelector('span');
  span.classList.toggle('translate-x-6');
  span.classList.toggle('translate-x-1');
});

// Initialize Charts
function initializeCharts() {
  // Main Chart
  const mainCtx = document.getElementById('main-chart').getContext('2d');
  new Chart(mainCtx, {
    type: 'bar',
    data: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      datasets: [{
        label: 'Spend',
        data: [65000, 72000, 68000, 75000, 82000, 78000, 85000, 88000, 92000, 89000, 95000, 98000],
        backgroundColor: '#2563eb',
        borderRadius: 4,
        yAxisID: 'y'
      }, {
        label: 'Leads',
        data: [980, 1120, 1050, 1180, 1290, 1220, 1350, 1420, 1480, 1390, 1520, 1580],
        type: 'line',
        borderColor: '#06b6d4',
        backgroundColor: 'rgba(6, 182, 212, 0.1)',
        tension: 0.4,
        yAxisID: 'y1'
      }, {
        label: 'Conversions',
        data: [243, 280, 262, 295, 322, 305, 337, 355, 370, 347, 380, 395],
        type: 'line',
        borderColor: '#059669',
        backgroundColor: 'rgba(5, 150, 105, 0.1)',
        tension: 0.4,
        yAxisID: 'y1'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: { display: false }
      },
      scales: {
        x: { grid: { display: false } },
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          grid: { color: '#f1f5f9' }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          grid: { drawOnChartArea: false }
        }
      }
    }
  });

  // Sparklines
  const sparklineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { legend: { display: false } },
    scales: {
      x: { display: false },
      y: { display: false }
    },
    elements: { point: { radius: 0 } }
  };

  // Spend Sparkline
  new Chart(document.getElementById('spend-sparkline').getContext('2d'), {
    type: 'line',
    data: {
      labels: ['', '', '', '', '', '', ''],
      datasets: [{
        data: [65, 72, 68, 75, 82, 78, 85],
        borderColor: '#2563eb',
        backgroundColor: 'rgba(37, 99, 235, 0.1)',
        tension: 0.4,
        fill: true
      }]
    },
    options: sparklineOptions
  });

  // Leads Sparkline
  new Chart(document.getElementById('leads-sparkline').getContext('2d'), {
    type: 'line',
    data: {
      labels: ['', '', '', '', '', '', ''],
      datasets: [{
        data: [980, 1120, 1050, 1180, 1290, 1220, 1350],
        borderColor: '#06b6d4',
        backgroundColor: 'rgba(6, 182, 212, 0.1)',
        tension: 0.4,
        fill: true
      }]
    },
    options: sparklineOptions
  });

  // CPL Sparkline
  new Chart(document.getElementById('cpl-sparkline').getContext('2d'), {
    type: 'line',
    data: {
      labels: ['', '', '', '', '', '', ''],
      datasets: [{
        data: [66, 64, 65, 64, 64, 64, 63],
        borderColor: '#d97706',
        backgroundColor: 'rgba(217, 119, 6, 0.1)',
        tension: 0.4,
        fill: true
      }]
    },
    options: sparklineOptions
  });

  // Conversion Sparkline
  new Chart(document.getElementById('conversion-sparkline').getContext('2d'), {
    type: 'line',
    data: {
      labels: ['', '', '', '', '', '', ''],
      datasets: [{
        data: [21, 22, 23, 23, 24, 24, 25],
        borderColor: '#059669',
        backgroundColor: 'rgba(5, 150, 105, 0.1)',
        tension: 0.4,
        fill: true
      }]
    },
    options: sparklineOptions
  });
}

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', initializeCharts);

// Simulate auto-refresh
setInterval(() => {
  const autoRefreshToggle = document.getElementById('auto-refresh-toggle');
  if (autoRefreshToggle.classList.contains('bg-indigo-600')) {
    console.log('Data refreshed at:', new Date().toLocaleTimeString());
  }
}, 30000); // 30 seconds
</script>