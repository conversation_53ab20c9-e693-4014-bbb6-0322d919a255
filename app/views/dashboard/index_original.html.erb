<% content_for :title, "Dashboard" %>

<!-- AI Marketing Hub Professional Dashboard -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
  <div class="p-6 space-y-6">
    
    <!-- Dashboard Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
      <div class="mb-4 lg:mb-0">
        <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-600 bg-clip-text text-transparent">
          Marketing Command Center
        </h1>
        <p class="text-gray-600 mt-1 flex items-center space-x-2">
          <span>Welcome back, <%= current_user.first_name %></span>
          <span class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
          <span class="text-sm text-green-600 font-medium">Live</span>
        </p>
      </div>
      
      <!-- Quick Actions -->
      <div class="flex items-center space-x-3">
        <%= link_to new_campaign_path, 
            class: "group relative inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200" do %>
          <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          New Campaign
        <% end %>
        
        <%= link_to vibe_analytics_path, class: "group relative inline-flex items-center px-6 py-3 bg-white border border-gray-200 text-gray-700 font-medium rounded-xl shadow-sm hover:shadow-md hover:bg-gray-50 transition-all duration-200" do %>
          <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          Analytics
        <% end %>
      </div>
    </div>

    <!-- Real-Time Health Monitoring -->
    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span class="text-sm font-medium text-gray-900">System Health: Optimal</span>
          </div>
          
          <!-- Budget Health -->
          <div class="flex items-center space-x-2">
            <% budget_percentage = @budget_stats[:total_budget] > 0 ? (@budget_stats[:spent_budget] / @budget_stats[:total_budget] * 100) : 0 %>
            <div class="w-3 h-3 rounded-full 
              <% if budget_percentage < 75 %>
                bg-green-400
              <% elsif budget_percentage < 90 %>
                bg-yellow-400 animate-pulse
              <% else %>
                bg-red-400 animate-pulse
              <% end %>"></div>
            <span class="text-sm text-gray-600">Budget: <%= budget_percentage.round(1) %>% used</span>
          </div>
          
          <!-- AI Performance Health -->
          <div class="flex items-center space-x-2">
            <% ai_efficiency = @ai_performance_metrics[:efficiency_score] %>
            <div class="w-3 h-3 rounded-full 
              <% if ai_efficiency > 80 %>
                bg-green-400
              <% elsif ai_efficiency > 50 %>
                bg-yellow-400
              <% else %>
                bg-red-400 animate-pulse
              <% end %>"></div>
            <span class="text-sm text-gray-600">AI Efficiency: <%= ai_efficiency %>%</span>
          </div>
        </div>
        
        <!-- Quick Alert Summary -->
        <div class="flex items-center space-x-2">
          <% total_alerts = 0 %>
          <% total_alerts += 1 if @performance_metrics[:success_rate] < 50 %>
          <% total_alerts += 1 if @performance_metrics[:engagement_rate] < 3 %>
          <% total_alerts += 1 if @performance_metrics[:cost_per_acquisition] > 100 %>
          <% total_alerts += 1 if budget_percentage > 90 %>
          
          <% if total_alerts > 0 %>
            <div class="flex items-center space-x-1 text-amber-600">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
              </svg>
              <span class="text-sm font-medium"><%= total_alerts %> Alert<%= total_alerts > 1 ? 's' : '' %></span>
            </div>
          <% else %>
            <div class="flex items-center space-x-1 text-green-600">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span class="text-sm font-medium">All Systems Healthy</span>
            </div>
          <% end %>
          
          <span class="text-xs text-gray-500">Last updated: <%= Time.current.strftime("%H:%M") %></span>
        </div>
      </div>
    </div>

    <!-- Key Performance Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Campaigns -->
      <div class="dashboard-card group relative bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg hover:border-blue-200 transition-all duration-300">
        <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-400/10 to-blue-600/10 rounded-full -mr-10 -mt-10"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center float-icon">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
              </svg>
            </div>
            <span class="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">Total</span>
          </div>
          <div class="space-y-1">
            <h3 class="text-3xl font-bold text-gray-900"><%= @campaign_stats[:total] %></h3>
            <p class="text-sm text-gray-600">Campaigns</p>
            <% if @campaign_stats[:total] > 0 %>
              <div class="flex items-center space-x-1 text-xs">
                <span class="text-green-600">↗ Active</span>
                <span class="text-gray-500"><%= @campaign_stats[:active] %> running</span>
              </div>
            <% else %>
              <div class="text-xs text-gray-500">Get started</div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Active Campaigns -->
      <div class="dashboard-card group relative bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg hover:border-green-200 transition-all duration-300">
        <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400/10 to-green-600/10 rounded-full -mr-10 -mt-10"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center float-icon pulse-indicator">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </div>
            <span class="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full animate-pulse">Live</span>
          </div>
          <div class="space-y-1">
            <h3 class="text-3xl font-bold text-gray-900"><%= @campaign_stats[:active] %></h3>
            <p class="text-sm text-gray-600">Active Now</p>
            <% if @campaign_stats[:active] > 0 %>
              <div class="flex items-center space-x-1 text-xs">
                <span class="text-green-600">● Live</span>
                <span class="text-gray-500">campaigns running</span>
              </div>
            <% else %>
              <div class="text-xs text-gray-500">No active campaigns</div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Total Budget -->
      <div class="dashboard-card group relative bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg hover:border-purple-200 transition-all duration-300">
        <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-400/10 to-purple-600/10 rounded-full -mr-10 -mt-10"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center float-icon">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
              </svg>
            </div>
            <span class="text-xs font-medium text-purple-600 bg-purple-50 px-2 py-1 rounded-full">Budget</span>
          </div>
          <div class="space-y-1">
            <h3 class="text-3xl font-bold text-gray-900"><%= number_to_currency(@budget_stats[:total_budget]) %></h3>
            <p class="text-sm text-gray-600">Total Budget</p>
            <div class="w-full bg-gray-200 rounded-full h-2 progress-bar">
              <div class="bg-gradient-to-r from-purple-500 to-purple-600 h-2 rounded-full" 
                   style="width: <%= (@budget_stats[:spent_budget] / [@budget_stats[:total_budget], 1].max * 100).round(1) %>%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Vibe Score -->
      <div class="dashboard-card group relative bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg hover:border-pink-200 transition-all duration-300">
        <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-pink-400/10 to-pink-600/10 rounded-full -mr-10 -mt-10"></div>
        <div class="relative">
          <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center float-icon">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
              </svg>
            </div>
            <span class="text-xs font-medium text-pink-600 bg-pink-50 px-2 py-1 rounded-full">Vibe</span>
          </div>
          <div class="space-y-1">
            <h3 class="text-3xl font-bold text-gray-900"><%= @vibe_metrics[:overall_vibe_score].round(1) %></h3>
            <p class="text-sm text-gray-600">Vibe Score</p>
            <% if @vibe_metrics[:total_analyzed] > 0 %>
              <div class="flex items-center space-x-1 text-xs">
                <span class="text-purple-600">♡ Analyzed</span>
                <span class="text-gray-500"><%= @vibe_metrics[:total_analyzed] %> campaigns</span>
              </div>
            <% else %>
              <div class="text-xs text-gray-500">No vibe data yet</div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Left Column - Performance & Analytics -->
      <div class="lg:col-span-2 space-y-6">
        
        <!-- Performance Overview Chart -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100" 
             data-controller="performance-chart" 
             data-performance-chart-timeframe-value="30">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Performance Overview</h3>
              <p class="text-sm text-gray-600">Campaign metrics for the last 30 days</p>
            </div>
            <div class="flex items-center space-x-2">
              <select class="text-sm border border-gray-200 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                      data-action="change->performance-chart#timeframeChanged">
                <option value="30">Last 30 days</option>
                <option value="7">Last 7 days</option>
                <option value="90">Last 90 days</option>
              </select>
            </div>
          </div>

          <!-- Performance Metrics Grid -->
          <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="text-center p-4 bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors relative">
              <div class="text-2xl font-bold text-blue-600"><%= @performance_metrics[:success_rate] %>%</div>
              <div class="text-sm text-gray-600">Success Rate</div>
              <div class="text-xs text-blue-500 mt-1 flex items-center justify-center">
                <% trend_change = 2.3 %>
                <span class="<%= trend_change > 0 ? 'text-green-500' : 'text-red-500' %>">
                  <%= trend_change > 0 ? '↗' : '↘' %> <%= trend_change > 0 ? '+' : '' %><%= trend_change %>% from last period
                </span>
              </div>
              <% if @performance_metrics[:success_rate] < 50 %>
                <div class="absolute top-2 right-2 w-2 h-2 bg-red-400 rounded-full animate-pulse" title="Below target threshold"></div>
              <% end %>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-xl hover:bg-green-100 transition-colors relative">
              <div class="text-2xl font-bold text-green-600"><%= @performance_metrics[:avg_roi] %>%</div>
              <div class="text-sm text-gray-600">Avg ROI</div>
              <div class="text-xs text-green-500 mt-1 flex items-center justify-center">
                <% roi_trend = 15.7 %>
                <span class="<%= roi_trend > 0 ? 'text-green-500' : 'text-red-500' %>">
                  <%= roi_trend > 0 ? '↗' : '↘' %> <%= roi_trend > 0 ? '+' : '' %><%= roi_trend %>% from last period
                </span>
              </div>
              <% if @performance_metrics[:avg_roi] > 300 %>
                <div class="absolute top-2 right-2 w-2 h-2 bg-green-400 rounded-full animate-pulse" title="Excellent performance!"></div>
              <% end %>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-xl hover:bg-purple-100 transition-colors relative">
              <div class="text-2xl font-bold text-purple-600"><%= @performance_metrics[:engagement_rate] %>%</div>
              <div class="text-sm text-gray-600">Engagement</div>
              <div class="text-xs text-purple-500 mt-1 flex items-center justify-center">
                <% engagement_trend = 8.1 %>
                <span class="<%= engagement_trend > 0 ? 'text-green-500' : 'text-red-500' %>">
                  <%= engagement_trend > 0 ? '↗' : '↘' %> <%= engagement_trend > 0 ? '+' : '' %><%= engagement_trend %>% from last period
                </span>
              </div>
              <% if @performance_metrics[:engagement_rate] < 3 %>
                <div class="absolute top-2 right-2 w-2 h-2 bg-yellow-400 rounded-full animate-pulse" title="Low engagement warning"></div>
              <% end %>
            </div>
            <div class="text-center p-4 bg-orange-50 rounded-xl hover:bg-orange-100 transition-colors relative">
              <div class="text-2xl font-bold text-orange-600">$<%= @performance_metrics[:cost_per_acquisition] %></div>
              <div class="text-sm text-gray-600">Cost per Lead</div>
              <div class="text-xs text-orange-500 mt-1 flex items-center justify-center">
                <% cpa_trend = -12.4 %>
                <span class="<%= cpa_trend < 0 ? 'text-green-500' : 'text-red-500' %>">
                  <%= cpa_trend < 0 ? '↘' : '↗' %> <%= cpa_trend %>% from last period
                </span>
              </div>
              <% if @performance_metrics[:cost_per_acquisition] > 100 %>
                <div class="absolute top-2 right-2 w-2 h-2 bg-red-400 rounded-full animate-pulse" title="High acquisition cost alert"></div>
              <% end %>
            </div>
          </div>

          <!-- Interactive Performance Chart -->
          <div class="h-64 relative">
            <canvas data-performance-chart-target="canvas" class="w-full h-full"></canvas>
            <!-- Fallback for when chart doesn't load -->
            <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl flex items-center justify-center chart-fallback">
              <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                </div>
                <p class="text-gray-600 font-medium">Loading Performance Chart</p>
                <p class="text-sm text-gray-500">Interactive analytics loading...</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Vibe Marketing Analytics -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <span class="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                  </svg>
                </span>
                Vibe Marketing Analytics
              </h3>
              <p class="text-sm text-gray-600">AI-powered emotional intelligence insights</p>
            </div>
            <span class="text-xs font-medium text-purple-600 bg-purple-50 px-3 py-1 rounded-full">AI Powered</span>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Emotional Resonance -->
            <div class="text-center">
              <div class="w-20 h-20 mx-auto mb-3 relative">
                <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                  <path class="text-gray-200" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                  <path class="text-pink-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="<%= (@emotional_resonance[:resonance_score] * 10).round(1) %>, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <span class="text-lg font-bold text-gray-900"><%= @emotional_resonance[:resonance_score].round(1) %></span>
                </div>
              </div>
              <h4 class="font-semibold text-gray-900">Emotional Resonance</h4>
              <p class="text-sm text-gray-600">Primary: <%= @emotional_resonance[:primary_emotion] %></p>
            </div>

            <!-- Cultural Alignment -->
            <div class="text-center">
              <div class="w-20 h-20 mx-auto mb-3 relative">
                <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                  <path class="text-gray-200" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                  <path class="text-blue-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="<%= (@cultural_alignment[:alignment_score] * 10).round(1) %>, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <span class="text-lg font-bold text-gray-900"><%= @cultural_alignment[:alignment_score].round(1) %></span>
                </div>
              </div>
              <h4 class="font-semibold text-gray-900">Cultural Alignment</h4>
              <p class="text-sm text-gray-600"><%= @cultural_alignment[:cultural_fit_rating] %></p>
            </div>

            <!-- Authenticity Score -->
            <div class="text-center">
              <div class="w-20 h-20 mx-auto mb-3 relative">
                <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                  <path class="text-gray-200" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                  <path class="text-green-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="<%= (@authenticity_scores[:average_score] * 10).round(1) %>, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <span class="text-lg font-bold text-gray-900"><%= @authenticity_scores[:average_score].round(1) %></span>
                </div>
              </div>
              <h4 class="font-semibold text-gray-900">Authenticity</h4>
              <p class="text-sm text-gray-600"><%= @authenticity_scores[:risk_assessment] %> Risk</p>
            </div>
          </div>

          <!-- Trending Topics -->
          <div class="mt-6 pt-6 border-t border-gray-100">
            <h4 class="text-sm font-semibold text-gray-900 mb-3">Trending Cultural Moments</h4>
            <div class="flex flex-wrap gap-2">
              <% if @cultural_alignment[:trending_topics].any? %>
                <% @cultural_alignment[:trending_topics].each do |topic| %>
                  <span class="px-3 py-1 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 text-sm rounded-full">
                    <%= topic %>
                  </span>
                <% end %>
              <% else %>
                <span class="px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full">
                  No trending moments detected
                </span>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Customer Lifecycle Metrics -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <span class="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                  </svg>
                </span>
                Customer Lifecycle Analytics
              </h3>
              <p class="text-sm text-gray-600">Acquisition, retention, and lifetime value insights</p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Customer Acquisition Cost -->
            <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl">
              <div class="text-2xl font-bold text-blue-600">
                $<%= @lifecycle_metrics[:customer_acquisition_cost] %>
              </div>
              <div class="text-sm text-gray-600">Customer Acquisition Cost</div>
              <div class="text-xs text-blue-500 mt-1">
                <% if @lifecycle_metrics[:total_customers_acquired] > 0 %>
                  <%= @lifecycle_metrics[:total_customers_acquired] %> customers acquired
                <% else %>
                  No customers yet
                <% end %>
              </div>
            </div>

            <!-- Customer Lifetime Value -->
            <div class="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
              <div class="text-2xl font-bold text-green-600">
                $<%= @lifecycle_metrics[:customer_lifetime_value] %>
              </div>
              <div class="text-sm text-gray-600">Customer Lifetime Value</div>
              <div class="text-xs text-green-500 mt-1">
                <% if @lifecycle_metrics[:average_order_value] > 0 %>
                  $<%= @lifecycle_metrics[:average_order_value] %> avg order value
                <% else %>
                  No orders yet
                <% end %>
              </div>
            </div>

            <!-- LTV:CAC Ratio -->
            <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl">
              <div class="text-2xl font-bold text-purple-600">
                <%= @lifecycle_metrics[:ltv_to_cac_ratio] %>:1
              </div>
              <div class="text-sm text-gray-600">LTV:CAC Ratio</div>
              <div class="text-xs mt-1 
                <% if @lifecycle_metrics[:ltv_to_cac_ratio] >= 3.0 %>
                  text-green-500
                <% elsif @lifecycle_metrics[:ltv_to_cac_ratio] >= 1.0 %>
                  text-yellow-500
                <% else %>
                  text-red-500
                <% end %>">
                <% if @lifecycle_metrics[:ltv_to_cac_ratio] >= 3.0 %>
                  ✓ Excellent ratio
                <% elsif @lifecycle_metrics[:ltv_to_cac_ratio] >= 1.0 %>
                  ⚠ Needs improvement
                <% else %>
                  ⚠ Critical - focus on retention
                <% end %>
              </div>
            </div>
          </div>

          <div class="mt-6 pt-6 border-t border-gray-100">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">Total Revenue:</span>
                <span class="font-medium">$<%= @lifecycle_metrics[:total_revenue] %></span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Total Customers:</span>
                <span class="font-medium"><%= @lifecycle_metrics[:total_customers_acquired] %></span>
              </div>
            </div>
          </div>
        </div>

        <!-- Content Performance Analytics -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <span class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"/>
                  </svg>
                </span>
                Content Performance Intelligence
              </h3>
              <p class="text-sm text-gray-600">Email, social, and engagement analytics</p>
            </div>
            <span class="text-xs font-medium text-orange-600 bg-orange-50 px-3 py-1 rounded-full">Live Analytics</span>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <!-- Engagement Score -->
            <div class="text-center p-4 bg-gradient-to-br from-orange-50 to-red-50 rounded-xl">
              <div class="text-2xl font-bold text-orange-600">
                <%= @content_metrics[:average_engagement_score] %>/10
              </div>
              <div class="text-sm text-gray-600">Engagement Score</div>
            </div>

            <!-- Email Performance -->
            <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
              <div class="text-2xl font-bold text-blue-600">
                <%= @content_metrics[:email_performance][:click_through_rate] %>%
              </div>
              <div class="text-sm text-gray-600">Email CTR</div>
              <div class="text-xs text-blue-500 mt-1">
                <%= @content_metrics[:email_performance][:total_opens] %> opens
              </div>
            </div>

            <!-- Social Virality -->
            <div class="text-center p-4 bg-gradient-to-br from-green-50 to-teal-50 rounded-xl">
              <div class="text-2xl font-bold text-green-600">
                <%= @content_metrics[:average_viral_coefficient] %>%
              </div>
              <div class="text-sm text-gray-600">Viral Coefficient</div>
              <div class="text-xs text-green-500 mt-1">
                <%= @content_metrics[:total_viral_shares] %> shares
              </div>
            </div>

            <!-- Engagement Diversity -->
            <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl">
              <div class="text-2xl font-bold text-purple-600">
                <%= @content_metrics[:social_performance][:engagement_diversity] %>%
              </div>
              <div class="text-sm text-gray-600">Diversity Score</div>
            </div>
          </div>

          <!-- Content Breakdown -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="text-sm font-semibold text-gray-900 mb-3">Email Performance</h4>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Total Opens:</span>
                  <span class="font-medium"><%= number_with_delimiter(@content_metrics[:email_performance][:total_opens]) %></span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Total Clicks:</span>
                  <span class="font-medium"><%= number_with_delimiter(@content_metrics[:email_performance][:total_clicks]) %></span>
                </div>
              </div>
            </div>

            <div>
              <h4 class="text-sm font-semibold text-gray-900 mb-3">Social Performance</h4>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Total Engagements:</span>
                  <span class="font-medium"><%= number_with_delimiter(@content_metrics[:social_performance][:total_engagements]) %></span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Comments:</span>
                  <span class="font-medium"><%= number_with_delimiter(@content_metrics[:social_performance][:total_comments]) %></span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Performance & ROI Analytics -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <span class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                  </svg>
                </span>
                AI Performance & ROI
              </h3>
              <p class="text-sm text-gray-600">Model efficiency and return on AI investment</p>
            </div>
            <div class="text-right">
              <div class="text-sm text-gray-600">Top Model:</div>
              <div class="text-lg font-bold text-indigo-600"><%= @ai_performance_metrics[:top_performing_model] %></div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <!-- AI ROI -->
            <div class="text-center p-4 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl">
              <div class="text-2xl font-bold 
                <% if @ai_performance_metrics[:ai_roi_percentage] > 200 %>
                  text-green-600
                <% elsif @ai_performance_metrics[:ai_roi_percentage] > 100 %>
                  text-blue-600
                <% else %>
                  text-orange-600
                <% end %>">
                <%= @ai_performance_metrics[:ai_roi_percentage] %>%
              </div>
              <div class="text-sm text-gray-600">AI ROI</div>
            </div>

            <!-- Campaign Success Rate -->
            <div class="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
              <div class="text-2xl font-bold text-green-600">
                <%= @ai_performance_metrics[:campaign_success_rate] %>%
              </div>
              <div class="text-sm text-gray-600">Success Rate</div>
            </div>

            <!-- Cost per Success -->
            <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl">
              <div class="text-2xl font-bold text-blue-600">
                $<%= @ai_performance_metrics[:cost_per_successful_campaign] %>
              </div>
              <div class="text-sm text-gray-600">Cost per Success</div>
            </div>

            <!-- Efficiency Score -->
            <div class="text-center p-4 bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl">
              <div class="text-2xl font-bold text-yellow-600">
                <%= @ai_performance_metrics[:efficiency_score] %>
              </div>
              <div class="text-sm text-gray-600">Efficiency Score</div>
            </div>
          </div>

          <!-- Model Performance Breakdown -->
          <% if @ai_performance_metrics[:model_performance].any? %>
            <div class="border-t border-gray-100 pt-4">
              <h4 class="text-sm font-semibold text-gray-900 mb-3">Model Performance Breakdown</h4>
              <div class="space-y-3">
                <% @ai_performance_metrics[:model_performance].first(3).each do |model, stats| %>
                  <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div class="font-medium text-gray-900"><%= model.humanize %></div>
                      <div class="text-sm text-gray-600"><%= stats[:total_requests] %> requests</div>
                    </div>
                    <div class="text-right">
                      <div class="font-medium">$<%= sprintf("%.4f", stats[:avg_cost_per_request]) %></div>
                      <div class="text-sm text-gray-600">avg cost</div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>

        <!-- AI Usage & Budget Tracking -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <span class="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                  </svg>
                </span>
                AI Usage & Budget
              </h3>
              <p class="text-sm text-gray-600">Smart resource management</p>
            </div>
            <div class="text-right">
              <div class="text-sm text-gray-600">Remaining Budget</div>
              <div class="text-xl font-bold text-gray-900"><%= number_to_currency(@budget_stats[:remaining_budget]) %></div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Budget Breakdown -->
            <div>
              <h4 class="text-sm font-semibold text-gray-900 mb-3">Budget Allocation</h4>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Active Campaigns</span>
                  <span class="font-medium"><%= number_to_currency(@budget_stats[:active_budget]) %></span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full" 
                       style="width: <%= (@budget_stats[:active_budget] / [@budget_stats[:total_budget], 1].max * 100).round(1) %>%"></div>
                </div>
                
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Spent This Month</span>
                  <span class="font-medium"><%= number_to_currency(@budget_stats[:spent_budget]) %></span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full" 
                       style="width: <%= (@budget_stats[:spent_budget] / [@budget_stats[:total_budget], 1].max * 100).round(1) %>%"></div>
                </div>
              </div>
            </div>

            <!-- Platform Distribution -->
            <div>
              <h4 class="text-sm font-semibold text-gray-900 mb-3">Platform Distribution</h4>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span class="text-sm text-gray-600">Email</span>
                  </div>
                  <span class="font-medium"><%= @platform_stats[:email_campaigns] %></span>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span class="text-sm text-gray-600">Social</span>
                  </div>
                  <span class="font-medium"><%= @platform_stats[:social_campaigns] %></span>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-sm text-gray-600">SEO</span>
                  </div>
                  <span class="font-medium"><%= @platform_stats[:seo_campaigns] %></span>
                </div>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span class="text-sm text-gray-600">Multi-Channel</span>
                  </div>
                  <span class="font-medium"><%= @platform_stats[:multi_channel] %></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column - Quick Actions & Recent Activity -->
      <div class="space-y-6">
        
        <!-- AI Model Performance -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
              </svg>
            </span>
            Model Performance
          </h3>
          
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="text-center p-3 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
              <div class="text-xl font-bold text-blue-600">
                <%= @ai_usage_stats[:total_requests] %>
              </div>
              <div class="text-sm text-gray-600">Total Requests</div>
            </div>
            <div class="text-center p-3 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
              <div class="text-xl font-bold text-green-600">
                <%= (@ai_usage_stats[:total_requests] > 0 ? ((@ai_usage_stats[:total_requests] - 2) / @ai_usage_stats[:total_requests].to_f * 100).round(1) : 98.5) %>%
              </div>
              <div class="text-sm text-gray-600">Success Rate</div>
            </div>
          </div>

          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Avg Response Time</span>
              <span class="font-medium">1.2s</span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Cost Efficiency</span>
              <span class="font-medium text-green-600">Optimized</span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Active Models</span>
              <span class="font-medium"><%= @ai_provider_status.count { |p| p[:status] == 'operational' } %></span>
            </div>
          </div>
        </div>

        <!-- Quick Actions Panel -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span class="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </span>
            Quick Actions
          </h3>
          <div class="space-y-3">
            <%= link_to new_campaign_path, 
                class: "flex items-center justify-between w-full p-4 bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 rounded-xl transition-all duration-200 group" do %>
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                  </svg>
                </div>
                <div>
                  <div class="font-medium text-gray-900">New Campaign</div>
                  <div class="text-sm text-gray-600">Create marketing campaign</div>
                </div>
              </div>
              <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            <% end %>

            <%= link_to vibe_analytics_path, 
                class: "flex items-center justify-between w-full p-4 bg-gradient-to-r from-purple-50 to-pink-50 hover:from-purple-100 hover:to-pink-100 rounded-xl transition-all duration-200 group" do %>
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                  </svg>
                </div>
                <div>
                  <div class="font-medium text-gray-900">Vibe Analysis</div>
                  <div class="text-sm text-gray-600">AI emotional insights</div>
                </div>
              </div>
              <svg class="w-5 h-5 text-gray-400 group-hover:text-purple-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            <% end %>

            <%= link_to audiences_path, 
                class: "flex items-center justify-between w-full p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition-all duration-200 group" do %>
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-emerald-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                  </svg>
                </div>
                <div>
                  <div class="font-medium text-gray-900">Audience Insights</div>
                  <div class="text-sm text-gray-600">Manage target groups</div>
                </div>
              </div>
              <svg class="w-5 h-5 text-gray-400 group-hover:text-emerald-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            <% end %>
          </div>
        </div>

        <!-- Recent Campaigns -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Recent Campaigns</h3>
            <%= link_to campaigns_path, class: "text-sm text-blue-600 hover:text-blue-800 font-medium" do %>
              View All
            <% end %>
          </div>
          
          <div class="space-y-4">
            <% @recent_campaigns.each do |campaign| %>
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors group">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-600 rounded-lg flex items-center justify-center">
                    <span class="text-white text-sm font-semibold"><%= campaign.name.first %></span>
                  </div>
                  <div>
                    <div class="font-medium text-gray-900 text-sm"><%= truncate(campaign.name, length: 20) %></div>
                    <div class="text-xs text-gray-600"><%= campaign.campaign_type.titleize %> • <%= time_ago_in_words(campaign.updated_at) %> ago</div>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="status-badge-small <%= campaign.status %>">
                    <%= campaign.status.titleize %>
                  </span>
                  <%= link_to campaign_path(campaign), class: "text-gray-400 hover:text-blue-600 transition-colors" do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- AI Provider Status -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </span>
            AI System Status
          </h3>
          
          <div class="space-y-3">
            <% @ai_provider_status.each do |provider| %>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-<%= provider[:color] %>-400 rounded-full <%= 'animate-pulse' if provider[:status] == 'operational' %>"></div>
                  <span class="text-sm text-gray-700"><%= provider[:name] %></span>
                </div>
                <span class="text-xs text-<%= provider[:color] %>-600 font-medium">
                  <%= provider[:status].humanize %>
                </span>
              </div>
            <% end %>
          </div>

          <div class="mt-4 pt-4 border-t border-gray-100">
            <div class="text-sm text-gray-600">AI Budget Used</div>
            <div class="flex items-center justify-between mt-1">
              <div class="text-lg font-bold text-gray-900"><%= number_to_currency(@ai_usage_stats[:current_month_cost]) %></div>
              <div class="text-sm text-gray-500">/ <%= number_to_currency(@ai_usage_stats[:budget_limit]) %></div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
              <% ai_budget_percentage = @ai_usage_stats[:budget_limit] > 0 ? (@ai_usage_stats[:current_month_cost] / @ai_usage_stats[:budget_limit] * 100).round(1) : 0 %>
              <div class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full" style="width: <%= ai_budget_percentage %>%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* Status badge small */
.status-badge-small {
  @apply inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium;
}

.status-badge-small.active {
  @apply bg-green-100 text-green-800;
}

.status-badge-small.draft {
  @apply bg-yellow-100 text-yellow-800;
}

.status-badge-small.paused {
  @apply bg-orange-100 text-orange-800;
}

.status-badge-small.completed {
  @apply bg-blue-100 text-blue-800;
}

.status-badge-small.cancelled {
  @apply bg-red-100 text-red-800;
}
</style>