<% content_for :title, "Campaign Analytics Deep Dive" %>

<!-- Header -->
<div class="bg-white shadow-sm border-b border-gray-200">
  <div class="px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 justify-between items-center">
      <div class="flex items-center">
        <button type="button" class="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500" onclick="toggleMobileSidebar()">
          <span class="sr-only">Open sidebar</span>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
        </button>
        <h1 class="ml-4 lg:ml-0 text-2xl font-semibold text-gray-900">Campaign Analytics Deep Dive</h1>
      </div>
      
      <div class="flex items-center space-x-3">
        <button class="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
          </svg>
          <span>Bookmark</span>
        </button>
        <button type="button" class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
          Export Report
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main content -->
<div class="flex-1 overflow-auto bg-gray-50">
  <div class="p-6">
    <!-- Page Header -->
    <div class="mb-8">
      <p class="text-gray-600 mb-6">Detailed performance analysis and optimization insights</p>
      
      <!-- Advanced Filtering Controls -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Campaign Selector with Search -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Campaign</label>
          <div class="relative">
            <input type="text" placeholder="Search campaigns..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <svg class="w-4 h-4 text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>

        <!-- Audience Segment Picker -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Audience Segment</label>
          <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>All Segments</option>
            <option>New Visitors</option>
            <option>Returning Customers</option>
            <option>High-Value Users</option>
            <option>Mobile Users</option>
          </select>
        </div>

        <!-- Attribution Model Toggle -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Attribution Model</label>
          <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>Multi-Touch</option>
            <option>First-Touch</option>
            <option>Last-Touch</option>
            <option>Linear</option>
            <option>Time-Decay</option>
          </select>
        </div>

        <!-- Comparison Mode -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Comparison</label>
          <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>No Comparison</option>
            <option>Previous Period</option>
            <option>Previous Year</option>
            <option>A/B Test Groups</option>
            <option>Custom Range</option>
          </select>
        </div>

        <!-- Apply Filters Button -->
        <div class="flex items-end">
          <button class="w-full px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-500 transition-colors">
            Apply Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Primary Metrics Strip -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Conversion Funnel Visualization -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Conversion Funnel</h3>
          <button class="text-gray-400 hover:text-gray-600 transition-colors" title="Expand">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
            </svg>
          </button>
        </div>
        <div class="space-y-4">
          <div class="relative">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-600">Impressions</span>
              <span class="text-sm font-medium text-gray-900"><%= number_to_human(@real_time_metrics[:todays_impressions] || 2400000, format: '%n%u', units: { thousand: 'K', million: 'M' }, precision: 1) %></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div class="bg-blue-600 h-3 rounded-full" style="width: 100%"></div>
            </div>
          </div>
          <div class="relative">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-600">Clicks</span>
              <span class="text-sm font-medium text-gray-900"><%= number_to_human((@real_time_metrics[:todays_impressions] || 2400000) * 0.04, format: '%n%u', units: { thousand: 'K', million: 'M' }, precision: 0) %></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div class="bg-cyan-600 h-3 rounded-full" style="width: 75%"></div>
            </div>
            <div class="text-xs text-gray-500 mt-1">4.0% CTR</div>
          </div>
          <div class="relative">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-600">Leads</span>
              <span class="text-sm font-medium text-gray-900"><%= number_to_human((@real_time_metrics[:todays_conversions] || 47) * 272, format: '%n%u', units: { thousand: 'K', million: 'M' }, precision: 1) %></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div class="bg-yellow-600 h-3 rounded-full" style="width: 45%"></div>
            </div>
            <div class="text-xs text-gray-500 mt-1">13.3% Conv.</div>
          </div>
          <div class="relative">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-gray-600">Customers</span>
              <span class="text-sm font-medium text-gray-900"><%= number_to_human(@real_time_metrics[:todays_conversions] || 3200, format: '%n%u', units: { thousand: 'K', million: 'M' }, precision: 1) %></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div class="bg-green-600 h-3 rounded-full" style="width: 25%"></div>
            </div>
            <div class="text-xs text-gray-500 mt-1">25.0% Close</div>
          </div>
        </div>
      </div>

      <!-- Customer Acquisition Cost Trends -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">CAC Trends</h3>
          <div class="flex items-center space-x-2">
            <div class="flex items-center space-x-1 text-green-600 text-sm">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>-12.3%</span>
            </div>
          </div>
        </div>
        <div class="mb-4">
          <div class="text-2xl font-semibold text-gray-900">$<%= (@lifecycle_metrics[:customer_acquisition_cost] || 68.42).round(2) %></div>
          <div class="text-sm text-gray-600">Average CAC</div>
        </div>
        <div class="h-32">
          <canvas id="cac-trend-chart" class="w-full h-full"></canvas>
        </div>
      </div>

      <!-- Lifetime Value Projections -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">LTV Projections</h3>
          <div class="flex items-center space-x-2">
            <div class="flex items-center space-x-1 text-green-600 text-sm">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span>+18.7%</span>
            </div>
          </div>
        </div>
        <div class="mb-4">
          <div class="text-2xl font-semibold text-gray-900">$<%= number_with_delimiter((@lifecycle_metrics[:customer_lifetime_value] || 1247).round(0)) %></div>
          <div class="text-sm text-gray-600">Projected 12-month LTV</div>
        </div>
        <div class="h-32">
          <canvas id="ltv-projection-chart" class="w-full h-full"></canvas>
        </div>
      </div>
    </div>

    <!-- Main Analysis Area with Tabbed Interface -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-6">
          <button class="tab-button active px-4 py-2 text-sm font-medium border-b-2 border-indigo-600 text-indigo-600" data-tab="attribution">
            Channel Attribution
          </button>
          <button class="tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-900 transition-colors" data-tab="segments">
            Audience Segments
          </button>
          <button class="tab-button px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-900 transition-colors" data-tab="comparison">
            Performance Comparison
          </button>
        </div>
        <div class="flex items-center space-x-3">
          <span class="text-sm text-gray-500">Last updated: 2 min ago</span>
          <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        </div>
      </div>

      <!-- Attribution Waterfall Chart Tab -->
      <div id="attribution-tab" class="tab-content">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div class="lg:col-span-2">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Attribution Waterfall</h4>
            <div class="h-80">
              <canvas id="attribution-waterfall-chart" class="w-full h-full"></canvas>
            </div>
          </div>
          <div class="space-y-4">
            <h4 class="text-lg font-medium text-gray-900">Channel Contribution</h4>
            <div class="space-y-3">
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div class="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <span class="text-sm font-medium">Google Ads</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900">$<%= number_to_human((@budget_stats[:spent_budget] || 0) * 0.382, format: '%n%u', units: { thousand: 'K', million: 'M' }, precision: 0) %></div>
                  <div class="text-xs text-gray-500">38.2%</div>
                </div>
              </div>
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div class="w-3 h-3 bg-cyan-600 rounded-full"></div>
                  <span class="text-sm font-medium">Facebook</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900">$<%= number_to_human((@budget_stats[:spent_budget] || 0) * 0.264, format: '%n%u', units: { thousand: 'K', million: 'M' }, precision: 0) %></div>
                  <div class="text-xs text-gray-500">26.4%</div>
                </div>
              </div>
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div class="w-3 h-3 bg-green-600 rounded-full"></div>
                  <span class="text-sm font-medium">Organic</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900">$<%= number_to_human((@budget_stats[:spent_budget] || 0) * 0.199, format: '%n%u', units: { thousand: 'K', million: 'M' }, precision: 0) %></div>
                  <div class="text-xs text-gray-500">19.9%</div>
                </div>
              </div>
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div class="w-3 h-3 bg-yellow-600 rounded-full"></div>
                  <span class="text-sm font-medium">Email</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900">$<%= number_to_human((@budget_stats[:spent_budget] || 0) * 0.155, format: '%n%u', units: { thousand: 'K', million: 'M' }, precision: 0) %></div>
                  <div class="text-xs text-gray-500">15.5%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Audience Segments Heatmap Tab -->
      <div id="segments-tab" class="tab-content hidden">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div class="lg:col-span-3">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Segment Performance Heatmap</h4>
            <div class="h-80 bg-gray-50 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <p class="text-gray-500">Interactive heatmap visualization</p>
              </div>
            </div>
          </div>
          <div class="space-y-4">
            <h4 class="text-lg font-medium text-gray-900">Top Segments</h4>
            <div class="space-y-3">
              <div class="p-3 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium">High-Value Mobile</span>
                  <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Hot</span>
                </div>
                <div class="text-xs text-gray-500">Conv. Rate: 34.2%</div>
                <div class="text-xs text-gray-500">Avg. Order: $287</div>
              </div>
              <div class="p-3 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium">Returning Desktop</span>
                  <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">Warm</span>
                </div>
                <div class="text-xs text-gray-500">Conv. Rate: 28.7%</div>
                <div class="text-xs text-gray-500">Avg. Order: $195</div>
              </div>
              <div class="p-3 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium">New Visitors</span>
                  <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">Cool</span>
                </div>
                <div class="text-xs text-gray-500">Conv. Rate: 12.4%</div>
                <div class="text-xs text-gray-500">Avg. Order: $89</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Comparison Tab -->
      <div id="comparison-tab" class="tab-content hidden">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div class="lg:col-span-2">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Time-Series Comparison</h4>
            <div class="h-80">
              <canvas id="comparison-chart" class="w-full h-full"></canvas>
            </div>
          </div>
          <div class="space-y-4">
            <h4 class="text-lg font-medium text-gray-900">Statistical Significance</h4>
            <div class="space-y-3">
              <div class="p-3 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium">Conversion Rate</span>
                  <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">95% Conf.</span>
                </div>
                <div class="text-xs text-gray-500">+2.3% improvement</div>
                <div class="text-xs text-gray-500">p-value: 0.023</div>
              </div>
              <div class="p-3 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium">Cost Per Lead</span>
                  <span class="text-xs bg-yellow-100 text-yellow-600 px-2 py-1 rounded-full">80% Conf.</span>
                </div>
                <div class="text-xs text-gray-500">-$4.20 reduction</div>
                <div class="text-xs text-gray-500">p-value: 0.089</div>
              </div>
              <div class="p-3 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium">ROAS</span>
                  <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">99% Conf.</span>
                </div>
                <div class="text-xs text-gray-500">+0.8x improvement</div>
                <div class="text-xs text-gray-500">p-value: 0.001</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Interactive Cohort Analysis Table -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900">Cohort Analysis</h3>
        <div class="flex items-center space-x-3">
          <select class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>Weekly Cohorts</option>
            <option>Monthly Cohorts</option>
            <option>Quarterly Cohorts</option>
          </select>
          <button class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-500 transition-colors">
            Drill Down
          </button>
        </div>
      </div>

      <!-- Cohort Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cohort</th>
              <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
              <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Week 0</th>
              <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Week 1</th>
              <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Week 2</th>
              <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Week 3</th>
              <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Week 4</th>
              <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Week 8</th>
              <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Week 12</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-900">Nov 2024</td>
              <td class="px-6 py-4 whitespace-nowrap text-center text-gray-900">2,847</td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-green-600 rounded text-white text-xs leading-6">100%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-green-600 rounded text-white text-xs leading-6">68%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-yellow-600 rounded text-white text-xs leading-6">45%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-yellow-600 rounded text-white text-xs leading-6">38%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-cyan-600 rounded text-white text-xs leading-6">32%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-cyan-600 rounded text-white text-xs leading-6">24%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-gray-400 rounded text-white text-xs leading-6">18%</span>
              </td>
            </tr>
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-900">Oct 2024</td>
              <td class="px-6 py-4 whitespace-nowrap text-center text-gray-900">3,192</td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-green-600 rounded text-white text-xs leading-6">100%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-green-600 rounded text-white text-xs leading-6">72%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-yellow-600 rounded text-white text-xs leading-6">48%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-yellow-600 rounded text-white text-xs leading-6">41%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-cyan-600 rounded text-white text-xs leading-6">35%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-cyan-600 rounded text-white text-xs leading-6">27%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-gray-400 rounded text-white text-xs leading-6">21%</span>
              </td>
            </tr>
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-900">Sep 2024</td>
              <td class="px-6 py-4 whitespace-nowrap text-center text-gray-900">2,956</td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-green-600 rounded text-white text-xs leading-6">100%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-green-600 rounded text-white text-xs leading-6">65%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-yellow-600 rounded text-white text-xs leading-6">42%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-yellow-600 rounded text-white text-xs leading-6">36%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-cyan-600 rounded text-white text-xs leading-6">29%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-cyan-600 rounded text-white text-xs leading-6">22%</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <span class="inline-block w-12 h-6 bg-gray-400 rounded text-white text-xs leading-6">16%</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Advanced Features Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Predictive Trend Modeling -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Predictive Trends</h3>
          <div class="flex items-center space-x-2">
            <span class="text-xs bg-cyan-100 text-cyan-600 px-2 py-1 rounded-full">AI-Powered</span>
          </div>
        </div>
        <div class="h-64">
          <canvas id="predictive-chart" class="w-full h-full"></canvas>
        </div>
        <div class="mt-4 p-3 bg-gray-50 rounded-lg">
          <div class="text-sm font-medium text-gray-900 mb-1">Next 30 Days Forecast</div>
          <div class="text-xs text-gray-600">Expected leads: 4,200 ± 320 (95% confidence)</div>
          <div class="text-xs text-gray-600">Projected spend: $89,500 ± $7,200</div>
        </div>
      </div>

      <!-- Automated Report Scheduling -->
      <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Report Automation</h3>
          <button class="text-indigo-600 text-sm hover:text-indigo-500 transition-colors">Configure</button>
        </div>
        <div class="space-y-4">
          <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
            <div>
              <div class="text-sm font-medium text-gray-900">Weekly Performance</div>
              <div class="text-xs text-gray-500">Every Monday at 9:00 AM</div>
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-2 h-2 bg-green-400 rounded-full"></span>
              <span class="text-xs text-green-600">Active</span>
            </div>
          </div>
          <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
            <div>
              <div class="text-sm font-medium text-gray-900">Monthly Deep Dive</div>
              <div class="text-xs text-gray-500">1st of each month</div>
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-2 h-2 bg-green-400 rounded-full"></span>
              <span class="text-xs text-green-600">Active</span>
            </div>
          </div>
          <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
            <div>
              <div class="text-sm font-medium text-gray-900">Executive Summary</div>
              <div class="text-xs text-gray-500">Quarterly</div>
            </div>
            <div class="flex items-center space-x-2">
              <span class="w-2 h-2 bg-yellow-400 rounded-full"></span>
              <span class="text-xs text-yellow-600">Paused</span>
            </div>
          </div>
        </div>
        <button class="w-full mt-4 px-4 py-2 border border-indigo-600 text-indigo-600 rounded-md text-sm font-medium hover:bg-indigo-50 transition-colors">
          + Add New Schedule
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Tab functionality
document.querySelectorAll('.tab-button').forEach(button => {
  button.addEventListener('click', function() {
    const tabId = this.getAttribute('data-tab');
    
    // Remove active class from all tabs
    document.querySelectorAll('.tab-button').forEach(btn => {
      btn.classList.remove('active', 'border-indigo-600', 'text-indigo-600');
      btn.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Add active class to clicked tab
    this.classList.add('active', 'border-indigo-600', 'text-indigo-600');
    this.classList.remove('border-transparent', 'text-gray-500');
    
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.add('hidden');
    });
    
    // Show selected tab content
    document.getElementById(tabId + '-tab').classList.remove('hidden');
  });
});

// Initialize Charts
function initializeCharts() {
  // CAC Trend Chart
  const cacCtx = document.getElementById('cac-trend-chart').getContext('2d');
  new Chart(cacCtx, {
    type: 'line',
    data: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [{
        data: <%= raw(Array.new(6) { (@lifecycle_metrics[:customer_acquisition_cost] || 68) + rand(-10..5) }).to_json %>,
        borderColor: '#2563eb',
        backgroundColor: 'rgba(37, 99, 235, 0.1)',
        tension: 0.4,
        fill: true,
        pointRadius: 3
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: { legend: { display: false } },
      scales: {
        x: { display: false },
        y: { display: false }
      }
    }
  });

  // LTV Projection Chart
  const ltvCtx = document.getElementById('ltv-projection-chart').getContext('2d');
  new Chart(ltvCtx, {
    type: 'line',
    data: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [{
        data: <%= raw(Array.new(6) { |i| (@lifecycle_metrics[:customer_lifetime_value] || 1050) + (i * 30) + rand(-20..20) }).to_json %>,
        borderColor: '#059669',
        backgroundColor: 'rgba(5, 150, 105, 0.1)',
        tension: 0.4,
        fill: true,
        pointRadius: 3
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: { legend: { display: false } },
      scales: {
        x: { display: false },
        y: { display: false }
      }
    }
  });

  // Attribution Waterfall Chart
  const attributionCtx = document.getElementById('attribution-waterfall-chart').getContext('2d');
  new Chart(attributionCtx, {
    type: 'bar',
    data: {
      labels: ['Google Ads', 'Facebook', 'Organic', 'Email', 'LinkedIn', 'Direct'],
      datasets: [{
        label: 'Attribution Value',
        data: [<%= (@budget_stats[:spent_budget] || 0) * 0.382 %>, <%= (@budget_stats[:spent_budget] || 0) * 0.264 %>, <%= (@budget_stats[:spent_budget] || 0) * 0.199 %>, <%= (@budget_stats[:spent_budget] || 0) * 0.155 %>, <%= (@budget_stats[:spent_budget] || 0) * 0.12 %>, <%= (@budget_stats[:spent_budget] || 0) * 0.09 %>],
        backgroundColor: ['#2563eb', '#06b6d4', '#059669', '#d97706', '#7c3aed', '#dc2626'],
        borderRadius: 4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: { legend: { display: false } },
      scales: {
        x: { grid: { display: false } },
        y: { grid: { color: '#f1f5f9' } }
      }
    }
  });

  // Comparison Chart
  const comparisonCtx = document.getElementById('comparison-chart').getContext('2d');
  new Chart(comparisonCtx, {
    type: 'line',
    data: {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'],
      datasets: [{
        label: 'Current Period',
        data: [24.5, 26.2, 25.8, 27.1, 28.3, 29.1],
        borderColor: '#2563eb',
        backgroundColor: 'rgba(37, 99, 235, 0.1)',
        tension: 0.4
      }, {
        label: 'Previous Period',
        data: [22.1, 23.8, 24.2, 24.9, 25.1, 25.7],
        borderColor: '#64748b',
        backgroundColor: 'rgba(100, 116, 139, 0.1)',
        tension: 0.4,
        borderDash: [5, 5]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: { 
        legend: { 
          display: true,
          position: 'top'
        } 
      },
      scales: {
        x: { grid: { display: false } },
        y: { grid: { color: '#f1f5f9' } }
      }
    }
  });

  // Predictive Chart
  const predictiveCtx = document.getElementById('predictive-chart').getContext('2d');
  new Chart(predictiveCtx, {
    type: 'line',
    data: {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Forecast 1', 'Forecast 2', 'Forecast 3', 'Forecast 4'],
      datasets: [{
        label: 'Actual',
        data: [3200, 3450, 3380, 3520, null, null, null, null],
        borderColor: '#2563eb',
        backgroundColor: 'rgba(37, 99, 235, 0.1)',
        tension: 0.4
      }, {
        label: 'Predicted',
        data: [null, null, null, 3520, 3680, 3850, 4020, 4200],
        borderColor: '#06b6d4',
        backgroundColor: 'rgba(6, 182, 212, 0.1)',
        tension: 0.4,
        borderDash: [5, 5]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: { 
        legend: { 
          display: true,
          position: 'top'
        } 
      },
      scales: {
        x: { grid: { display: false } },
        y: { grid: { color: '#f1f5f9' } }
      }
    }
  });
}

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', initializeCharts);
</script>