<% content_for :title, "Marketing Performance Overview" %>
<!-- Header -->
<div class="bg-white shadow-sm border-b border-gray-200">
  <div class="px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 justify-between items-center">
      <div class="flex items-center">
        <button type="button" class="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500" onclick="toggleMobileSidebar()">
          <span class="sr-only">Open sidebar</span>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
        </button>
        <h1 class="ml-4 lg:ml-0 text-2xl font-semibold text-gray-900">Marketing Performance Overview</h1>
      </div>
      
      <div class="flex items-center space-x-4">
        <!-- Auto-refresh Toggle -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600">Auto-refresh</span>
          <button class="relative inline-flex h-6 w-11 items-center rounded-full bg-indigo-600 transition-colors" id="auto-refresh-toggle">
            <span class="inline-block h-4 w-4 transform rounded-full bg-white transition translate-x-6"></span>
          </button>
        </div>
        
        <!-- Create button -->
        <button type="button" class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
          <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
          </svg>
          Export
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main content -->
<div class="flex-1 overflow-auto bg-gray-50">
  <div class="p-6">
    <!-- Page Header -->
    <div class="mb-8">
      <p class="text-gray-600 mb-6">Monitor campaign effectiveness and ROI across all channels</p>
      
      <!-- Global Filters -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- Date Range Picker -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Date Range</label>
          <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>Last 7 days</option>
            <option>Last 30 days</option>
            <option>Last 90 days</option>
            <option>MTD</option>
            <option>QTD</option>
            <option>Custom Range</option>
          </select>
        </div>

        <!-- Campaign Type Selector -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Campaign Type</label>
          <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>All Types</option>
            <option>Search Ads</option>
            <option>Display</option>
            <option>Social Media</option>
            <option>Email</option>
          </select>
        </div>

        <!-- Channel Multi-select -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Channels</label>
          <div class="relative">
            <button class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-left bg-white flex items-center justify-between" id="channel-dropdown">
              <span>All Channels</span>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Apply Filters Button -->
        <div class="flex items-end">
          <button class="w-full px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-500 transition-colors">
            Apply Filters
          </button>
        </div>
      </div>
    </div>

        <!-- KPI Cards Row -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Spend Card -->
            <div class="data-card p-6 animate-fade-in">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-2 bg-primary-50 rounded-lg">
                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="flex items-center space-x-1 text-success text-sm">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>+12.5%</span>
                    </div>
                </div>
                <div>
                    <h3 class="text-2xl font-semibold text-text-primary text-data">$847,392</h3>
                    <p class="text-text-secondary text-sm mt-1">Total Spend</p>
                    <!-- Mini Sparkline -->
                    <div class="mt-3 h-8">
                        <canvas id="spend-sparkline" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>

            <!-- Leads Generated Card -->
            <div class="data-card p-6 animate-fade-in" style="animation-delay: 100ms;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-2 bg-accent-50 rounded-lg">
                        <svg class="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="flex items-center space-x-1 text-success text-sm">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>+8.3%</span>
                    </div>
                </div>
                <div>
                    <h3 class="text-2xl font-semibold text-text-primary text-data">12,847</h3>
                    <p class="text-text-secondary text-sm mt-1">Leads Generated</p>
                    <div class="mt-3 h-8">
                        <canvas id="leads-sparkline" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>

            <!-- Cost Per Lead Card -->
            <div class="data-card p-6 animate-fade-in" style="animation-delay: 200ms;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-2 bg-warning-50 rounded-lg">
                        <svg class="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="flex items-center space-x-1 text-error text-sm">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>-3.2%</span>
                    </div>
                </div>
                <div>
                    <h3 class="text-2xl font-semibold text-text-primary text-data">$65.94</h3>
                    <p class="text-text-secondary text-sm mt-1">Cost Per Lead</p>
                    <div class="mt-3 h-8">
                        <canvas id="cpl-sparkline" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>

            <!-- Conversion Rate Card -->
            <div class="data-card p-6 animate-fade-in" style="animation-delay: 300ms;">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-2 bg-success-50 rounded-lg">
                        <svg class="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <div class="flex items-center space-x-1 text-success text-sm">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span>+15.7%</span>
                    </div>
                </div>
                <div>
                    <h3 class="text-2xl font-semibold text-text-primary text-data">24.8%</h3>
                    <p class="text-text-secondary text-sm mt-1">Conversion Rate</p>
                    <div class="mt-3 h-8">
                        <canvas id="conversion-sparkline" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Visualization and Sidebar -->
        <section class="grid grid-cols-1 lg:grid-cols-12 gap-6 mb-8">
            <!-- Main Chart Area (8 cols) -->
            <div class="lg:col-span-8">
                <div class="data-card p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-text-primary">Performance Trends</h3>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-primary rounded-full"></div>
                                <span class="text-sm text-text-secondary">Spend</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-accent rounded-full"></div>
                                <span class="text-sm text-text-secondary">Leads</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-success rounded-full"></div>
                                <span class="text-sm text-text-secondary">Conversions</span>
                            </div>
                        </div>
                    </div>
                    <div class="h-80">
                        <canvas id="main-chart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>

            <!-- Right Sidebar (4 cols) -->
            <div class="lg:col-span-4 space-y-6">
                <!-- Channel Performance -->
                <div class="data-card p-6">
                    <h3 class="text-lg font-semibold text-text-primary mb-4">Channel Performance</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-primary rounded-full"></div>
                                <span class="text-sm text-text-secondary">Google Ads</span>
                            </div>
                            <span class="text-sm font-medium text-text-primary">42.3%</span>
                        </div>
                        <div class="w-full bg-secondary-100 rounded-full h-2">
                            <div class="bg-primary h-2 rounded-full" style="width: 42.3%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-accent rounded-full"></div>
                                <span class="text-sm text-text-secondary">Facebook Ads</span>
                            </div>
                            <span class="text-sm font-medium text-text-primary">28.7%</span>
                        </div>
                        <div class="w-full bg-secondary-100 rounded-full h-2">
                            <div class="bg-accent h-2 rounded-full" style="width: 28.7%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-success rounded-full"></div>
                                <span class="text-sm text-text-secondary">LinkedIn Ads</span>
                            </div>
                            <span class="text-sm font-medium text-text-primary">18.9%</span>
                        </div>
                        <div class="w-full bg-secondary-100 rounded-full h-2">
                            <div class="bg-success h-2 rounded-full" style="width: 18.9%"></div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-warning rounded-full"></div>
                                <span class="text-sm text-text-secondary">Email</span>
                            </div>
                            <span class="text-sm font-medium text-text-primary">10.1%</span>
                        </div>
                        <div class="w-full bg-secondary-100 rounded-full h-2">
                            <div class="bg-warning h-2 rounded-full" style="width: 10.1%"></div>
                        </div>
                    </div>
                </div>

                <!-- Campaign List -->
                <div class="data-card p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-text-primary">Active Campaigns</h3>
                        <button class="text-primary text-sm hover:text-primary-700 transition-micro">View All</button>
                    </div>
                    <div class="space-y-4 max-h-64 overflow-y-auto">
                        <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-success rounded-full"></div>
                                <div>
                                    <p class="text-sm font-medium text-text-primary">Q4 Product Launch</p>
                                    <p class="text-xs text-text-secondary">Google Ads • Active</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-text-primary">$12.4K</p>
                                <p class="text-xs text-success">+8.2%</p>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-success rounded-full"></div>
                                <div>
                                    <p class="text-sm font-medium text-text-primary">Holiday Promotion</p>
                                    <p class="text-xs text-text-secondary">Facebook • Active</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-text-primary">$8.9K</p>
                                <p class="text-xs text-success">+15.3%</p>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-warning rounded-full"></div>
                                <div>
                                    <p class="text-sm font-medium text-text-primary">B2B Lead Gen</p>
                                    <p class="text-xs text-text-secondary">LinkedIn • Paused</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-text-primary">$5.2K</p>
                                <p class="text-xs text-error">-2.1%</p>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-success rounded-full"></div>
                                <div>
                                    <p class="text-sm font-medium text-text-primary">Retargeting Campaign</p>
                                    <p class="text-xs text-text-secondary">Display • Active</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-text-primary">$3.7K</p>
                                <p class="text-xs text-success">+5.8%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Campaign Performance Table -->
        <section class="data-card p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-text-primary">Top Performing Campaigns</h3>
                <div class="flex items-center space-x-3">
                    <div class="relative">
                        <input type="text" placeholder="Search campaigns..." class="pl-10 pr-4 py-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent">
                        <svg class="w-4 h-4 text-text-secondary absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <button class="px-4 py-2 bg-primary text-white rounded-md text-sm font-medium hover:bg-primary-700 transition-micro">
                        Export
                    </button>
                </div>
            </div>

            <!-- Desktop Table -->
            <div class="hidden md:block overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-border">
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Campaign</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Channel</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Spend</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Leads</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">CPL</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Conv. Rate</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Status</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-text-secondary">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-border">
                        <tr class="hover:bg-secondary-50 transition-micro">
                            <td class="py-4 px-4">
                                <div class="font-medium text-text-primary">Q4 Product Launch</div>
                                <div class="text-sm text-text-secondary">Search campaign targeting product keywords</div>
                            </td>
                            <td class="py-4 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary">Google Ads</span>
                            </td>
                            <td class="py-4 px-4 text-data font-medium">$12,450</td>
                            <td class="py-4 px-4 text-data font-medium">189</td>
                            <td class="py-4 px-4 text-data font-medium">$65.87</td>
                            <td class="py-4 px-4">
                                <span class="text-success font-medium">24.8%</span>
                            </td>
                            <td class="py-4 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success">Active</span>
                            </td>
                            <td class="py-4 px-4">
                                <div class="flex items-center space-x-2">
                                    <button class="p-1 text-text-secondary hover:text-primary transition-micro" title="Edit">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button class="p-1 text-text-secondary hover:text-warning transition-micro" title="Pause">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </button>
                                    <button class="p-1 text-text-secondary hover:text-accent transition-micro" title="Duplicate">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-secondary-50 transition-micro">
                            <td class="py-4 px-4">
                                <div class="font-medium text-text-primary">Holiday Promotion</div>
                                <div class="text-sm text-text-secondary">Social media campaign for holiday sales</div>
                            </td>
                            <td class="py-4 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-accent-100 text-accent">Facebook</span>
                            </td>
                            <td class="py-4 px-4 text-data font-medium">$8,920</td>
                            <td class="py-4 px-4 text-data font-medium">142</td>
                            <td class="py-4 px-4 text-data font-medium">$62.82</td>
                            <td class="py-4 px-4">
                                <span class="text-success font-medium">28.2%</span>
                            </td>
                            <td class="py-4 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success">Active</span>
                            </td>
                            <td class="py-4 px-4">
                                <div class="flex items-center space-x-2">
                                    <button class="p-1 text-text-secondary hover:text-primary transition-micro" title="Edit">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button class="p-1 text-text-secondary hover:text-warning transition-micro" title="Pause">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </button>
                                    <button class="p-1 text-text-secondary hover:text-accent transition-micro" title="Duplicate">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-secondary-50 transition-micro">
                            <td class="py-4 px-4">
                                <div class="font-medium text-text-primary">B2B Lead Generation</div>
                                <div class="text-sm text-text-secondary">Professional targeting for enterprise leads</div>
                            </td>
                            <td class="py-4 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success">LinkedIn</span>
                            </td>
                            <td class="py-4 px-4 text-data font-medium">$5,230</td>
                            <td class="py-4 px-4 text-data font-medium">67</td>
                            <td class="py-4 px-4 text-data font-medium">$78.06</td>
                            <td class="py-4 px-4">
                                <span class="text-warning font-medium">18.7%</span>
                            </td>
                            <td class="py-4 px-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning">Paused</span>
                            </td>
                            <td class="py-4 px-4">
                                <div class="flex items-center space-x-2">
                                    <button class="p-1 text-text-secondary hover:text-primary transition-micro" title="Edit">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button class="p-1 text-text-secondary hover:text-success transition-micro" title="Resume">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </button>
                                    <button class="p-1 text-text-secondary hover:text-accent transition-micro" title="Duplicate">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Mobile Cards -->
            <div class="md:hidden space-y-4">
                <div class="border border-border rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-text-primary">Q4 Product Launch</h4>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success">Active</span>
                    </div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-text-secondary">Channel:</span>
                            <span class="ml-1 font-medium">Google Ads</span>
                        </div>
                        <div>
                            <span class="text-text-secondary">Spend:</span>
                            <span class="ml-1 font-medium text-data">$12,450</span>
                        </div>
                        <div>
                            <span class="text-text-secondary">Leads:</span>
                            <span class="ml-1 font-medium text-data">189</span>
                        </div>
                        <div>
                            <span class="text-text-secondary">Conv. Rate:</span>
                            <span class="ml-1 font-medium text-success">24.8%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-end space-x-2 mt-4">
                        <button class="p-2 text-text-secondary hover:text-primary transition-micro">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                        <button class="p-2 text-text-secondary hover:text-warning transition-micro">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </button>
                        <button class="p-2 text-text-secondary hover:text-accent transition-micro">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="border border-border rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-text-primary">Holiday Promotion</h4>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success">Active</span>
                    </div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-text-secondary">Channel:</span>
                            <span class="ml-1 font-medium">Facebook</span>
                        </div>
                        <div>
                            <span class="text-text-secondary">Spend:</span>
                            <span class="ml-1 font-medium text-data">$8,920</span>
                        </div>
                        <div>
                            <span class="text-text-secondary">Leads:</span>
                            <span class="ml-1 font-medium text-data">142</span>
                        </div>
                        <div>
                            <span class="text-text-secondary">Conv. Rate:</span>
                            <span class="ml-1 font-medium text-success">28.2%</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-end space-x-2 mt-4">
                        <button class="p-2 text-text-secondary hover:text-primary transition-micro">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                        <button class="p-2 text-text-secondary hover:text-warning transition-micro">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </button>
                        <button class="p-2 text-text-secondary hover:text-accent transition-micro">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-surface border-t border-border mt-16">
        <div class="max-w-7xl mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="flex items-center space-x-3 mb-4 md:mb-0">
                    <svg class="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                    </svg>
                    <span class="text-text-primary font-medium">MarketingHub</span>
                </div>
                <p class="text-text-secondary text-sm">© 2025 MarketingHub. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Auto-refresh toggle
        document.getElementById('auto-refresh-toggle').addEventListener('click', function() {
            this.classList.toggle('bg-primary');
            this.classList.toggle('bg-secondary-300');
            const span = this.querySelector('span');
            span.classList.toggle('translate-x-6');
            span.classList.toggle('translate-x-1');
        });

        // Initialize Charts
        function initializeCharts() {
            // Main Chart
            const mainCtx = document.getElementById('main-chart').getContext('2d');
            new Chart(mainCtx, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Spend',
                        data: [65000, 72000, 68000, 75000, 82000, 78000, 85000, 88000, 92000, 89000, 95000, 98000],
                        backgroundColor: '#1e40af',
                        borderRadius: 4,
                        yAxisID: 'y'
                    }, {
                        label: 'Leads',
                        data: [980, 1120, 1050, 1180, 1290, 1220, 1350, 1420, 1480, 1390, 1520, 1580],
                        type: 'line',
                        borderColor: '#0ea5e9',
                        backgroundColor: 'rgba(14, 165, 233, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }, {
                        label: 'Conversions',
                        data: [243, 280, 262, 295, 322, 305, 337, 355, 370, 347, 380, 395],
                        type: 'line',
                        borderColor: '#059669',
                        backgroundColor: 'rgba(5, 150, 105, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            grid: {
                                color: '#f1f5f9'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });

            // Sparklines
            const sparklineOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                elements: {
                    point: { radius: 0 }
                }
            };

            // Spend Sparkline
            new Chart(document.getElementById('spend-sparkline').getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [65, 72, 68, 75, 82, 78, 85],
                        borderColor: '#1e40af',
                        backgroundColor: 'rgba(30, 64, 175, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: sparklineOptions
            });

            // Leads Sparkline
            new Chart(document.getElementById('leads-sparkline').getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [980, 1120, 1050, 1180, 1290, 1220, 1350],
                        borderColor: '#0ea5e9',
                        backgroundColor: 'rgba(14, 165, 233, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: sparklineOptions
            });

            // CPL Sparkline
            new Chart(document.getElementById('cpl-sparkline').getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [66, 64, 65, 64, 64, 64, 63],
                        borderColor: '#d97706',
                        backgroundColor: 'rgba(217, 119, 6, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: sparklineOptions
            });

            // Conversion Sparkline
            new Chart(document.getElementById('conversion-sparkline').getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: [21, 22, 23, 23, 24, 24, 25],
                        borderColor: '#059669',
                        backgroundColor: 'rgba(5, 150, 105, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: sparklineOptions
            });
        }

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', initializeCharts);

        // Simulate auto-refresh
        setInterval(() => {
            const autoRefreshToggle = document.getElementById('auto-refresh-toggle');
            if (autoRefreshToggle.classList.contains('bg-primary')) {
                // Simulate data refresh
                console.log('Data refreshed at:', new Date().toLocaleTimeString());
            }
        }, 30000); // 30 seconds
    </script>
<script id="dhws-dataInjector" src="../public/dhws-data-injector.js"></script>
<script id="dhws-elementInspector" src="../public/dhws-web-inspector.js"></script>
<script id="dhws-errorTracker" src="../public/dhws-error-tracker.js"></script>
</body>
</html>