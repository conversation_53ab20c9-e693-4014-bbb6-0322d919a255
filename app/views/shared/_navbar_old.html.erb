<%# Modern Navigation Bar for AI Marketing Hub %>
<%# Aligns with the app's design system: subtle shadows, clean Tailwind styling, professional UX %>

<nav class="fixed top-0 w-full z-50 bg-white shadow-sm border-b border-gray-100"
     data-controller="navbar"
     role="navigation"
     aria-label="Main navigation">
  
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      
      <!-- Logo Section -->
      <div class="flex items-center flex-shrink-0">
        <%= link_to (user_signed_in? ? dashboard_path : root_path), 
            class: "flex items-center space-x-3 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg p-1",
            'aria-label': 'AI Marketing Hub home' do %>
          
          <!-- Brand Icon -->
          <div class="relative">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-sm group-hover:shadow-md transform group-hover:scale-105 transition-all duration-200">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            
            <!-- Active Status Indicator for Authenticated Users -->
            <% if user_signed_in? %>
              <div class="absolute -top-1 -right-1 w-4 h-4 bg-emerald-500 rounded-full border-2 border-white shadow-sm"
                   title="Active session"
                   aria-label="Active session indicator"></div>
            <% end %>
          </div>
          
          <!-- Brand Text -->
          <div class="hidden sm:block">
            <h1 class="text-lg font-bold text-gray-900 leading-none">
              AI Marketing Hub
            </h1>
            <% if user_signed_in? %>
              <p class="text-xs text-gray-500 leading-none mt-0.5">
                <%= current_user.tenant&.name || "Dashboard" %>
              </p>
            <% end %>
          </div>
        <% end %>
      </div>
      
      <!-- Desktop Navigation Links -->
      <div class="hidden md:flex items-center space-x-1">
        <% unless user_signed_in? %>
          <!-- Marketing Pages Navigation -->
          <% [
               { path: features_path, label: 'Features', icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10' },
               { path: pricing_path, label: 'Pricing', icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
               { path: about_path, label: 'About', icon: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
               { path: contact_path, label: 'Contact', icon: 'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' }
             ].each do |nav_item| %>
            <%= link_to nav_item[:path], 
                class: "flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                'aria-current': (current_page?(nav_item[:path]) ? 'page' : nil) do %>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= nav_item[:icon] %>"></path>
              </svg>
              <span><%= nav_item[:label] %></span>
            <% end %>
          <% end %>
          
        <% else %>
          <!-- Dashboard Navigation -->
          <% dashboard_nav_items = [
               { path: dashboard_path, label: 'Dashboard', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z', controller: 'dashboard' },
               { path: campaigns_path, label: 'Campaigns', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z', controller: 'campaigns' },
               { path: audiences_path, label: 'Audiences', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z', controller: 'audiences' },
               { path: ai_agents_path, label: 'AI Agents', icon: 'M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z', controller: 'ai_agents', badge: 'New' }
             ] %>
          
          <% dashboard_nav_items.each do |nav_item| %>
            <% is_active = (nav_item[:controller] && controller_name == nav_item[:controller]) || current_page?(nav_item[:path]) %>
            <%= link_to nav_item[:path], 
                class: "flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 #{ is_active ? 'text-blue-700 bg-blue-50 border border-blue-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }",
                'aria-current': (is_active ? 'page' : nil) do %>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= nav_item[:icon] %>"></path>
              </svg>
              <span><%= nav_item[:label] %></span>
              <% if nav_item[:badge] %>
                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 animate-pulse">
                  <%= nav_item[:badge] %>
                </span>
              <% end %>
            <% end %>
          <% end %>
          
          <!-- Analytics with Special Styling -->
          <%= link_to vibe_analytics_path,
              class: "flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 #{ controller_name == 'vibe_analytics' ? 'text-purple-700 bg-purple-50 border border-purple-200' : 'text-gray-600 hover:text-purple-700 hover:bg-purple-50' }",
              'aria-current': (controller_name == 'vibe_analytics' ? 'page' : nil) do %>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              <circle cx="12" cy="12" r="2" fill="currentColor"/>
            </svg>
            <span>Vibe Analytics</span>
            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-sm">
              Live
            </span>
          <% end %>
        <% end %>
      </div>
      
      <!-- User Section -->
      <div class="flex items-center space-x-3">
        <% if user_signed_in? %>
          <!-- AI Status Indicator -->
          <%= render 'shared/ai_provider_status' %>
          
          <!-- User Profile Dropdown -->
          <div class="relative" data-controller="dropdown">
            <button type="button" 
                    class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    data-action="click->dropdown#toggle"
                    data-dropdown-target="button"
                    aria-expanded="false"
                    aria-haspopup="true"
                    aria-label="User menu">
              
              <!-- User Avatar -->
              <div class="flex-shrink-0">
                <%= render 'shared/user_avatar', user: current_user, size: 'sm' %>
              </div>
              
              <!-- User Info (Desktop) -->
              <div class="hidden sm:block text-left">
                <p class="text-sm font-medium text-gray-900 leading-tight">
                  <%= current_user.first_name %> <%= current_user.last_name %>
                </p>
                <p class="text-xs text-gray-500 capitalize leading-tight">
                  <%= current_user.role %>
                </p>
              </div>
              
              <!-- Dropdown Arrow -->
              <svg class="w-4 h-4 text-gray-400 transition-transform duration-200"
                   data-dropdown-target="arrow"
                   fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            <!-- Dropdown Menu -->
            <div class="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-100 opacity-0 invisible transform scale-95 transition-all duration-200 z-50"
                 data-dropdown-target="menu"
                 role="menu"
                 aria-orientation="vertical">
              
              <!-- User Info Header -->
              <div class="px-4 py-3 bg-gray-50 rounded-t-xl border-b border-gray-100">
                <div class="flex items-center space-x-3">
                  <%= render 'shared/user_avatar', user: current_user, size: 'md' %>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-semibold text-gray-900 truncate">
                      <%= current_user.first_name %> <%= current_user.last_name %>
                    </p>
                    <p class="text-xs text-gray-500 truncate">
                      <%= current_user.email %>
                    </p>
                    <p class="text-xs text-blue-600 font-medium capitalize">
                      <%= current_user.role %>
                    </p>
                  </div>
                </div>
              </div>
              
              <!-- Menu Items -->
              <div class="py-2">
                <% [
                     { path: profile_path, label: 'Profile Settings', icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z' },
                     { path: account_settings_path, label: 'Account Settings', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z' },
                     { path: help_index_path, label: 'Help & Support', icon: 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z' }
                   ].each do |menu_item| %>
                  <%= link_to menu_item[:path], 
                      class: "flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",
                      role: "menuitem" do %>
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= menu_item[:icon] %>"></path>
                    </svg>
                    <span><%= menu_item[:label] %></span>
                  <% end %>
                <% end %>
                
                <!-- Divider -->
                <div class="border-t border-gray-100 my-2"></div>
                
                <!-- Sign Out -->
                <%= button_to destroy_user_session_path,
                    method: :delete,
                    class: "w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-red-500",
                    data: { turbo_confirm: "Are you sure you want to sign out?" },
                    role: "menuitem" do %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                  <span>Sign Out</span>
                <% end %>
              </div>
            </div>
          </div>
          
        <% else %>
          <!-- Guest User Actions -->
          <%= link_to "Sign In", new_user_session_path, 
              class: "text-gray-600 hover:text-gray-900 font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg px-3 py-2" %>
          
          <%= link_to new_user_registration_path, 
              class: "inline-flex items-center space-x-2 px-6 py-2.5 text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow-sm hover:shadow-md hover:from-blue-700 hover:to-indigo-700 transform hover:-translate-y-0.5 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" do %>
            <span>Start Free Trial</span>
            <svg class="w-4 h-4 transition-transform group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
            </svg>
          <% end %>
        <% end %>
        
        <!-- Mobile Menu Button -->
        <button type="button" 
                class="md:hidden inline-flex items-center justify-center p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                data-action="click->navbar#toggleMobile"
                data-navbar-target="mobileButton"
                aria-expanded="false"
                aria-controls="mobile-menu"
                aria-label="Toggle mobile menu">
          <svg class="w-6 h-6" data-navbar-target="mobileIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          <svg class="w-6 h-6 hidden" data-navbar-target="mobileCloseIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
        
        <% if user_signed_in? %>
          <!-- Sidebar Toggle (Mobile) -->
          <button type="button" 
                  class="lg:hidden inline-flex items-center justify-center p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  data-action="click->navbar#toggleSidebar"
                  aria-label="Open sidebar">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16"></path>
            </svg>
          </button>
        <% end %>
      </div>
    </div>
  </div>
  
  <!-- Mobile Menu -->
  <div class="md:hidden hidden border-t border-gray-100 bg-white"
       data-navbar-target="mobileMenu"
       id="mobile-menu">
    <div class="px-4 py-4 space-y-2">
      <% unless user_signed_in? %>
        <!-- Marketing Navigation -->
        <% [
             { path: features_path, label: 'Features' },
             { path: pricing_path, label: 'Pricing' },
             { path: about_path, label: 'About' },
             { path: contact_path, label: 'Contact' }
           ].each do |nav_item| %>
          <%= link_to nav_item[:label], nav_item[:path], 
              class: "block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors duration-200" %>
        <% end %>
        
        <!-- Mobile Sign Up -->
        <div class="pt-4 border-t border-gray-100">
          <%= link_to new_user_registration_path, 
              class: "block w-full text-center px-6 py-3 text-white bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg font-semibold shadow-sm" do %>
            Start Free Trial
          <% end %>
        </div>
        
      <% else %>
        <!-- Dashboard Navigation for Mobile -->
        <% dashboard_nav_items = [
             { path: dashboard_path, label: 'Dashboard', controller: 'dashboard' },
             { path: campaigns_path, label: 'Campaigns', controller: 'campaigns' },
             { path: audiences_path, label: 'Audiences', controller: 'audiences' },
             { path: ai_agents_path, label: 'AI Agents', controller: 'ai_agents' },
             { path: vibe_analytics_path, label: 'Vibe Analytics', controller: 'vibe_analytics' }
           ] %>
        
        <% dashboard_nav_items.each do |nav_item| %>
          <% is_active = (nav_item[:controller] && controller_name == nav_item[:controller]) || current_page?(nav_item[:path]) %>
          <%= link_to nav_item[:label], nav_item[:path], 
              class: "block px-3 py-2 text-base font-medium rounded-lg transition-colors duration-200 #{ is_active ? 'text-blue-700 bg-blue-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' }" %>
        <% end %>
      <% end %>
    </div>
  </div>
</nav>