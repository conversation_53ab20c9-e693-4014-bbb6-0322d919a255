<%
  # AI Attribution Panel Component
  # Comprehensive transparency panel showing all AI attribution details
  
  object = local_assigns[:object]
  expanded = local_assigns[:expanded] || false
  show_export = local_assigns[:show_export] || true
  show_history = local_assigns[:show_history] || true
  
  return unless object && object.respond_to?(:ai_generated_content?)
  return unless object.ai_generated_content?
  
  attribution_service = AiAttributionService.new(user: current_user, generatable: object)
  attribution_summary = attribution_service.attribution_info
  transparency_details = attribution_service.transparency_details
%>

<div class="ai-attribution-panel bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden"
     data-controller="ai-attribution"
     data-ai-attribution-expanded-value="<%= expanded %>"
     data-ai-attribution-object-type="<%= object.class.name %>"
     data-ai-attribution-object-id="<%= object.id %>">

  <!-- Header -->
  <div class="bg-gradient-to-r from-purple-50 to-blue-50 border-b border-gray-200 px-4 py-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <!-- AI Icon -->
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
            </svg>
          </div>
        </div>
        
        <!-- Title and Summary -->
        <div>
          <h3 class="text-sm font-medium text-gray-900">AI Content Attribution</h3>
          <p class="text-xs text-gray-600"><%= attribution_summary[:display_summary] %></p>
        </div>
      </div>
      
      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <% if show_export %>
          <button type="button" 
                  class="inline-flex items-center px-2 py-1 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  data-action="click->ai-attribution#exportReport"
                  title="Export attribution report">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            Export
          </button>
        <% end %>
        
        <button type="button" 
                class="inline-flex items-center px-2 py-1 text-xs font-medium text-purple-600 hover:text-purple-800"
                data-action="click->ai-attribution#toggleExpanded"
                data-ai-attribution-target="toggleButton">
          <span data-ai-attribution-target="toggleText">
            <%= expanded ? 'Show Less' : 'Show Details' %>
          </span>
          <svg class="w-3 h-3 ml-1 transform transition-transform duration-200 <%= expanded ? 'rotate-180' : '' %>" 
               data-ai-attribution-target="toggleIcon"
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Content Fields Overview -->
  <div class="p-4">
    <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
      <% %w[subject_line preview_text content].each do |field| %>
        <% field_info = attribution_summary[:fields][field] || {} %>
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 rounded-full <%= field_info[:ai_generated] ? 'bg-purple-400' : 'bg-gray-400' %>"></div>
            <span class="text-sm font-medium text-gray-900"><%= field.humanize %></span>
          </div>
          
          <% if field_info[:ai_generated] %>
            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
              AI
            </span>
          <% else %>
            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
              Manual
            </span>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Detailed Information (Expandable) -->
  <div class="ai-attribution-details <%= expanded ? '' : 'hidden' %>" 
       data-ai-attribution-target="detailsPanel">
    
    <!-- Field Details -->
    <div class="border-t border-gray-200 px-4 py-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">Field Details</h4>
      
      <div class="space-y-4">
        <% attribution_summary[:fields].each do |field, field_data| %>
          <% next unless field_data[:ai_generated] %>
          
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-start justify-between mb-3">
              <div>
                <h5 class="text-sm font-medium text-gray-900"><%= field.humanize %></h5>
                <% if field_data[:model] %>
                  <p class="text-xs text-gray-600">
                    Generated by <%= attribution_service.send(:format_model_name, field_data[:model]) %>
                  </p>
                <% end %>
              </div>
              
              <button type="button" 
                      class="text-xs text-purple-600 hover:text-purple-800 font-medium"
                      data-action="click->ai-attribution#showFieldDetails"
                      data-field="<%= field %>">
                View Details
              </button>
            </div>
            
            <% generation_event = object.ai_generation_event_for_field(field) %>
            <% if generation_event %>
              <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 text-xs">
                <div>
                  <span class="font-medium text-gray-700">Generated:</span>
                  <div class="text-gray-600">
                    <%= time_ago_in_words(generation_event.created_at) %> ago
                  </div>
                </div>
                
                <% if generation_event.confidence_percentage %>
                  <div>
                    <span class="font-medium text-gray-700">Confidence:</span>
                    <div class="text-gray-600"><%= generation_event.confidence_percentage %>%</div>
                  </div>
                <% end %>
                
                <% if generation_event.cost_formatted != 'N/A' %>
                  <div>
                    <span class="font-medium text-gray-700">Cost:</span>
                    <div class="text-gray-600"><%= generation_event.cost_formatted %></div>
                  </div>
                <% end %>
                
                <% if generation_event.token_count %>
                  <div>
                    <span class="font-medium text-gray-700">Tokens:</span>
                    <div class="text-gray-600"><%= number_with_delimiter(generation_event.token_count) %></div>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Generation Summary -->
    <% if transparency_details && transparency_details[:generation_summary] %>
      <div class="border-t border-gray-200 px-4 py-4">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Generation Summary</h4>
        
        <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
          <div class="text-center p-3 bg-blue-50 rounded-lg">
            <div class="text-lg font-semibold text-blue-600">
              <%= transparency_details[:generation_summary][:total_events] %>
            </div>
            <div class="text-xs text-blue-600">Total Generations</div>
          </div>
          
          <div class="text-center p-3 bg-green-50 rounded-lg">
            <div class="text-lg font-semibold text-green-600">
              $<%= sprintf("%.4f", transparency_details[:generation_summary][:total_cost]) %>
            </div>
            <div class="text-xs text-green-600">Total Cost</div>
          </div>
          
          <div class="text-center p-3 bg-purple-50 rounded-lg">
            <div class="text-lg font-semibold text-purple-600">
              <%= number_with_delimiter(transparency_details[:generation_summary][:total_tokens]) %>
            </div>
            <div class="text-xs text-purple-600">Total Tokens</div>
          </div>
          
          <div class="text-center p-3 bg-orange-50 rounded-lg">
            <div class="text-lg font-semibold text-orange-600">
              <%= transparency_details[:generation_summary][:models_used].length %>
            </div>
            <div class="text-xs text-orange-600">Models Used</div>
          </div>
        </div>
        
        <% if transparency_details[:generation_summary][:models_used].any? %>
          <div class="mt-3">
            <span class="text-xs font-medium text-gray-700">Models: </span>
            <% transparency_details[:generation_summary][:models_used].each_with_index do |model, index| %>
              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 mr-1">
                <%= attribution_service.send(:format_model_name, model) %>
              </span>
            <% end %>
          </div>
        <% end %>
      </div>
    <% end %>

    <!-- Generation History -->
    <% if show_history && object.ai_generation_events.any? %>
      <div class="border-t border-gray-200 px-4 py-4">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Recent Generation History</h4>
        
        <div class="space-y-2 max-h-48 overflow-y-auto">
          <% object.ai_generation_events.recent.limit(5).each do |event| %>
            <div class="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
              <div class="flex items-center space-x-2">
                <div class="w-1.5 h-1.5 bg-purple-400 rounded-full"></div>
                <span class="font-medium"><%= event.content_type_display_name %></span>
                <span class="text-gray-600">by <%= event.model_display_name %></span>
              </div>
              <span class="text-gray-500">
                <%= time_ago_in_words(event.created_at) %> ago
              </span>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>

<!-- Hidden data for JavaScript -->
<script type="application/json" data-ai-attribution-panel-data>
  <%= {
    object_type: object.class.name,
    object_id: object.id,
    attribution_summary: attribution_summary,
    transparency_details: transparency_details
  }.to_json.html_safe %>
</script>
