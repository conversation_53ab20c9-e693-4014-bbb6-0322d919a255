<%
  # AI Attribution Badge Component
  # Shows visual indicators for AI-generated content
  
  object = local_assigns[:object]
  field = local_assigns[:field]
  size = local_assigns[:size] || 'sm' # sm, md, lg
  style = local_assigns[:style] || 'badge' # badge, inline, detailed
  show_model = local_assigns[:show_model] || false
  show_timestamp = local_assigns[:show_timestamp] || false
  
  return unless object && object.respond_to?(:ai_generated_content?)
  return unless object.ai_generated_content?(field)
  
  attribution_service = AiAttributionService.new(user: current_user, generatable: object)
  attribution_info = attribution_service.attribution_info(field)
  visual_indicators = attribution_service.visual_indicators(field)
  
  return unless visual_indicators
  
  # Size classes
  size_classes = case size
  when 'xs'
    { badge: 'px-1.5 py-0.5 text-xs', icon: 'w-3 h-3', text: 'text-xs' }
  when 'sm'
    { badge: 'px-2 py-1 text-xs', icon: 'w-3 h-3', text: 'text-xs' }
  when 'md'
    { badge: 'px-2.5 py-1.5 text-sm', icon: 'w-4 h-4', text: 'text-sm' }
  when 'lg'
    { badge: 'px-3 py-2 text-base', icon: 'w-5 h-5', text: 'text-base' }
  else
    { badge: 'px-2 py-1 text-xs', icon: 'w-3 h-3', text: 'text-xs' }
  end
  
  # Color classes based on AI model/provider
  color_classes = case attribution_info[:model]&.downcase
  when /gpt|openai/
    'bg-green-100 text-green-800 border-green-200'
  when /claude|anthropic/
    'bg-orange-100 text-orange-800 border-orange-200'
  when /gemini|google/
    'bg-blue-100 text-blue-800 border-blue-200'
  else
    'bg-purple-100 text-purple-800 border-purple-200'
  end
%>

<% if style == 'badge' %>
  <!-- Badge Style -->
  <span class="inline-flex items-center <%= size_classes[:badge] %> rounded-full font-medium border <%= color_classes %> ai-attribution-badge"
        data-controller="tooltip"
        data-tooltip-content="<%= visual_indicators[:tooltip] %>"
        data-ai-attribution-field="<%= field %>"
        data-ai-attribution-model="<%= attribution_info[:model] %>">
    
    <!-- AI Sparkles Icon -->
    <svg class="<%= size_classes[:icon] %> mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3l14 9-14 9V3z"/>
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
    </svg>
    
    <span class="truncate">
      AI Generated
      <% if show_model && attribution_info[:model] %>
        <span class="font-normal opacity-75">
          · <%= AiAttributionService.new(user: current_user, generatable: object).send(:format_model_name, attribution_info[:model]) %>
        </span>
      <% end %>
    </span>
  </span>

<% elsif style == 'inline' %>
  <!-- Inline Style -->
  <span class="inline-flex items-center space-x-1 <%= size_classes[:text] %> text-gray-600 ai-attribution-inline"
        data-controller="tooltip"
        data-tooltip-content="<%= visual_indicators[:tooltip] %>"
        data-ai-attribution-field="<%= field %>"
        data-ai-attribution-model="<%= attribution_info[:model] %>">
    
    <svg class="<%= size_classes[:icon] %> text-purple-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3l14 9-14 9V3z"/>
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
    </svg>
    
    <span class="text-purple-600 font-medium">AI</span>
    
    <% if show_model && attribution_info[:model] %>
      <span class="text-gray-500">
        · <%= AiAttributionService.new(user: current_user, generatable: object).send(:format_model_name, attribution_info[:model]) %>
      </span>
    <% end %>
    
    <% if show_timestamp && attribution_info[:generated_at] %>
      <span class="text-gray-400">
        · <%= time_ago_in_words(Time.zone.parse(attribution_info[:generated_at])) %> ago
      </span>
    <% end %>
  </span>

<% elsif style == 'detailed' %>
  <!-- Detailed Style -->
  <div class="ai-attribution-detailed bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-3"
       data-ai-attribution-field="<%= field %>"
       data-ai-attribution-model="<%= attribution_info[:model] %>">
    
    <div class="flex items-start space-x-3">
      <!-- AI Icon -->
      <div class="flex-shrink-0">
        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
          <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3l14 9-14 9V3z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        </div>
      </div>
      
      <!-- Content -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center space-x-2 mb-1">
          <h4 class="text-sm font-medium text-gray-900">AI Generated Content</h4>
          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <%= color_classes %>">
            <%= field.to_s.humanize %>
          </span>
        </div>
        
        <div class="space-y-1 text-xs text-gray-600">
          <% if attribution_info[:model] %>
            <div class="flex items-center space-x-1">
              <span class="font-medium">Model:</span>
              <span><%= AiAttributionService.new(user: current_user, generatable: object).send(:format_model_name, attribution_info[:model]) %></span>
            </div>
          <% end %>
          
          <% if attribution_info[:generated_at] %>
            <div class="flex items-center space-x-1">
              <span class="font-medium">Generated:</span>
              <span><%= time_ago_in_words(Time.zone.parse(attribution_info[:generated_at])) %> ago</span>
            </div>
          <% end %>
          
          <% generation_event = object.ai_generation_event_for_field(field) %>
          <% if generation_event %>
            <div class="flex items-center space-x-3 mt-2 pt-2 border-t border-purple-200">
              <% if generation_event.confidence_percentage %>
                <span class="text-xs">
                  <span class="font-medium">Confidence:</span> <%= generation_event.confidence_percentage %>%
                </span>
              <% end %>
              
              <% if generation_event.cost_formatted != 'N/A' %>
                <span class="text-xs">
                  <span class="font-medium">Cost:</span> <%= generation_event.cost_formatted %>
                </span>
              <% end %>
              
              <% if generation_event.token_count %>
                <span class="text-xs">
                  <span class="font-medium">Tokens:</span> <%= number_with_delimiter(generation_event.token_count) %>
                </span>
              <% end %>
            </div>
          <% end %>
        </div>
        
        <!-- Actions -->
        <div class="mt-3 flex items-center space-x-3">
          <button type="button" 
                  class="text-xs text-purple-600 hover:text-purple-800 font-medium"
                  data-action="click->ai-attribution#showDetails"
                  data-field="<%= field %>">
            View Details
          </button>
          
          <button type="button" 
                  class="text-xs text-gray-500 hover:text-gray-700"
                  data-action="click->ai-attribution#exportData"
                  data-field="<%= field %>">
            Export Data
          </button>
        </div>
      </div>
    </div>
  </div>
<% end %>

<!-- Hidden data for JavaScript -->
<script type="application/json" data-ai-attribution-data="<%= field %>">
  <%= {
    field: field,
    ai_generated: attribution_info[:ai_generated],
    model: attribution_info[:model],
    generated_at: attribution_info[:generated_at],
    display_info: attribution_info[:display_info],
    visual_indicators: visual_indicators
  }.to_json.html_safe %>
</script>
