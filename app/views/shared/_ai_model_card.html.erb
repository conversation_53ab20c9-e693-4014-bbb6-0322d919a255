<%
  # Model card component for displaying AI model information
  model = local_assigns[:model]
  featured = local_assigns[:featured] || false
  selectable = local_assigns[:selectable] || false
  selected = local_assigns[:selected] || false
%>

<div class="ai-model-card <%= featured ? 'featured-model' : 'standard-model' %> 
            <%= selectable ? 'selectable-model cursor-pointer' : '' %>
            <%= selected ? 'selected-model' : '' %>
            bg-white rounded-lg border transition-all duration-200 hover:shadow-md
            <%= model[:status] == :available ? 'border-gray-200 hover:border-blue-300' : 'border-gray-100' %>"
     data-model-id="<%= model[:id] %>"
     data-model-status="<%= model[:status] %>"
     data-controller="<%= selectable ? 'ai-model-card' : '' %>"
     <%= selectable ? "data-action='click->ai-model-card#selectModel'" : '' %>>
  
  <div class="p-4 <%= featured ? 'pb-3' : '' %>">
    <!-- Header -->
    <div class="flex items-start justify-between mb-3">
      <div class="flex items-start space-x-3 flex-1 min-w-0">
        <!-- Status Indicator -->
        <div class="flex-shrink-0 mt-1">
          <% case model[:status] %>
          <% when :available %>
            <div class="relative">
              <div class="w-3 h-3 bg-green-400 rounded-full"></div>
              <div class="absolute inset-0 w-3 h-3 bg-green-400 rounded-full animate-ping opacity-75"></div>
            </div>
          <% when :error %>
            <div class="w-3 h-3 bg-red-400 rounded-full" title="<%= model[:error_message] %>"></div>
          <% when :not_configured %>
            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
          <% when :placeholder %>
            <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
          <% else %>
            <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
          <% end %>
        </div>

        <!-- Model Info -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2 mb-1">
            <h4 class="text-sm font-semibold text-gray-900 truncate"><%= model[:name] %></h4>
            
            <!-- Performance Tier Badge -->
            <% case model[:performance_tier] %>
            <% when "premium" %>
              <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border border-purple-200">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                Premium
              </span>
            <% when "standard" %>
              <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Standard
              </span>
            <% when "basic" %>
              <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Basic
              </span>
            <% end %>
          </div>
          
          <p class="text-xs text-gray-600 mb-2 line-clamp-2"><%= model[:description] %></p>
          
          <!-- Capabilities -->
          <div class="flex flex-wrap gap-1 mb-2">
            <% model[:capabilities].each do |capability| %>
              <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium 
                           <%= capability == 'text' ? 'bg-green-100 text-green-700' : 
                               capability == 'vision' ? 'bg-blue-100 text-blue-700' :
                               capability == 'function_calling' ? 'bg-purple-100 text-purple-700' :
                               capability == 'audio' ? 'bg-orange-100 text-orange-700' :
                               'bg-gray-100 text-gray-700' %>">
                <% case capability %>
                <% when 'text' %>
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"/>
                  </svg>
                <% when 'vision' %>
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                  </svg>
                <% when 'function_calling' %>
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                  </svg>
                <% when 'audio' %>
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"/>
                  </svg>
                <% end %>
                <%= capability.humanize %>
              </span>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Status Badge -->
      <div class="flex-shrink-0 ml-2">
        <% case model[:status] %>
        <% when :available %>
          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800 border border-green-200">
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
            Available
          </span>
        <% when :error %>
          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-red-100 text-red-800 border border-red-200">
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
            </svg>
            Error
          </span>
        <% when :not_configured %>
          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            Setup Required
          </span>
        <% when :placeholder %>
          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            Configure
          </span>
        <% end %>
      </div>
    </div>

    <!-- Model Metrics -->
    <div class="grid grid-cols-2 gap-3 text-xs">
      <div class="bg-gray-50 rounded-lg p-2">
        <div class="text-gray-500 mb-1">Cost per 1K tokens</div>
        <div class="font-semibold text-gray-900">$<%= sprintf("%.4f", model[:cost_per_1k_tokens]) %></div>
      </div>
      <div class="bg-gray-50 rounded-lg p-2">
        <div class="text-gray-500 mb-1">Context window</div>
        <div class="font-semibold text-gray-900"><%= number_with_delimiter(model[:context_window]) %></div>
      </div>
    </div>

    <!-- Performance Indicators (Featured models only) -->
    <% if featured %>
      <div class="mt-3 pt-3 border-t border-gray-100">
        <div class="flex items-center justify-between text-xs">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-1">
              <span class="text-gray-500">Speed:</span>
              <div class="flex space-x-0.5">
                <% speed_rating = model[:performance_tier] == "premium" ? 2 : model[:performance_tier] == "standard" ? 3 : 3 %>
                <% 3.times do |i| %>
                  <div class="w-1.5 h-1.5 rounded-full <%= i < speed_rating ? 'bg-green-400' : 'bg-gray-200' %>"></div>
                <% end %>
              </div>
            </div>
            <div class="flex items-center space-x-1">
              <span class="text-gray-500">Quality:</span>
              <div class="flex space-x-0.5">
                <% quality_rating = model[:performance_tier] == "premium" ? 3 : model[:performance_tier] == "standard" ? 2 : 1 %>
                <% 3.times do |i| %>
                  <div class="w-1.5 h-1.5 rounded-full <%= i < quality_rating ? 'bg-blue-400' : 'bg-gray-200' %>"></div>
                <% end %>
              </div>
            </div>
          </div>
          
          <% if model[:response_time] && model[:response_time] > 0 %>
            <div class="text-gray-500">
              <%= model[:response_time] %>ms
            </div>
          <% end %>
        </div>
      </div>
    <% end %>

    <!-- Best For Tags (Featured models only) -->
    <% if featured && model[:best_for] %>
      <div class="mt-3">
        <div class="text-xs text-gray-500 mb-1">Best for:</div>
        <div class="flex flex-wrap gap-1">
          <% model[:best_for].each do |use_case| %>
            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-indigo-100 text-indigo-800">
              <%= use_case.humanize %>
            </span>
          <% end %>
        </div>
      </div>
    <% end %>

    <!-- Selection Indicator (Selectable models only) -->
    <% if selectable %>
      <div class="selection-indicator mt-3 pt-3 border-t border-gray-100 <%= selected ? '' : 'hidden' %>">
        <div class="flex items-center justify-center text-blue-600">
          <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
          <span class="text-sm font-medium">Selected</span>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Action Footer (Available models only) -->
  <% if model[:status] == :available && !selectable %>
    <div class="px-4 py-3 bg-gray-50 rounded-b-lg border-t border-gray-100">
      <div class="flex items-center justify-between">
        <div class="text-xs text-gray-600">
          Ready to use
        </div>
        <button type="button" 
                class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                data-action="click->ai-model-status#selectModel"
                data-model-id="<%= model[:id] %>">
          Use This Model
        </button>
      </div>
    </div>
  <% elsif model[:status] != :available %>
    <div class="px-4 py-3 bg-gray-50 rounded-b-lg border-t border-gray-100">
      <div class="flex items-center justify-between">
        <div class="text-xs text-gray-600">
          <% case model[:status] %>
          <% when :not_configured %>
            API key required
          <% when :placeholder %>
            Replace placeholder key
          <% when :error %>
            Configuration error
          <% end %>
        </div>
        <button type="button" 
                class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                data-action="click->ai-model-status#configureModel"
                data-model-id="<%= model[:id] %>"
                data-provider="<%= model[:provider] %>">
          Configure
        </button>
      </div>
    </div>
  <% end %>
</div>
