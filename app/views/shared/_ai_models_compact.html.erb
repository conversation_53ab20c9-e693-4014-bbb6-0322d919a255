<%
  # Compact AI Models component for sidebar usage
  ai_model_service = AiModelService.new
  available_models = ai_model_service.available_models
  recommended_models = ai_model_service.recommended_models_for_task('email_content')
  
  total_available = available_models.count { |m| m[:status] == :available }
  show_all = local_assigns[:show_all] || false
  max_models = local_assigns[:max_models] || 4
  
  models_to_show = show_all ? available_models : recommended_models.first(max_models)
%>

<div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden" 
     data-controller="ai-model-status-compact">
  
  <!-- Header -->
  <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-md flex items-center justify-center">
          <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
          </svg>
        </div>
        <h3 class="text-sm font-medium text-gray-900">Available AI Models</h3>
      </div>
      
      <div class="flex items-center space-x-2">
        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium <%= total_available > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
          <%= total_available %> Available
        </span>
        <button type="button" 
                class="text-gray-400 hover:text-gray-600 transition-colors"
                data-action="click->ai-model-status-compact#refresh"
                title="Refresh status">
          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
        </button>
      </div>
    </div>
    <p class="text-xs text-gray-600 mt-1">Current status of AI content generation</p>
  </div>

  <!-- Models List -->
  <div class="p-4">
    <% if total_available == 0 %>
      <!-- No Models Available -->
      <div class="text-center py-6">
        <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <p class="text-sm text-gray-600 mb-3">No AI models available</p>
        <button type="button" 
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50"
                data-action="click->ai-model-status-compact#showSetupGuide">
          Configure Models
        </button>
      </div>
    <% else %>
      <!-- Models Grid -->
      <div class="space-y-3">
        <% models_to_show.each do |model| %>
          <div class="flex items-center justify-between p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors <%= model[:status] == :available ? 'bg-white' : 'bg-gray-50' %>">
            <!-- Model Info -->
            <div class="flex items-center space-x-3 flex-1 min-w-0">
              <!-- Status Dot -->
              <div class="flex-shrink-0">
                <% case model[:status] %>
                <% when :available %>
                  <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                <% when :error %>
                  <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                <% when :not_configured %>
                  <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                <% when :placeholder %>
                  <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <% end %>
              </div>
              
              <!-- Model Details -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-900 truncate"><%= model[:name] %></span>
                  <% if model[:performance_tier] == "premium" %>
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                      Pro
                    </span>
                  <% end %>
                </div>
                <div class="flex items-center space-x-3 mt-1">
                  <span class="text-xs text-gray-500">
                    $<%= sprintf("%.4f", model[:cost_per_1k_tokens]) %>/1K
                  </span>
                  <% if model[:response_time] && model[:response_time] > 0 %>
                    <span class="text-xs text-gray-500">
                      <%= model[:response_time] %>ms
                    </span>
                  <% end %>
                </div>
              </div>
            </div>

            <!-- Status Badge -->
            <div class="flex-shrink-0 ml-2">
              <% case model[:status] %>
              <% when :available %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  Available
                </span>
              <% when :error %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                  Error
                </span>
              <% when :not_configured %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                  Setup
                </span>
              <% when :placeholder %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                  Config
                </span>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Show More/Less Toggle -->
      <% if available_models.count > max_models %>
        <div class="mt-3 text-center">
          <button type="button" 
                  class="text-xs text-blue-600 hover:text-blue-800 font-medium"
                  data-action="click->ai-model-status-compact#toggleShowAll"
                  data-ai-model-status-compact-target="toggleButton">
            <%= show_all ? "Show Less" : "Show All (#{available_models.count})" %>
          </button>
        </div>
      <% end %>
    <% end %>
  </div>

  <!-- Footer -->
  <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
    <div class="flex items-center justify-between text-xs text-gray-600">
      <span>
        <% if total_available > 0 %>
          Models ready for content generation
        <% else %>
          Configure API keys to enable AI
        <% end %>
      </span>
      <% if total_available > 0 %>
        <span class="text-green-600 font-medium">● Active</span>
      <% else %>
        <span class="text-red-600 font-medium">● Inactive</span>
      <% end %>
    </div>
  </div>
</div>

<!-- Compact Setup Guide Modal -->
<div class="setup-guide-modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" 
     data-ai-model-status-compact-target="setupModal">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Setup AI Models</h3>
        <button type="button" 
                class="text-gray-400 hover:text-gray-600"
                data-action="click->ai-model-status-compact#closeSetupGuide">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      
      <div class="space-y-4">
        <div class="bg-blue-50 rounded-lg p-4">
          <h4 class="font-medium text-blue-900 mb-2">Quick Setup</h4>
          <p class="text-sm text-blue-800 mb-3">
            Configure your AI provider API keys to enable content generation.
          </p>
          <div class="space-y-2 text-sm">
            <div class="bg-white rounded p-2 font-mono text-xs">
              <span class="text-gray-600">OpenAI:</span> OPENAI_API_KEY=sk-...
            </div>
            <div class="bg-white rounded p-2 font-mono text-xs">
              <span class="text-gray-600">Anthropic:</span> ANTHROPIC_API_KEY=sk-ant-...
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%# Compact model data for JavaScript %>
<% content_for :page_data do %>
  <script>
    window.aiModelDataCompact = {
      models: <%= available_models.to_json.html_safe %>,
      recommended: <%= recommended_models.to_json.html_safe %>,
      totalAvailable: <%= total_available %>,
      showAll: <%= show_all %>,
      maxModels: <%= max_models %>,
      lastUpdated: '<%= Time.current.iso8601 %>'
    };
  </script>
<% end %>
