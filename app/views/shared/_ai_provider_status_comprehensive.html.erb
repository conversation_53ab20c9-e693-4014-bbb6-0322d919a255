

<% 
  title ||= "AI Providers"
  show_descriptions = true if show_descriptions.nil?
  show_cost_levels = true if show_cost_levels.nil?
  compact_mode ||= false
%>

<div class="bg-white rounded-lg shadow-sm p-6">
  <div class="flex items-center mb-4">
    <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
    </svg>
    <h3 class="text-base font-medium text-gray-900"><%= title %></h3>
    <span class="ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
      <%= ai_providers.count %> Available
    </span>
  </div>
  
  <div class="<%= compact_mode ? 'space-y-2' : 'space-y-3' %>">
    <% ai_providers.each do |provider_key, config| %>
      <div class="flex items-center justify-between <%= 'py-1' if compact_mode %>">
        <div class="flex items-center space-x-2 min-w-0 flex-1">
          <div class="w-2 h-2 bg-green-400 rounded-full flex-shrink-0"></div>
          <div class="flex flex-col min-w-0 flex-1">
            <span class="text-sm text-gray-900 font-medium truncate"><%= config[:name] %></span>
            <% if show_descriptions && !compact_mode %>
              <span class="text-xs text-gray-500 truncate"><%= config[:description] %></span>
            <% end %>
          </div>
        </div>
        <div class="flex flex-col items-end flex-shrink-0 ml-2">
          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
            Available
          </span>
          <% if show_cost_levels && !compact_mode %>
            <span class="text-xs text-gray-500 mt-1"><%= config[:cost_level] %></span>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
  
  <% unless compact_mode %>
    <div class="mt-4 pt-3 border-t border-gray-200">
      <div class="flex items-start space-x-2">
        <svg class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <p class="text-xs text-gray-500">
          All providers are optimized for email content generation with automatic model selection based on task complexity and cost efficiency.
        </p>
      </div>
    </div>
  <% end %>
</div>

<%# Provider strengths tooltip data for future enhancement %>
<% content_for :page_data do %>
  <script>
    window.aiProviderData = <%= ai_providers.to_json.html_safe %>;
  </script>
<% end %>
