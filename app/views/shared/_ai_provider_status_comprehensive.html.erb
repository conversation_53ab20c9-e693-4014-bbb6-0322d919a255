
<%
  # Initialize AI Model Service and get real-time data
  ai_model_service = AiModelService.new
  provider_stats = ai_model_service.provider_statistics
  available_models = ai_model_service.available_models
  recommended_models = ai_model_service.recommended_models_for_task('email_content')

  title ||= "Available AI Models"
  show_descriptions = true if show_descriptions.nil?
  show_cost_levels = true if show_cost_levels.nil?
  compact_mode ||= false

  total_available = available_models.count { |m| m[:status] == :available }
  total_models = available_models.count
  total_configured = available_models.count { |m| m[:status] != :not_configured }
%>

<div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-100 overflow-hidden"
     data-controller="ai-model-status"
     data-ai-model-status-refresh-interval-value="300000">

  <!-- Header Section -->
  <div class="bg-white border-b border-blue-100 px-6 py-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
            </svg>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900"><%= title %></h3>
          <p class="text-sm text-gray-600">Choose the best AI model for your content generation needs</p>
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <!-- Status Overview -->
        <div class="hidden sm:flex items-center space-x-4 text-sm">
          <div class="flex items-center space-x-1">
            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
            <span class="text-gray-600"><%= total_available %> Available</span>
          </div>
          <div class="flex items-center space-x-1">
            <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
            <span class="text-gray-600"><%= total_configured %> Configured</span>
          </div>
        </div>

        <!-- Refresh Button -->
        <button type="button"
                class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                data-action="click->ai-model-status#refresh"
                data-ai-model-status-target="refreshButton"
                title="Refresh model status">
          <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          <span class="hidden sm:inline">Refresh</span>
        </button>

        <!-- View Toggle -->
        <div class="flex bg-gray-100 rounded-lg p-1">
          <button type="button"
                  class="view-toggle-btn px-3 py-1 text-xs font-medium rounded-md transition-all duration-200 bg-white text-gray-900 shadow-sm"
                  data-action="click->ai-model-status#toggleView"
                  data-view="recommended"
                  data-ai-model-status-target="viewToggle">
            Recommended
          </button>
          <button type="button"
                  class="view-toggle-btn px-3 py-1 text-xs font-medium rounded-md transition-all duration-200 text-gray-600 hover:text-gray-900"
                  data-action="click->ai-model-status#toggleView"
                  data-view="all"
                  data-ai-model-status-target="viewToggle">
            All Models
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="p-6">
    <!-- Quick Status Cards -->
    <% if total_available == 0 %>
      <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div class="ml-3">
            <h4 class="text-sm font-medium text-red-800">No AI Models Available</h4>
            <p class="mt-1 text-sm text-red-700">Please configure your API keys to enable AI content generation.</p>
            <div class="mt-3">
              <button type="button"
                      class="inline-flex items-center px-3 py-1.5 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      data-action="click->ai-model-status#showSetupGuide">
                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                Setup Guide
              </button>
            </div>
          </div>
        </div>
      </div>
    <% elsif total_available < total_models %>
      <div class="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
          </div>
          <div class="ml-3">
            <h4 class="text-sm font-medium text-yellow-800">Some Models Need Configuration</h4>
            <p class="mt-1 text-sm text-yellow-700">
              <%= total_models - total_available %> models are not configured.
              <button type="button" class="underline hover:no-underline" data-action="click->ai-model-status#showSetupGuide">
                Configure them now
              </button> for full functionality.
            </p>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Model Views -->
    <!-- Recommended Models View -->
    <div class="model-view" data-view="recommended" data-ai-model-status-target="modelView">
      <div class="mb-4">
        <h4 class="text-base font-medium text-gray-900 mb-2">Recommended for Email Content</h4>
        <p class="text-sm text-gray-600">These models are optimized for email marketing content generation</p>
      </div>

      <div class="grid gap-4 sm:grid-cols-1 lg:grid-cols-2">
        <% recommended_models.each do |model| %>
          <%= render 'shared/ai_model_card', model: model, featured: true %>
        <% end %>
      </div>
    </div>

    <!-- All Models View -->
    <div class="model-view hidden" data-view="all" data-ai-model-status-target="modelView">
      <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-base font-medium text-gray-900">All Available Models</h4>
          <div class="flex items-center space-x-2">
            <label class="text-sm text-gray-600">Group by:</label>
            <select class="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    data-action="change->ai-model-status#changeGrouping"
                    data-ai-model-status-target="groupingSelect">
              <option value="provider">Provider</option>
              <option value="performance">Performance</option>
              <option value="cost">Cost</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Provider Grouped View -->
      <div class="provider-grouped-view" data-ai-model-status-target="providerGroupedView">
        <% provider_stats.each do |provider, stats| %>
          <div class="mb-8">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <%= render 'shared/provider_icon', provider: provider %>
                </div>
                <div>
                  <h5 class="text-lg font-medium text-gray-900"><%= provider.capitalize %></h5>
                  <p class="text-sm text-gray-600">
                    <%= stats[:available] %> of <%= stats[:total] %> models available
                  </p>
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900"><%= stats[:availability_percentage] %>%</div>
                <div class="text-xs text-gray-500">Availability</div>
              </div>
            </div>

            <div class="grid gap-3 sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
              <% stats[:models].each do |model| %>
                <%= render 'shared/ai_model_card', model: model, featured: false %>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Usage Statistics -->
    <% unless compact_mode %>
      <div class="mt-8 bg-white rounded-lg border border-gray-200 p-6">
        <h5 class="text-base font-medium text-gray-900 mb-4">Usage Overview</h5>
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">1,250</div>
            <div class="text-sm text-gray-600">Requests Today</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">2.5M</div>
            <div class="text-sm text-gray-600">Tokens Used</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">$12.50</div>
            <div class="text-sm text-gray-600">Cost Today</div>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Help and Tips -->
    <div class="mt-6 bg-blue-50 rounded-lg p-4">
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <div class="flex-1">
          <h6 class="text-sm font-medium text-blue-900 mb-1">Smart Model Selection</h6>
          <p class="text-sm text-blue-800 mb-2">
            Our system automatically recommends the best models for your specific content type and requirements.
          </p>
          <div class="space-y-1 text-xs text-blue-700">
            <div class="flex items-center space-x-2">
              <div class="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
              <span><strong>Premium models:</strong> Best quality, higher cost</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
              <span><strong>Standard models:</strong> Good balance of quality and cost</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
              <span><strong>Basic models:</strong> Fast and cost-effective</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Setup Guide Modal (Hidden by default) -->
<div class="setup-guide-modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
     data-ai-model-status-target="setupModal">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">AI Model Setup Guide</h3>
        <button type="button"
                class="text-gray-400 hover:text-gray-600"
                data-action="click->ai-model-status#closeSetupGuide">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <div class="space-y-4">
        <% provider_stats.each do |provider, stats| %>
          <% if stats[:available] < stats[:total] %>
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-2"><%= provider.capitalize %></h4>
              <p class="text-sm text-gray-600 mb-3">
                Configure your <%= provider.capitalize %> API key to enable these models.
              </p>
              <div class="bg-gray-50 rounded p-3 text-sm font-mono">
                <div class="text-gray-600">Environment Variable:</div>
                <div class="text-gray-900"><%= provider.upcase %>_API_KEY=your_api_key_here</div>
              </div>
              <div class="mt-2">
                <a href="#" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                  Get API Key →
                </a>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
  </div>
</div>

<%# Enhanced model data for JavaScript %>
<% content_for :page_data do %>
  <script>
    window.aiModelData = {
      models: <%= available_models.to_json.html_safe %>,
      recommendedModels: <%= recommended_models.to_json.html_safe %>,
      providerStats: <%= provider_stats.to_json.html_safe %>,
      totalAvailable: <%= total_available %>,
      totalModels: <%= total_models %>,
      lastUpdated: '<%= Time.current.iso8601 %>',
      refreshInterval: 300000,
      taskType: 'email_content'
    };
  </script>
<% end %>
