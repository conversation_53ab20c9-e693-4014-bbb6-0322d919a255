
<%
  # Initialize AI Model Service and get real-time data
  ai_model_service = AiModelService.new
  provider_stats = ai_model_service.provider_statistics
  available_models = ai_model_service.available_models

  title ||= "AI Models"
  show_descriptions = true if show_descriptions.nil?
  show_cost_levels = true if show_cost_levels.nil?
  compact_mode ||= false

  total_available = available_models.count { |m| m[:status] == :available }
  total_models = available_models.count
%>

<div class="bg-white rounded-lg shadow-sm p-6" data-controller="ai-model-status">
  <div class="flex items-center justify-between mb-4">
    <div class="flex items-center">
      <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
      </svg>
      <h3 class="text-base font-medium text-gray-900"><%= title %></h3>
    </div>
    <div class="flex items-center space-x-2">
      <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium <%= total_available > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>">
        <%= total_available %>/<%= total_models %> Available
      </span>
      <button type="button"
              class="text-gray-400 hover:text-gray-600 transition-colors"
              data-action="click->ai-model-status#refresh"
              title="Refresh status">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
      </button>
    </div>
  </div>

  <!-- Provider Tabs -->
  <div class="border-b border-gray-200 mb-4">
    <nav class="-mb-px flex space-x-6">
      <% provider_stats.each_with_index do |(provider, stats), index| %>
        <button type="button"
                class="provider-tab <%= index == 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' %> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                data-action="click->ai-model-status#switchProvider"
                data-provider="<%= provider %>"
                data-ai-model-status-target="providerTab">
          <%= provider.capitalize %>
          <span class="ml-1 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium <%= stats[:available] > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
            <%= stats[:available] %>
          </span>
        </button>
      <% end %>
    </nav>
  </div>

  <!-- Provider Content -->
  <% provider_stats.each_with_index do |(provider, stats), index| %>
    <div class="provider-content <%= 'hidden' unless index == 0 %>"
         data-provider="<%= provider %>"
         data-ai-model-status-target="providerContent">

      <div class="space-y-3">
        <% stats[:models].each do |model| %>
          <div class="flex items-center justify-between p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
            <div class="flex items-center space-x-3 min-w-0 flex-1">
              <!-- Status Indicator -->
              <div class="flex-shrink-0">
                <% case model[:status] %>
                <% when :available %>
                  <div class="w-3 h-3 bg-green-400 rounded-full" title="Available"></div>
                <% when :error %>
                  <div class="w-3 h-3 bg-red-400 rounded-full" title="Error: <%= model[:error_message] %>"></div>
                <% when :not_configured %>
                  <div class="w-3 h-3 bg-gray-400 rounded-full" title="Not configured"></div>
                <% when :placeholder %>
                  <div class="w-3 h-3 bg-yellow-400 rounded-full" title="Placeholder API key"></div>
                <% else %>
                  <div class="w-3 h-3 bg-gray-300 rounded-full" title="Unknown status"></div>
                <% end %>
              </div>

              <!-- Model Info -->
              <div class="flex flex-col min-w-0 flex-1">
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-900 truncate"><%= model[:name] %></span>
                  <% if model[:performance_tier] == "premium" %>
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                      Premium
                    </span>
                  <% end %>
                </div>
                <% if show_descriptions && !compact_mode %>
                  <span class="text-xs text-gray-500 truncate"><%= model[:description] %></span>
                  <div class="flex items-center space-x-3 mt-1">
                    <span class="text-xs text-gray-400">
                      Context: <%= number_with_delimiter(model[:context_window]) %>
                    </span>
                    <% if model[:response_time] && model[:response_time] > 0 %>
                      <span class="text-xs text-gray-400">
                        Response: <%= model[:response_time] %>ms
                      </span>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>

            <!-- Status and Cost -->
            <div class="flex flex-col items-end flex-shrink-0 ml-3">
              <% case model[:status] %>
              <% when :available %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  Available
                </span>
              <% when :error %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                  Error
                </span>
              <% when :not_configured %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                  Not Configured
                </span>
              <% when :placeholder %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                  Setup Required
                </span>
              <% end %>

              <% if show_cost_levels && !compact_mode %>
                <span class="text-xs text-gray-500 mt-1">
                  $<%= sprintf("%.4f", model[:cost_per_1k_tokens]) %>/1K tokens
                </span>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Provider Summary -->
      <% unless compact_mode %>
        <div class="mt-4 pt-3 border-t border-gray-200">
          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-lg font-semibold text-gray-900"><%= stats[:available] %></div>
              <div class="text-xs text-gray-500">Available</div>
            </div>
            <div>
              <div class="text-lg font-semibold text-gray-900"><%= stats[:configured] %></div>
              <div class="text-xs text-gray-500">Configured</div>
            </div>
            <div>
              <div class="text-lg font-semibold text-gray-900"><%= stats[:availability_percentage] %>%</div>
              <div class="text-xs text-gray-500">Uptime</div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% end %>

  <!-- Configuration Help -->
  <% unless compact_mode %>
    <div class="mt-4 pt-3 border-t border-gray-200">
      <div class="flex items-start space-x-2">
        <svg class="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <div class="text-xs text-gray-500">
          <p class="mb-1">Models are automatically selected based on task complexity and cost efficiency.</p>
          <% if total_available == 0 %>
            <p class="text-red-600 font-medium">⚠️ No models are currently available. Please configure API keys.</p>
          <% elsif total_available < total_models %>
            <p class="text-yellow-600">Some models need configuration. Check API keys for full functionality.</p>
          <% end %>
        </div>
      </div>
    </div>
  <% end %>
</div>

<%# Enhanced provider data for JavaScript %>
<% content_for :page_data do %>
  <script>
    window.aiModelData = {
      models: <%= available_models.to_json.html_safe %>,
      providerStats: <%= provider_stats.to_json.html_safe %>,
      lastUpdated: '<%= Time.current.iso8601 %>'
    };
  </script>
<% end %>
