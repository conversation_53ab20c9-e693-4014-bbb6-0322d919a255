

<% 
  # Set default values for optional parameters
  form_id ||= 'content_generation_form'
  panel_id ||= 'content_generation_panel'
  apply_endpoint ||= campaign_email_content_path(campaign)
  include_version_history = true if include_version_history.nil?
  
  # Default options if not provided
  brand_voice_options ||= ['professional', 'casual', 'authoritative', 'playful', 'technical', 'inspirational']
  email_type_options ||= ['promotional', 'newsletter', 'welcome', 'announcement', 'follow-up', 'abandoned_cart', 're-engagement']
  
  # Default placeholder
  custom_prompt_placeholder ||= 'Enter key message or specific instructions for this content...'
%>

<div id="<%= panel_id %>" class="bg-white rounded-lg shadow-sm"
     data-controller="content-generation"
     data-content-generation-campaign-id-value="<%= campaign.id %>"
     data-content-generation-endpoint-value="<%= endpoint %>"
     data-content-generation-check-interval-value="1500"
     data-content-generation-max-retries-value="3"
     data-content-generation-hidden-class="hidden"
     data-content-generation-loading-class="loading"
     data-content-generation-error-class="error"
     data-content-generation-success-class="success"
     data-content-generation-active-class="active">

  <div class="flex items-center justify-between p-6 border-b border-gray-200">
    <h3 class="text-lg font-medium text-gray-900">AI Content Generation</h3>
    <div class="flex items-center space-x-2">
      <button type="button"
              class="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              title="Keyboard shortcuts"
              data-turbo-frame="modal">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
        </svg>
      </button>
      <% if include_version_history %>
        <button type="button"
                class="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                title="Version history"
                data-action="click->content-generation#toggleVersionHistory">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </button>
      <% end %>
    </div>
  </div>
  
  <div class="p-6">
    <div class="space-y-6">
      <%= form_with url: endpoint, method: :post, local: true, id: form_id, data: { content_generation_target: "form" }, class: "space-y-6" do |form| %>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="brand_voice" class="block text-sm font-medium text-gray-700 mb-2">Brand Voice</label>
            <%= form.select :brand_voice,
                options_for_select([
                  ["Default (#{campaign.brand_voice.present? ? campaign.brand_voice.humanize : 'Professional'})", ""]
                ] + brand_voice_options.map { |voice| [voice.humanize, voice] }),
                {},
                { class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
          </div>

          <div>
            <label for="email_type" class="block text-sm font-medium text-gray-700 mb-2">Email Type</label>
            <%= form.select :email_type,
                options_for_select([
                  ["Default (#{campaign.respond_to?(:email_type) && campaign.email_type.present? ? campaign.email_type.humanize : 'Promotional'})", ""]
                ] + email_type_options.map { |type| [type.humanize, type] }),
                {},
                { class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
          </div>

          <div class="md:col-span-2">
            <label for="key_message" class="block text-sm font-medium text-gray-700 mb-2">Key Message</label>
            <%= form.text_area :key_message,
                placeholder: custom_prompt_placeholder,
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                rows: 3 %>
            <p class="mt-1 text-sm text-gray-500">Provide a key message or specific instructions for the AI to focus on.</p>
          </div>

          <div class="md:col-span-2 flex justify-end">
            <%= form.submit "Generate Content",
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
          </div>
        </div>
      <% end %>
    </div>

    <div data-content-generation-target="progress" class="hidden mt-6 p-4 bg-blue-50 rounded-lg">
      <div class="mb-3">
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div class="bg-blue-600 h-2 rounded-full transition-all duration-300 animate-pulse" style="width: 0%" data-content-generation-target="progressBar"></div>
        </div>
      </div>
      <p data-content-generation-target="status" class="text-center text-sm text-gray-600 mb-3">Initializing...</p>
      <div class="text-center">
        <button type="button"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                data-action="content-generation#cancel">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
          Cancel
        </button>
      </div>
    </div>

    <div data-content-generation-target="error" class="hidden mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Generation Error</h3>
          <div class="mt-2 text-sm text-red-700" data-content-generation-target="errorMessage">
            An error occurred while generating content.
          </div>
        </div>
      </div>
    </div>
    
    <div class="mt-6">
      <%
        # Check if email_campaign local variable exists and has content
        email_campaign_available = local_assigns[:email_campaign]&.content.present?
        campaign_content_available = campaign.respond_to?(:content) && campaign.content.present?
        content_available = email_campaign_available || campaign_content_available
      %>

      <% if content_available %>
        <!-- Success indicator for generated content -->
        <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <p class="text-sm text-green-800 font-medium">
              AI content generated successfully! Review the content below and make any adjustments before saving.
            </p>
          </div>
        </div>
      <% end %>

      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-medium text-gray-900">Generated Content</h4>
        <div>
          <% if local_assigns[:email_campaign]&.content.present? %>
            <%= form_with url: apply_endpoint, method: :post, local: true, class: "inline-block" do |form| %>
              <%= form.hidden_field :subject_line, value: local_assigns[:email_campaign]&.subject_line, name: "email_campaign[subject_line]" %>
              <%= form.hidden_field :preview_text, value: local_assigns[:email_campaign]&.preview_text, name: "email_campaign[preview_text]" %>
              <%= form.hidden_field :content, value: local_assigns[:email_campaign]&.content, name: "email_campaign[content]" %>
              <%= form.hidden_field :from_name, value: local_assigns[:email_campaign]&.from_name, name: "email_campaign[from_name]" %>
              <%= form.hidden_field :from_email, value: local_assigns[:email_campaign]&.from_email, name: "email_campaign[from_email]" %>
              <%= form.submit "Apply to Campaign",
                  class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors" %>
            <% end %>
          <% else %>
            <button type="button" disabled
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gray-400 cursor-not-allowed transition-colors">
              Apply to Campaign
            </button>
          <% end %>
        </div>
      </div>

      <div class="space-y-4">
        <div>
          <div class="flex items-center justify-between mb-2">
            <label class="block text-sm font-medium text-gray-700">Subject Line</label>
            <% if local_assigns[:email_campaign]&.ai_generated_content?('subject_line') %>
              <%= render 'shared/ai_attribution_badge',
                  object: local_assigns[:email_campaign],
                  field: 'subject_line',
                  size: 'xs',
                  style: 'inline',
                  show_model: true %>
            <% end %>
          </div>
          <div class="p-3 bg-gray-50 rounded-md border border-gray-200" id="subject_line_display">
            <% if local_assigns[:email_campaign]&.subject_line.present? %>
              <%= local_assigns[:email_campaign].subject_line %>
            <% elsif campaign.respond_to?(:subject_line) && campaign.subject_line.present? %>
              <%= campaign.subject_line %>
            <% else %>
              Subject line will appear here
            <% end %>
          </div>
        </div>

        <div>
          <div class="flex items-center justify-between mb-2">
            <label class="block text-sm font-medium text-gray-700">Email Content</label>
            <% if local_assigns[:email_campaign]&.ai_generated_content?('content') %>
              <%= render 'shared/ai_attribution_badge',
                  object: local_assigns[:email_campaign],
                  field: 'content',
                  size: 'xs',
                  style: 'inline',
                  show_model: true %>
            <% end %>
          </div>
          <div data-content-generation-target="output" class="p-4 bg-gray-50 rounded-md border border-gray-200 min-h-[200px] max-h-[400px] overflow-y-auto">
            <% if local_assigns[:email_campaign]&.content.present? %>
              <%= local_assigns[:email_campaign].content.html_safe %>
            <% elsif campaign.respond_to?(:content) && campaign.content.present? %>
              <%= campaign.content.html_safe %>
            <% else %>
              <p class="text-gray-500 italic">Generated content will appear here.</p>
            <% end %>
          </div>
        </div>
      </div>

      <!-- File Upload Section -->
      <% if local_assigns[:email_campaign]&.content.present? %>
        <div class="mt-6">
          <%= form_with url: apply_endpoint, method: :post, local: true, multipart: true, class: "space-y-6" do |form| %>
            <%= form.hidden_field :subject_line, value: local_assigns[:email_campaign]&.subject_line, name: "email_campaign[subject_line]" %>
            <%= form.hidden_field :preview_text, value: local_assigns[:email_campaign]&.preview_text, name: "email_campaign[preview_text]" %>
            <%= form.hidden_field :content, value: local_assigns[:email_campaign]&.content, name: "email_campaign[content]" %>
            <%= form.hidden_field :from_name, value: local_assigns[:email_campaign]&.from_name, name: "email_campaign[from_name]" %>
            <%= form.hidden_field :from_email, value: local_assigns[:email_campaign]&.from_email, name: "email_campaign[from_email]" %>

            <%= render 'shared/file_upload_section', form: form, email_campaign: local_assigns[:email_campaign] %>

            <div class="flex justify-end">
              <%= form.submit "Apply to Campaign with Attachments",
                  class: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors" %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>

    <% if include_version_history %>
      <div id="version-history-container" class="hidden mt-6" data-content-generation-target="versionHistory">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-4 py-3 border-b border-gray-200">
            <h5 class="text-sm font-medium text-gray-900">Version History</h5>
          </div>
          <div class="p-4">
            <div data-content-generation-target="versionList" class="space-y-2 max-h-48 overflow-y-auto">
              <p class="text-center text-gray-500 text-sm">No versions available yet.</p>
            </div>
          </div>
          <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
            <p class="text-xs text-gray-500">
              Tip: Use Ctrl+Z and Ctrl+Shift+Z to navigate between versions.
            </p>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <div class="flex items-center justify-between px-6 py-3 border-t border-gray-200 bg-gray-50">
    <div class="text-xs text-gray-500">
      AI model: <span id="model_used" class="font-medium">Default</span>
    </div>
    <div class="text-xs text-gray-500">
      Last generated: <span id="generation_timestamp" class="font-medium">Never</span>
    </div>
  </div>
</div>

<!-- Keyboard Shortcuts Modal -->
<div id="keyboard-shortcuts-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" data-content-generation-target="shortcutsModal">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Keyboard Shortcuts</h3>
        <button type="button"
                class="text-gray-400 hover:text-gray-600"
                data-action="content-generation#closeShortcuts">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <div class="space-y-3">
        <div class="flex justify-between items-center">
          <div class="flex space-x-1">
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Ctrl</kbd>
            <span class="text-gray-500">+</span>
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">G</kbd>
          </div>
          <span class="text-sm text-gray-600">Generate content</span>
        </div>

        <div class="flex justify-between items-center">
          <div class="flex space-x-1">
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Ctrl</kbd>
            <span class="text-gray-500">+</span>
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Z</kbd>
          </div>
          <span class="text-sm text-gray-600">Load previous version</span>
        </div>

        <div class="flex justify-between items-center">
          <div class="flex space-x-1">
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Ctrl</kbd>
            <span class="text-gray-500">+</span>
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Shift</kbd>
            <span class="text-gray-500">+</span>
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Z</kbd>
          </div>
          <span class="text-sm text-gray-600">Load next version</span>
        </div>

        <div class="flex justify-between items-center">
          <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Esc</kbd>
          <span class="text-sm text-gray-600">Cancel generation</span>
        </div>
      </div>

      <div class="mt-6 flex justify-end">
        <button type="button"
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                data-action="content-generation#closeShortcuts">
          Close
        </button>
      </div>
    </div>
  </div>
</div>



<script>
  // Update the subject line display and form fields when content is generated
  document.addEventListener('content-generation:complete', function(event) {
    const data = event.detail;

    if (data.campaign_data) {
      // Update subject line display
      if (data.campaign_data.subject_line) {
        const subjectDisplay = document.getElementById('subject_line_display');
        if (subjectDisplay) {
          subjectDisplay.textContent = data.campaign_data.subject_line;
        }
      }

      // Update hidden form fields for "Apply to Campaign"
      const updateHiddenField = (name, value) => {
        const field = document.querySelector(`input[name="email_campaign[${name}]"]`);
        if (field && value) {
          field.value = value;
        }
      };

      updateHiddenField('subject_line', data.campaign_data.subject_line);
      updateHiddenField('preview_text', data.campaign_data.preview_text);
      updateHiddenField('content', data.campaign_data.content);
      updateHiddenField('from_name', data.campaign_data.from_name);
      updateHiddenField('from_email', data.campaign_data.from_email);

      // Enable the "Apply to Campaign" button if content is available
      const applyButton = document.querySelector('input[value="Apply to Campaign"]');
      if (applyButton && data.campaign_data.content) {
        applyButton.disabled = false;
        applyButton.classList.remove('bg-gray-400', 'cursor-not-allowed');
        applyButton.classList.add('bg-green-600', 'hover:bg-green-700');
      }
    }

    // Update model and timestamp info
    if (data.model_used) {
      const modelDisplay = document.getElementById('model_used');
      if (modelDisplay) {
        modelDisplay.textContent = data.model_used;
      }
    }

    const timestampDisplay = document.getElementById('generation_timestamp');
    if (timestampDisplay) {
      const now = new Date();
      timestampDisplay.textContent = now.toLocaleString();
    }
  });
</script>
