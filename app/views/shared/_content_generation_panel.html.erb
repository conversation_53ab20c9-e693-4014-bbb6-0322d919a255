<%# Content Generation Panel
  This partial provides a responsive, user-friendly interface for AI content generation
  with real-time feedback, error handling, and version history.

  Usage:
  <%= render 'shared/content_generation_panel', 
    campaign: @campaign,
    endpoint: generate_email_content_path,
    form_id: 'email_generation_form'
  %>

  Required locals:
  - campaign: The campaign object for which content is being generated
  - endpoint: The API endpoint for content generation (e.g., generate_email_content_path)
  
  Optional locals:
  - form_id: ID for the generation form (default: 'content_generation_form')
  - panel_id: ID for the panel container (default: 'content_generation_panel')
  - apply_endpoint: The endpoint for applying generated content (default: derived from campaign and context)
  - include_version_history: Whether to include version history UI (default: true)
  - brand_voice_options: Array of available brand voice options
  - email_type_options: Array of available email type options
  - custom_prompt_placeholder: Custom placeholder for the prompt textarea
%>

<% 
  # Set default values for optional parameters
  form_id ||= 'content_generation_form'
  panel_id ||= 'content_generation_panel'
  apply_endpoint ||= campaign_email_content_path(campaign)
  include_version_history = true if include_version_history.nil?
  
  # Default options if not provided
  brand_voice_options ||= ['professional', 'casual', 'authoritative', 'playful', 'technical', 'inspirational']
  email_type_options ||= ['promotional', 'newsletter', 'welcome', 'announcement', 'follow-up', 'abandoned_cart', 're-engagement']
  
  # Default placeholder
  custom_prompt_placeholder ||= 'Enter key message or specific instructions for this content...'
%>

<div id="<%= panel_id %>" class="bg-white rounded-lg shadow-sm"
     data-controller="content-generation"
     data-content-generation-campaign-id-value="<%= campaign.id %>"
     data-content-generation-endpoint-value="<%= endpoint %>"
     data-content-generation-check-interval-value="1500"
     data-content-generation-max-retries-value="3"
     data-content-generation-hidden-class="hidden"
     data-content-generation-loading-class="loading"
     data-content-generation-error-class="error"
     data-content-generation-success-class="success"
     data-content-generation-active-class="active">

  <div class="flex items-center justify-between p-6 border-b border-gray-200">
    <h3 class="text-lg font-medium text-gray-900">AI Content Generation</h3>
    <div class="flex items-center space-x-2">
      <button type="button"
              class="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              title="Keyboard shortcuts"
              data-turbo-frame="modal">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
        </svg>
      </button>
      <% if include_version_history %>
        <button type="button"
                class="inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                title="Version history"
                data-action="click->content-generation#toggleVersionHistory">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </button>
      <% end %>
    </div>
  </div>
  
  <div class="p-6">
    <div class="space-y-6">
      <%= form_with url: endpoint, method: :post, local: true, id: form_id, data: { content_generation_target: "form" }, class: "space-y-6" do |form| %>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="brand_voice" class="block text-sm font-medium text-gray-700 mb-2">Brand Voice</label>
            <%= form.select :brand_voice,
                options_for_select([
                  ["Default (#{campaign.brand_voice.present? ? campaign.brand_voice.humanize : 'Professional'})", ""]
                ] + brand_voice_options.map { |voice| [voice.humanize, voice] }),
                {},
                { class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
          </div>

          <div>
            <label for="email_type" class="block text-sm font-medium text-gray-700 mb-2">Email Type</label>
            <%= form.select :email_type,
                options_for_select([
                  ["Default (#{campaign.respond_to?(:email_type) && campaign.email_type.present? ? campaign.email_type.humanize : 'Promotional'})", ""]
                ] + email_type_options.map { |type| [type.humanize, type] }),
                {},
                { class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
          </div>

          <div class="md:col-span-2">
            <label for="key_message" class="block text-sm font-medium text-gray-700 mb-2">Key Message</label>
            <%= form.text_area :key_message,
                placeholder: custom_prompt_placeholder,
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                rows: 3 %>
            <p class="mt-1 text-sm text-gray-500">Provide a key message or specific instructions for the AI to focus on.</p>
          </div>

          <div class="md:col-span-2 flex justify-end">
            <%= form.submit "Generate Content",
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
          </div>
        </div>
      <% end %>
    </div>

    <div data-content-generation-target="progress" class="hidden mt-6 p-4 bg-blue-50 rounded-lg">
      <div class="mb-3">
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div class="bg-blue-600 h-2 rounded-full transition-all duration-300 animate-pulse" style="width: 0%" data-content-generation-target="progressBar"></div>
        </div>
      </div>
      <p data-content-generation-target="status" class="text-center text-sm text-gray-600 mb-3">Initializing...</p>
      <div class="text-center">
        <button type="button"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                data-action="content-generation#cancel">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
          Cancel
        </button>
      </div>
    </div>

    <div data-content-generation-target="error" class="hidden mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Generation Error</h3>
          <div class="mt-2 text-sm text-red-700" data-content-generation-target="errorMessage">
            An error occurred while generating content.
          </div>
        </div>
      </div>
    </div>

    <div class="mt-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-medium text-gray-900">Generated Content</h4>
        <div>
          <%= button_to "Apply to Campaign",
              apply_endpoint,
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors",
              data: { turbo_method: :post },
              form: { data: { controller: 'submit-button', submit_button_target: 'form' } } %>
        </div>
      </div>

      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Subject Line</label>
          <div class="p-3 bg-gray-50 rounded-md border border-gray-200" id="subject_line_display">
            <%= campaign.respond_to?(:subject_line) && campaign.subject_line.present? ? campaign.subject_line : "Subject line will appear here" %>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Email Content</label>
          <div data-content-generation-target="output" class="p-4 bg-gray-50 rounded-md border border-gray-200 min-h-[200px] max-h-[400px] overflow-y-auto">
            <% if campaign.respond_to?(:content) && campaign.content.present? %>
              <%= campaign.content.html_safe %>
            <% else %>
              <p class="text-gray-500 italic">Generated content will appear here.</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <% if include_version_history %>
      <div id="version-history-container" class="hidden mt-6" data-content-generation-target="versionHistory">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-4 py-3 border-b border-gray-200">
            <h5 class="text-sm font-medium text-gray-900">Version History</h5>
          </div>
          <div class="p-4">
            <div data-content-generation-target="versionList" class="space-y-2 max-h-48 overflow-y-auto">
              <p class="text-center text-gray-500 text-sm">No versions available yet.</p>
            </div>
          </div>
          <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
            <p class="text-xs text-gray-500">
              Tip: Use Ctrl+Z and Ctrl+Shift+Z to navigate between versions.
            </p>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <div class="flex items-center justify-between px-6 py-3 border-t border-gray-200 bg-gray-50">
    <div class="text-xs text-gray-500">
      AI model: <span id="model_used" class="font-medium">Default</span>
    </div>
    <div class="text-xs text-gray-500">
      Last generated: <span id="generation_timestamp" class="font-medium">Never</span>
    </div>
  </div>
</div>

<!-- Keyboard Shortcuts Modal -->
<div id="keyboard-shortcuts-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" data-content-generation-target="shortcutsModal">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Keyboard Shortcuts</h3>
        <button type="button"
                class="text-gray-400 hover:text-gray-600"
                data-action="content-generation#closeShortcuts">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>

      <div class="space-y-3">
        <div class="flex justify-between items-center">
          <div class="flex space-x-1">
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Ctrl</kbd>
            <span class="text-gray-500">+</span>
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">G</kbd>
          </div>
          <span class="text-sm text-gray-600">Generate content</span>
        </div>

        <div class="flex justify-between items-center">
          <div class="flex space-x-1">
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Ctrl</kbd>
            <span class="text-gray-500">+</span>
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Z</kbd>
          </div>
          <span class="text-sm text-gray-600">Load previous version</span>
        </div>

        <div class="flex justify-between items-center">
          <div class="flex space-x-1">
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Ctrl</kbd>
            <span class="text-gray-500">+</span>
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Shift</kbd>
            <span class="text-gray-500">+</span>
            <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Z</kbd>
          </div>
          <span class="text-sm text-gray-600">Load next version</span>
        </div>

        <div class="flex justify-between items-center">
          <kbd class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded">Esc</kbd>
          <span class="text-sm text-gray-600">Cancel generation</span>
        </div>
      </div>

      <div class="mt-6 flex justify-end">
        <button type="button"
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                data-action="content-generation#closeShortcuts">
          Close
        </button>
      </div>
    </div>
  </div>
</div>



<script>
  // Update the subject line display when content is generated
  document.addEventListener('content-generation:complete', function(event) {
    const data = event.detail;
    if (data.campaign_data && data.campaign_data.subject_line) {
      const subjectDisplay = document.getElementById('subject_line_display');
      if (subjectDisplay) {
        subjectDisplay.textContent = data.campaign_data.subject_line;
      }
    }
    
    // Update model and timestamp info
    if (data.model_used) {
      const modelDisplay = document.getElementById('model_used');
      if (modelDisplay) {
        modelDisplay.textContent = data.model_used;
      }
    }
    
    const timestampDisplay = document.getElementById('generation_timestamp');
    if (timestampDisplay) {
      const now = new Date();
      timestampDisplay.textContent = now.toLocaleString();
    }
  });
</script>
