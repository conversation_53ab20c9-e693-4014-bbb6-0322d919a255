<%# Content Generation Panel
  This partial provides a responsive, user-friendly interface for AI content generation
  with real-time feedback, error handling, and version history.

  Usage:
  <%= render 'shared/content_generation_panel', 
    campaign: @campaign,
    endpoint: generate_email_content_path,
    form_id: 'email_generation_form'
  %>

  Required locals:
  - campaign: The campaign object for which content is being generated
  - endpoint: The API endpoint for content generation (e.g., generate_email_content_path)
  
  Optional locals:
  - form_id: ID for the generation form (default: 'content_generation_form')
  - panel_id: ID for the panel container (default: 'content_generation_panel')
  - apply_endpoint: The endpoint for applying generated content (default: derived from campaign and context)
  - include_version_history: Whether to include version history UI (default: true)
  - brand_voice_options: Array of available brand voice options
  - email_type_options: Array of available email type options
  - custom_prompt_placeholder: Custom placeholder for the prompt textarea
%>

<% 
  # Set default values for optional parameters
  form_id ||= 'content_generation_form'
  panel_id ||= 'content_generation_panel'
  apply_endpoint ||= campaign_email_content_path(campaign)
  include_version_history = true if include_version_history.nil?
  
  # Default options if not provided
  brand_voice_options ||= ['professional', 'casual', 'authoritative', 'playful', 'technical', 'inspirational']
  email_type_options ||= ['promotional', 'newsletter', 'welcome', 'announcement', 'follow-up', 'abandoned_cart', 're-engagement']
  
  # Default placeholder
  custom_prompt_placeholder ||= 'Enter key message or specific instructions for this content...'
%>

<div id="<%= panel_id %>" class="content-generation-panel" 
     data-controller="content-generation"
     data-content-generation-campaign-id-value="<%= campaign.id %>"
     data-content-generation-endpoint-value="<%= endpoint %>"
     data-content-generation-check-interval-value="1500"
     data-content-generation-max-retries-value="3"
     data-content-generation-hidden-class="hidden"
     data-content-generation-loading-class="loading"
     data-content-generation-error-class="error"
     data-content-generation-success-class="success"
     data-content-generation-active-class="active">
  
  <div class="panel-header">
    <h3 class="panel-title">Content Generation</h3>
    <div class="panel-actions">
      <button type="button" class="btn btn-outline-secondary btn-sm" title="Keyboard shortcuts" data-bs-toggle="modal" data-bs-target="#keyboard-shortcuts-modal">
        <i class="bi bi-keyboard"></i>
      </button>
      <% if include_version_history %>
        <button type="button" class="btn btn-outline-secondary btn-sm" title="Version history" data-bs-toggle="collapse" data-bs-target="#version-history-container">
          <i class="bi bi-clock-history"></i>
        </button>
      <% end %>
    </div>
  </div>
  
  <div class="panel-body">
    <div class="generation-options">
      <form id="<%= form_id %>" data-content-generation-target="form">
        <div class="row g-3">
          <div class="col-md-6">
            <label for="brand_voice" class="form-label">Brand Voice</label>
            <select id="brand_voice" name="brand_voice" class="form-select">
              <option value="">Default (<%= campaign.brand_voice.present? ? campaign.brand_voice.humanize : "Professional" %>)</option>
              <% brand_voice_options.each do |voice| %>
                <option value="<%= voice %>"><%= voice.humanize %></option>
              <% end %>
            </select>
          </div>
          
          <div class="col-md-6">
            <label for="email_type" class="form-label">Email Type</label>
            <select id="email_type" name="email_type" class="form-select">
              <option value="">Default (<%= campaign.respond_to?(:email_type) && campaign.email_type.present? ? campaign.email_type.humanize : "Promotional" %>)</option>
              <% email_type_options.each do |type| %>
                <option value="<%= type %>"><%= type.humanize %></option>
              <% end %>
            </select>
          </div>
          
          <div class="col-12">
            <label for="key_message" class="form-label">Key Message</label>
            <textarea id="key_message" name="key_message" class="form-control" rows="3" placeholder="<%= custom_prompt_placeholder %>"></textarea>
            <div class="form-text">Provide a key message or specific instructions for the AI to focus on.</div>
          </div>
          
          <div class="col-12 text-end">
            <button type="button" class="btn btn-primary" data-action="content-generation#generate">
              <i class="bi bi-magic"></i> Generate Content
            </button>
          </div>
        </div>
      </form>
    </div>
    
    <div data-content-generation-target="progress" class="generation-progress hidden">
      <div class="progress mb-2">
        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
      </div>
      <p data-content-generation-target="status" class="text-center text-muted">Initializing...</p>
      <button type="button" class="btn btn-sm btn-outline-secondary" data-action="content-generation#cancel">
        <i class="bi bi-x-circle"></i> Cancel
      </button>
    </div>
    
    <div data-content-generation-target="error" class="generation-error alert alert-danger hidden"></div>
    
    <div class="content-preview mt-4">
      <div class="content-preview-header">
        <h4>Generated Content</h4>
        <div class="preview-actions">
          <% if defined?(Turbo) %>
            <%= button_to "Apply to Campaign", 
                apply_endpoint, 
                class: "btn btn-sm btn-success", 
                data: { turbo_method: :post },
                form: { data: { controller: 'submit-button', submit_button_target: 'form' } } %>
          <% else %>
            <%= button_to "Apply to Campaign", 
                apply_endpoint, 
                method: :post,
                class: "btn btn-sm btn-success" %>
          <% end %>
        </div>
      </div>
      
      <div class="content-preview-subject mb-2">
        <h5>Subject Line:</h5>
        <div class="subject-line-display p-2 border rounded" id="subject_line_display">
          <%= campaign.respond_to?(:subject_line) && campaign.subject_line.present? ? campaign.subject_line : "Subject line will appear here" %>
        </div>
      </div>
      
      <div class="content-preview-body">
        <h5>Email Body:</h5>
        <div data-content-generation-target="output" class="content-output p-3 border rounded">
          <% if campaign.respond_to?(:content) && campaign.content.present? %>
            <%= campaign.content.html_safe %>
          <% else %>
            <p class="text-muted">Generated content will appear here.</p>
          <% end %>
        </div>
      </div>
    </div>
    
    <% if include_version_history %>
      <div id="version-history-container" class="collapse mt-4">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">Version History</h5>
          </div>
          <div class="card-body">
            <div data-content-generation-target="versionList" class="version-list">
              <p class="text-center text-muted">No versions available yet.</p>
            </div>
          </div>
          <div class="card-footer text-muted">
            <small>Tip: Use Ctrl+Z and Ctrl+Shift+Z to navigate between versions.</small>
          </div>
        </div>
      </div>
    <% end %>
  </div>
  
  <div class="panel-footer">
    <div class="model-info text-muted">
      <small>AI model: <span id="model_used">Default</span></small>
    </div>
    <div class="generation-timestamp text-muted">
      <small>Last generated: <span id="generation_timestamp">Never</span></small>
    </div>
  </div>
</div>

<!-- Keyboard Shortcuts Modal -->
<div class="modal fade" id="keyboard-shortcuts-modal" tabindex="-1" aria-labelledby="keyboardShortcutsModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="keyboardShortcutsModalLabel">Keyboard Shortcuts</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Shortcut</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><kbd>Ctrl</kbd> + <kbd>G</kbd></td>
              <td>Generate content</td>
            </tr>
            <tr>
              <td><kbd>Ctrl</kbd> + <kbd>Z</kbd></td>
              <td>Load previous version</td>
            </tr>
            <tr>
              <td><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>Z</kbd></td>
              <td>Load next version</td>
            </tr>
            <tr>
              <td><kbd>Esc</kbd></td>
              <td>Cancel generation</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<style>
  .content-generation-panel {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    background-color: #fff;
    margin-bottom: 2rem;
  }
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
  
  .panel-body {
    padding: 1.5rem;
  }
  
  .panel-footer {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 1.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
  
  .generation-progress {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 5px;
    text-align: center;
  }
  
  .content-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .content-output {
    min-height: 300px;
    max-height: 500px;
    overflow-y: auto;
    background-color: #ffffff;
  }
  
  .version-list {
    max-height: 300px;
    overflow-y: auto;
  }
  
  .version-item {
    padding: 0.5rem;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .version-item:hover {
    background-color: #f0f0f0;
  }
  
  .version-item.active {
    background-color: #e9f5ff;
    border-left: 3px solid #0d6efd;
  }
  
  .version-time {
    font-size: 0.8rem;
    color: #6c757d;
    margin-right: 0.5rem;
  }
  
  .generation-error {
    margin: 1rem 0;
  }
  
  .retry-button {
    display: inline-block;
    margin-left: 1rem;
    padding: 0.25rem 0.5rem;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    cursor: pointer;
  }
  
  .hidden {
    display: none !important;
  }
  
  .loading .progress-bar {
    width: 100%;
  }
  
  @media (max-width: 768px) {
    .panel-header, .panel-footer, .content-preview-header {
      flex-direction: column;
      align-items: flex-start;
    }
    
    .panel-actions, .preview-actions {
      margin-top: 0.5rem;
    }
  }
</style>

<script>
  // Update the subject line display when content is generated
  document.addEventListener('content-generation:complete', function(event) {
    const data = event.detail;
    if (data.campaign_data && data.campaign_data.subject_line) {
      const subjectDisplay = document.getElementById('subject_line_display');
      if (subjectDisplay) {
        subjectDisplay.textContent = data.campaign_data.subject_line;
      }
    }
    
    // Update model and timestamp info
    if (data.model_used) {
      const modelDisplay = document.getElementById('model_used');
      if (modelDisplay) {
        modelDisplay.textContent = data.model_used;
      }
    }
    
    const timestampDisplay = document.getElementById('generation_timestamp');
    if (timestampDisplay) {
      const now = new Date();
      timestampDisplay.textContent = now.toLocaleString();
    }
  });
</script>
