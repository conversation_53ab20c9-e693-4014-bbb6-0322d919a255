<%
  # Initialize AI Model Service
  ai_model_service = AiModelService.new
  task_type = local_assigns[:task_type] || "email_content"
  recommended_models = ai_model_service.recommended_models_for_task(task_type)
  all_available_models = ai_model_service.available_models_by_status(:available)
  
  # Get the best model as default
  default_model = ai_model_service.best_model_for_task(task_type)
  selected_model_id = local_assigns[:selected_model] || default_model&.dig(:id) || recommended_models.first&.dig(:id)
%>

<div class="bg-white rounded-lg border border-gray-200 p-4" data-controller="ai-model-selector">
  <div class="flex items-center justify-between mb-3">
    <label class="block text-sm font-medium text-gray-700">
      AI Model Selection
    </label>
    <button type="button" 
            class="text-xs text-blue-600 hover:text-blue-800 font-medium"
            data-action="click->ai-model-selector#toggleAdvanced"
            data-ai-model-selector-target="advancedToggle">
      Advanced Options
    </button>
  </div>

  <!-- Model Selection Dropdown -->
  <div class="mb-4">
    <select name="ai_model" 
            id="ai_model_select"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            data-ai-model-selector-target="modelSelect"
            data-action="change->ai-model-selector#modelChanged">
      
      <!-- Recommended Models Section -->
      <% if recommended_models.any? %>
        <optgroup label="Recommended for <%= task_type.humanize %>">
          <% recommended_models.each do |model| %>
            <option value="<%= model[:id] %>" 
                    <%= 'selected' if model[:id] == selected_model_id %>
                    data-cost="<%= model[:cost_per_1k_tokens] %>"
                    data-performance="<%= model[:performance_tier] %>"
                    data-provider="<%= model[:provider] %>"
                    data-capabilities="<%= model[:capabilities].join(',') %>">
              <%= model[:name] %> - $<%= sprintf("%.4f", model[:cost_per_1k_tokens]) %>/1K tokens
            </option>
          <% end %>
        </optgroup>
      <% end %>

      <!-- All Available Models Section -->
      <% if all_available_models.any? %>
        <optgroup label="All Available Models">
          <% all_available_models.reject { |m| recommended_models.map { |r| r[:id] }.include?(m[:id]) }.each do |model| %>
            <option value="<%= model[:id] %>" 
                    <%= 'selected' if model[:id] == selected_model_id %>
                    data-cost="<%= model[:cost_per_1k_tokens] %>"
                    data-performance="<%= model[:performance_tier] %>"
                    data-provider="<%= model[:provider] %>"
                    data-capabilities="<%= model[:capabilities].join(',') %>">
              <%= model[:name] %> - $<%= sprintf("%.4f", model[:cost_per_1k_tokens]) %>/1K tokens
            </option>
          <% end %>
        </optgroup>
      <% end %>

      <!-- Fallback if no models available -->
      <% if all_available_models.empty? %>
        <option value="" disabled selected>No models available - Please configure API keys</option>
      <% end %>
    </select>
  </div>

  <!-- Model Information Display -->
  <div class="model-info bg-gray-50 rounded-md p-3 mb-4" data-ai-model-selector-target="modelInfo">
    <% if selected_model_id %>
      <% selected_model = all_available_models.find { |m| m[:id] == selected_model_id } %>
      <% if selected_model %>
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h4 class="text-sm font-medium text-gray-900 mb-1"><%= selected_model[:name] %></h4>
            <p class="text-xs text-gray-600 mb-2"><%= selected_model[:description] %></p>
            
            <!-- Capabilities -->
            <div class="flex flex-wrap gap-1 mb-2">
              <% selected_model[:capabilities].each do |capability| %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  <%= capability.humanize %>
                </span>
              <% end %>
            </div>

            <!-- Strengths -->
            <div class="text-xs text-gray-500">
              <strong>Best for:</strong> <%= selected_model[:strengths].join(", ") %>
            </div>
          </div>

          <!-- Performance Badge -->
          <div class="ml-3 flex flex-col items-end">
            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <%= selected_model[:performance_tier] == 'premium' ? 'bg-purple-100 text-purple-800' : selected_model[:performance_tier] == 'standard' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>">
              <%= selected_model[:performance_tier].humanize %>
            </span>
            <div class="text-xs text-gray-500 mt-1">
              <%= number_with_delimiter(selected_model[:context_window]) %> tokens
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <div class="text-center text-gray-500 text-sm">
        Select a model to see details
      </div>
    <% end %>
  </div>

  <!-- Advanced Options (Hidden by default) -->
  <div class="advanced-options hidden" data-ai-model-selector-target="advancedOptions">
    <div class="border-t border-gray-200 pt-4">
      <h5 class="text-sm font-medium text-gray-900 mb-3">Advanced Settings</h5>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Temperature -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">
            Creativity Level
            <span class="text-gray-500">(Temperature)</span>
          </label>
          <select name="temperature" 
                  class="block w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  data-ai-model-selector-target="temperatureSelect">
            <option value="0.1">Conservative (0.1) - Factual, consistent</option>
            <option value="0.3">Low (0.3) - Slightly varied</option>
            <option value="0.7" selected>Balanced (0.7) - Good mix</option>
            <option value="0.9">Creative (0.9) - More varied</option>
            <option value="1.2">Highly Creative (1.2) - Very varied</option>
          </select>
        </div>

        <!-- Max Tokens -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">
            Response Length
          </label>
          <select name="max_tokens" 
                  class="block w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  data-ai-model-selector-target="maxTokensSelect">
            <option value="500">Short (500 tokens)</option>
            <option value="1000">Medium (1,000 tokens)</option>
            <option value="2000" selected>Long (2,000 tokens)</option>
            <option value="4000">Very Long (4,000 tokens)</option>
          </select>
        </div>
      </div>

      <!-- Cost Estimation -->
      <div class="mt-4 p-3 bg-blue-50 rounded-md">
        <div class="flex items-center justify-between">
          <div class="text-sm text-blue-900">
            <strong>Estimated Cost:</strong>
            <span data-ai-model-selector-target="costEstimate">$0.00</span>
          </div>
          <div class="text-xs text-blue-700">
            Based on selected model and length
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Model Comparison (Hidden by default) -->
  <div class="model-comparison hidden mt-4" data-ai-model-selector-target="modelComparison">
    <div class="border-t border-gray-200 pt-4">
      <h5 class="text-sm font-medium text-gray-900 mb-3">Model Comparison</h5>
      
      <div class="overflow-x-auto">
        <table class="min-w-full text-xs">
          <thead>
            <tr class="border-b border-gray-200">
              <th class="text-left py-2 font-medium text-gray-900">Model</th>
              <th class="text-left py-2 font-medium text-gray-900">Cost</th>
              <th class="text-left py-2 font-medium text-gray-900">Speed</th>
              <th class="text-left py-2 font-medium text-gray-900">Quality</th>
            </tr>
          </thead>
          <tbody>
            <% recommended_models.first(3).each do |model| %>
              <tr class="border-b border-gray-100">
                <td class="py-2 text-gray-900"><%= model[:name] %></td>
                <td class="py-2 text-gray-600">$<%= sprintf("%.4f", model[:cost_per_1k_tokens]) %></td>
                <td class="py-2">
                  <% case model[:performance_tier] %>
                  <% when "premium" %>
                    <span class="text-yellow-600">●●○</span>
                  <% when "standard" %>
                    <span class="text-green-600">●●●</span>
                  <% else %>
                    <span class="text-blue-600">●●●</span>
                  <% end %>
                </td>
                <td class="py-2">
                  <% case model[:performance_tier] %>
                  <% when "premium" %>
                    <span class="text-green-600">●●●</span>
                  <% when "standard" %>
                    <span class="text-blue-600">●●○</span>
                  <% else %>
                    <span class="text-gray-600">●○○</span>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Model Selection Help -->
  <div class="mt-4 text-xs text-gray-500">
    <div class="flex items-start space-x-2">
      <svg class="w-3 h-3 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
      <div>
        <p class="mb-1"><strong>Recommended:</strong> Models optimized for <%= task_type.humanize.downcase %> tasks.</p>
        <p>Higher cost usually means better quality and more capabilities.</p>
      </div>
    </div>
  </div>
</div>

<!-- Hidden input for form submission -->
<input type="hidden" name="selected_ai_model" value="<%= selected_model_id %>" data-ai-model-selector-target="hiddenInput">

<script>
  // Initialize model data for JavaScript
  window.aiModelSelectorData = {
    models: <%= all_available_models.to_json.html_safe %>,
    taskType: '<%= task_type %>',
    selectedModel: '<%= selected_model_id %>'
  };
</script>
