<%# 
  File Upload Section for Email Campaigns
  Supports images, documents, audio, and video files with drag-and-drop functionality
%>

<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6" data-controller="file-upload">
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-medium text-gray-900">Media Attachments</h3>
    <span class="text-sm text-gray-500">Optional</span>
  </div>

  <!-- File Upload Tabs -->
  <div class="border-b border-gray-200 mb-6">
    <nav class="-mb-px flex space-x-8">
      <button type="button" 
              class="file-tab-btn active border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600"
              data-file-upload-target="tabBtn"
              data-action="click->file-upload#switchTab"
              data-tab="images">
        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
        </svg>
        Images
      </button>
      <button type="button" 
              class="file-tab-btn border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
              data-file-upload-target="tabBtn"
              data-action="click->file-upload#switchTab"
              data-tab="documents">
        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        Documents
      </button>
      <button type="button" 
              class="file-tab-btn border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
              data-file-upload-target="tabBtn"
              data-action="click->file-upload#switchTab"
              data-tab="audio">
        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"/>
        </svg>
        Audio
      </button>
      <button type="button" 
              class="file-tab-btn border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
              data-file-upload-target="tabBtn"
              data-action="click->file-upload#switchTab"
              data-tab="video">
        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
        </svg>
        Video
      </button>
    </nav>
  </div>

  <!-- Images Tab -->
  <div class="file-tab-content" data-file-upload-target="tabContent" data-tab="images">
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Upload Images
        <span class="text-gray-500 font-normal">(JPEG, PNG, GIF, WebP - Max 10MB each)</span>
      </label>
      
      <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors"
           data-file-upload-target="dropZone"
           data-file-type="images">
        <div class="space-y-1 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <div class="flex text-sm text-gray-600">
            <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
              <span>Upload images</span>
              <%= form.file_field :images, 
                  multiple: true, 
                  accept: "image/*",
                  class: "sr-only",
                  data: { 
                    file_upload_target: "fileInput",
                    file_type: "images",
                    action: "change->file-upload#handleFileSelect"
                  } %>
            </label>
            <p class="pl-1">or drag and drop</p>
          </div>
          <p class="text-xs text-gray-500">PNG, JPG, GIF, WebP up to 10MB</p>
        </div>
      </div>
    </div>

    <!-- Image Preview Area -->
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4" 
         data-file-upload-target="previewArea" 
         data-file-type="images">
      <!-- Existing images will be displayed here -->
      <% if local_assigns[:email_campaign]&.images&.attached? %>
        <% email_campaign.images.each do |image| %>
          <div class="relative group">
            <%= image_tag image.variant(:thumbnail), 
                class: "w-full h-24 object-cover rounded-lg border border-gray-200" %>
            <button type="button" 
                    class="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    data-action="click->file-upload#removeFile"
                    data-file-id="<%= image.id %>"
                    data-file-type="images">
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
            <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b-lg">
              <%= truncate(image.filename.to_s, length: 15) %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Documents Tab -->
  <div class="file-tab-content hidden" data-file-upload-target="tabContent" data-tab="documents">
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Upload Documents
        <span class="text-gray-500 font-normal">(PDF, DOC, DOCX, TXT - Max 25MB each)</span>
      </label>
      
      <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors"
           data-file-upload-target="dropZone"
           data-file-type="documents">
        <div class="space-y-1 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <div class="flex text-sm text-gray-600">
            <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
              <span>Upload documents</span>
              <%= form.file_field :documents, 
                  multiple: true, 
                  accept: ".pdf,.doc,.docx,.txt",
                  class: "sr-only",
                  data: { 
                    file_upload_target: "fileInput",
                    file_type: "documents",
                    action: "change->file-upload#handleFileSelect"
                  } %>
            </label>
            <p class="pl-1">or drag and drop</p>
          </div>
          <p class="text-xs text-gray-500">PDF, DOC, DOCX, TXT up to 25MB</p>
        </div>
      </div>
    </div>

    <!-- Document Preview Area -->
    <div class="space-y-2 mt-4" data-file-upload-target="previewArea" data-file-type="documents">
      <% if local_assigns[:email_campaign]&.documents&.attached? %>
        <% email_campaign.documents.each do |document| %>
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div class="flex items-center space-x-3">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <div>
                <p class="text-sm font-medium text-gray-900"><%= document.filename %></p>
                <p class="text-xs text-gray-500"><%= number_to_human_size(document.byte_size) %></p>
              </div>
            </div>
            <button type="button" 
                    class="text-red-500 hover:text-red-700"
                    data-action="click->file-upload#removeFile"
                    data-file-id="<%= document.id %>"
                    data-file-type="documents">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
              </svg>
            </button>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Audio Tab -->
  <div class="file-tab-content hidden" data-file-upload-target="tabContent" data-tab="audio">
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Upload Audio Files
        <span class="text-gray-500 font-normal">(MP3, WAV, M4A - Max 50MB each)</span>
      </label>
      
      <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors"
           data-file-upload-target="dropZone"
           data-file-type="audio">
        <div class="space-y-1 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"/>
          </svg>
          <div class="flex text-sm text-gray-600">
            <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
              <span>Upload audio</span>
              <%= form.file_field :audio_files, 
                  multiple: true, 
                  accept: "audio/*",
                  class: "sr-only",
                  data: { 
                    file_upload_target: "fileInput",
                    file_type: "audio",
                    action: "change->file-upload#handleFileSelect"
                  } %>
            </label>
            <p class="pl-1">or drag and drop</p>
          </div>
          <p class="text-xs text-gray-500">MP3, WAV, M4A up to 50MB</p>
        </div>
      </div>
    </div>

    <!-- Audio Preview Area -->
    <div class="space-y-2 mt-4" data-file-upload-target="previewArea" data-file-type="audio">
      <% if local_assigns[:email_campaign]&.audio_files&.attached? %>
        <% email_campaign.audio_files.each do |audio| %>
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div class="flex items-center space-x-3">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"/>
              </svg>
              <div>
                <p class="text-sm font-medium text-gray-900"><%= audio.filename %></p>
                <p class="text-xs text-gray-500"><%= number_to_human_size(audio.byte_size) %></p>
              </div>
            </div>
            <button type="button" 
                    class="text-red-500 hover:text-red-700"
                    data-action="click->file-upload#removeFile"
                    data-file-id="<%= audio.id %>"
                    data-file-type="audio">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
              </svg>
            </button>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Video Tab -->
  <div class="file-tab-content hidden" data-file-upload-target="tabContent" data-tab="video">
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Upload Video Files
        <span class="text-gray-500 font-normal">(MP4, MOV, AVI - Max 100MB each)</span>
      </label>
      
      <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors"
           data-file-upload-target="dropZone"
           data-file-type="video">
        <div class="space-y-1 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
          </svg>
          <div class="flex text-sm text-gray-600">
            <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
              <span>Upload video</span>
              <%= form.file_field :video_files, 
                  multiple: true, 
                  accept: "video/*",
                  class: "sr-only",
                  data: { 
                    file_upload_target: "fileInput",
                    file_type: "video",
                    action: "change->file-upload#handleFileSelect"
                  } %>
            </label>
            <p class="pl-1">or drag and drop</p>
          </div>
          <p class="text-xs text-gray-500">MP4, MOV, AVI up to 100MB</p>
        </div>
      </div>
    </div>

    <!-- Video Preview Area -->
    <div class="space-y-2 mt-4" data-file-upload-target="previewArea" data-file-type="video">
      <% if local_assigns[:email_campaign]&.video_files&.attached? %>
        <% email_campaign.video_files.each do |video| %>
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div class="flex items-center space-x-3">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
              </svg>
              <div>
                <p class="text-sm font-medium text-gray-900"><%= video.filename %></p>
                <p class="text-xs text-gray-500"><%= number_to_human_size(video.byte_size) %></p>
              </div>
            </div>
            <button type="button" 
                    class="text-red-500 hover:text-red-700"
                    data-action="click->file-upload#removeFile"
                    data-file-id="<%= video.id %>"
                    data-file-type="video">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
              </svg>
            </button>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Upload Progress -->
  <div class="hidden mt-4" data-file-upload-target="progressContainer">
    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
      <div class="flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span class="text-sm text-blue-700">Uploading files...</span>
      </div>
      <div class="mt-2 bg-blue-200 rounded-full h-2">
        <div class="bg-blue-500 h-2 rounded-full transition-all duration-300" 
             data-file-upload-target="progressBar" 
             style="width: 0%"></div>
      </div>
    </div>
  </div>
</div>
