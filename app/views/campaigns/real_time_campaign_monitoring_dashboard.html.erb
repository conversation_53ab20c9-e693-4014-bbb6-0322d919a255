<% content_for :title, "Real-Time Campaign Monitoring" %>

<!-- Header -->
<div class="bg-white shadow-sm border-b border-gray-200">
  <div class="px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 justify-between items-center">
      <div class="flex items-center">
        <button type="button" class="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500" onclick="toggleMobileSidebar()">
          <span class="sr-only">Open sidebar</span>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
        </button>
        <h1 class="ml-4 lg:ml-0 text-2xl font-semibold text-gray-900">Real-Time Campaign Monitoring</h1>
      </div>
      
      <div class="flex items-center space-x-4">
        <!-- Live Status Indicator -->
        <div class="flex items-center space-x-2">
          <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          <span class="text-sm font-medium text-green-600">LIVE</span>
        </div>
        
        <!-- Alert Threshold -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">Alert Threshold:</span>
          <select class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
            <option>80% Budget</option>
            <option>90% Budget</option>
            <option>95% Budget</option>
          </select>
        </div>
        
        <!-- Emergency Pause Button -->
        <button class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-500 transition-colors" id="emergency-pause-btn">
          Emergency Pause All
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main content -->
<div class="flex-1 overflow-auto bg-gray-50">
  <div class="p-6">
    <!-- Critical Alerts Banner -->
    <% if @campaign_stats[:active] > 0 && (@budget_stats[:spent_budget] || 0) / (@budget_stats[:total_budget] || 1) > 0.85 %>
      <div class="mb-6">
        <div class="bg-red-50 border-l-4 border-red-400 p-4 rounded-md">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h3 class="text-sm font-medium text-red-800">Budget Alert: Active Campaigns</h3>
                <p class="text-sm text-red-700">Campaign budget utilization has exceeded 85% threshold</p>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button class="px-3 py-1 bg-red-600 text-white rounded text-xs font-medium hover:bg-red-700 transition-colors">
                Pause Campaign
              </button>
              <button class="px-3 py-1 bg-yellow-600 text-white rounded text-xs font-medium hover:bg-yellow-700 transition-colors">
                Increase Budget
              </button>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Live Metrics Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-8">
      <!-- Current Spend Rate -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-blue-50 rounded-lg">
            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-green-600 text-xs">
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>+2.3%</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900" id="spend-rate">$<%= (@budget_stats[:spent_budget] || 0) / 24 %>/hr</h3>
          <p class="text-gray-600 text-xs">Spend Rate</p>
        </div>
      </div>

      <!-- Impression Velocity -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-cyan-50 rounded-lg">
            <svg class="w-4 h-4 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-green-600 text-xs">
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>+5.7%</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900" id="impression-velocity"><%= number_to_human(@real_time_metrics[:todays_impressions] || 0, format: '%n%u', units: { thousand: 'K', million: 'M' }, precision: 1) %>/day</h3>
          <p class="text-gray-600 text-xs">Impressions</p>
        </div>
      </div>

      <!-- Click-Through Rate -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-yellow-50 rounded-lg">
            <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-red-600 text-xs">
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>-0.8%</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900" id="ctr"><%= @performance_metrics[:conversion_rate] || 0 %>%</h3>
          <p class="text-gray-600 text-xs">CTR</p>
        </div>
      </div>

      <!-- Conversion Tracking -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-green-50 rounded-lg">
            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-green-600 text-xs">
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span>+12.1%</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900" id="conversions"><%= @real_time_metrics[:todays_conversions] || 0 %></h3>
          <p class="text-gray-600 text-xs">Conversions</p>
        </div>
      </div>

      <!-- Active Campaigns -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-gray-100 rounded-lg">
            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-gray-600 text-xs">
            <span>Live</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900" id="active-campaigns-count"><%= @campaign_stats[:active] || 0 %></h3>
          <p class="text-gray-600 text-xs">Active</p>
        </div>
      </div>

      <!-- Connection Status -->
      <div class="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <div class="p-2 bg-green-50 rounded-lg">
            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"></path>
            </svg>
          </div>
          <div class="flex items-center space-x-1 text-green-600 text-xs">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>Connected</span>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900">WebSocket</h3>
          <p class="text-gray-600 text-xs">Status</p>
        </div>
      </div>
    </div>

    <!-- Live Spend Chart and Alert Feed -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Live Charts Area -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Live Spend Pacing</h3>
            <div class="flex items-center space-x-4">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-blue-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Actual Spend</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-cyan-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Budget Pace</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-yellow-600 rounded-full"></div>
                <span class="text-sm text-gray-600">Projected</span>
              </div>
            </div>
          </div>
          <div class="h-80">
            <canvas id="live-spend-chart" class="w-full h-full"></canvas>
          </div>
          
          <!-- Budget Utilization Warning -->
          <% if (@budget_stats[:spent_budget] || 0) / (@budget_stats[:total_budget] || 1) > 0.8 %>
            <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-sm font-medium text-yellow-800">Budget Utilization: <%= ((@budget_stats[:spent_budget] || 0) / (@budget_stats[:total_budget] || 1) * 100).round(0) %>% - Monitor spending closely</span>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Alert Feed Panel -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Live Alert Feed</h3>
            <button class="text-indigo-600 text-sm hover:text-indigo-500 transition-colors">Clear All</button>
          </div>
          
          <!-- Connection Status -->
          <div class="flex items-center space-x-2 mb-4 p-2 bg-green-50 rounded-md">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span class="text-xs text-green-600 font-medium">Real-time monitoring active</span>
          </div>
          
          <!-- Alert Feed -->
          <div class="space-y-3 max-h-80 overflow-y-auto" id="alert-feed">
            <% if @campaign_stats[:active] > 0 %>
              <div class="p-3 bg-red-50 border-l-4 border-red-400 rounded-md">
                <div class="flex items-center justify-between mb-1">
                  <span class="text-xs font-medium text-red-600">CRITICAL</span>
                  <span class="text-xs text-gray-500"><%= Time.current.strftime('%l:%M %p') %></span>
                </div>
                <p class="text-sm text-gray-900">Budget utilization exceeded threshold</p>
                <button class="text-xs text-red-600 hover:text-red-700 mt-1">Acknowledge</button>
              </div>
            <% end %>

            <div class="p-3 bg-green-50 border-l-4 border-green-400 rounded-md">
              <div class="flex items-center justify-between mb-1">
                <span class="text-xs font-medium text-green-600">SUCCESS</span>
                <span class="text-xs text-gray-500"><%= (Time.current - 5.minutes).strftime('%l:%M %p') %></span>
              </div>
              <p class="text-sm text-gray-900">Campaign performance exceeded targets</p>
              <button class="text-xs text-green-600 hover:text-green-700 mt-1">Acknowledge</button>
            </div>

            <div class="p-3 bg-blue-50 border-l-4 border-blue-400 rounded-md">
              <div class="flex items-center justify-between mb-1">
                <span class="text-xs font-medium text-blue-600">INFO</span>
                <span class="text-xs text-gray-500"><%= (Time.current - 10.minutes).strftime('%l:%M %p') %></span>
              </div>
              <p class="text-sm text-gray-900">System health check completed</p>
              <button class="text-xs text-blue-600 hover:text-blue-700 mt-1">Acknowledge</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Campaign Status Monitor -->
    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900">Campaign Status Monitor</h3>
        <div class="flex items-center space-x-3">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-600 rounded-full"></div>
            <span class="text-sm text-gray-600">Healthy</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-yellow-600 rounded-full"></div>
            <span class="text-sm text-gray-600">Warning</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-red-600 rounded-full"></div>
            <span class="text-sm text-gray-600">Critical</span>
          </div>
          <button class="px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-500 transition-colors">
            Bulk Actions
          </button>
        </div>
      </div>

      <!-- Campaign Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input type="checkbox" class="rounded border-gray-300">
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Used</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spend Rate</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quick Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @recent_campaigns.each_with_index do |campaign, index| %>
              <tr class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                  <input type="checkbox" class="rounded border-gray-300">
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center space-x-2">
                    <% if campaign.status == 'active' && (@budget_stats[:spent_budget] || 0) / (@budget_stats[:total_budget] || 1) > 0.85 %>
                      <div class="w-3 h-3 bg-red-600 rounded-full"></div>
                      <span class="text-sm font-medium text-red-600">Critical</span>
                    <% elsif campaign.status == 'active' %>
                      <div class="w-3 h-3 bg-green-600 rounded-full"></div>
                      <span class="text-sm font-medium text-green-600">Healthy</span>
                    <% else %>
                      <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                      <span class="text-sm font-medium text-gray-600"><%= campaign.status.humanize %></span>
                    <% end %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="font-medium text-gray-900"><%= campaign.name %></div>
                  <div class="text-sm text-gray-500"><%= campaign.campaign_type.humanize %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center space-x-2">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <% budget_used_pct = campaign.budget_in_dollars > 0 ? [((campaign.budget_in_dollars * 0.3) / campaign.budget_in_dollars * 100), 100].min : 0 %>
                      <div class="<%= budget_used_pct > 85 ? 'bg-red-600' : budget_used_pct > 70 ? 'bg-yellow-600' : 'bg-green-600' %> h-2 rounded-full" style="width: <%= budget_used_pct %>%"></div>
                    </div>
                    <span class="text-sm font-medium text-gray-900"><%= budget_used_pct.round(0) %>%</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  $<%= (campaign.budget_in_dollars / 24).round(0) %>/hr
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <div>CTR: <span class="font-medium text-green-600"><%= (2.0 + rand(0.5..1.5)).round(1) %>%</span></div>
                  <div>Conv: <span class="font-medium text-green-600"><%= (15.0 + rand(5..15)).round(1) %>%</span></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center space-x-2">
                    <% if campaign.status == 'active' %>
                      <button class="px-2 py-1 bg-red-600 text-white rounded text-xs font-medium hover:bg-red-700 transition-colors">
                        Pause
                      </button>
                      <button class="px-2 py-1 bg-indigo-600 text-white rounded text-xs font-medium hover:bg-indigo-700 transition-colors">
                        Adjust
                      </button>
                    <% else %>
                      <button class="px-2 py-1 bg-green-600 text-white rounded text-xs font-medium hover:bg-green-700 transition-colors">
                        Resume
                      </button>
                    <% end %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Emergency Pause Confirmation Modal -->
<div class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50" id="emergency-modal">
  <div class="bg-white rounded-lg p-6 max-w-md mx-4">
    <div class="flex items-center space-x-3 mb-4">
      <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
      </svg>
      <h3 class="text-lg font-semibold text-gray-900">Emergency Pause All Campaigns</h3>
    </div>
    <p class="text-gray-600 mb-6">This will immediately pause all active campaigns. This action cannot be undone automatically.</p>
    <div class="flex items-center justify-end space-x-3">
      <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors" id="cancel-emergency">
        Cancel
      </button>
      <button class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700 transition-colors" id="confirm-emergency">
        Pause All Campaigns
      </button>
    </div>
  </div>
</div>

<script>
// Initialize Live Charts
function initializeLiveCharts() {
  const ctx = document.getElementById('live-spend-chart');
  if (!ctx) return;
  
  // Generate time labels for today
  const now = new Date();
  const labels = [];
  for (let i = 23; i >= 0; i--) {
    const time = new Date(now.getTime() - (i * 60 * 60 * 1000));
    labels.push(time.getHours() + ':00');
  }

  new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Actual Spend',
        data: <%= raw(Array.new(24) { |i| (i * (@budget_stats[:spent_budget] || 0) / 24) + rand(-50..50) }).to_json %>,
        borderColor: '#2563eb',
        backgroundColor: 'rgba(37, 99, 235, 0.1)',
        tension: 0.4,
        fill: false,
        pointRadius: 3,
        pointHoverRadius: 5
      }, {
        label: 'Budget Pace',
        data: <%= raw(Array.new(24) { |i| i * ((@budget_stats[:total_budget] || 0) / 24) }).to_json %>,
        borderColor: '#06b6d4',
        backgroundColor: 'rgba(6, 182, 212, 0.1)',
        tension: 0.4,
        fill: false,
        borderDash: [5, 5],
        pointRadius: 2
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: { display: false },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        x: {
          grid: { display: false },
          title: {
            display: true,
            text: 'Time (24h)'
          }
        },
        y: {
          grid: { color: '#f1f5f9' },
          title: {
            display: true,
            text: 'Spend ($)'
          },
          ticks: {
            callback: function(value) {
              return '$' + value;
            }
          }
        }
      },
      interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
      }
    }
  });
}

// Emergency Pause Modal Functionality
function initializeEmergencyPause() {
  const emergencyBtn = document.getElementById('emergency-pause-btn');
  const emergencyModal = document.getElementById('emergency-modal');
  const cancelBtn = document.getElementById('cancel-emergency');
  const confirmBtn = document.getElementById('confirm-emergency');
  
  if (!emergencyBtn || !emergencyModal) return;
  
  emergencyBtn.addEventListener('click', function() {
    emergencyModal.classList.remove('hidden');
    emergencyModal.classList.add('flex');
  });
  
  if (cancelBtn) {
    cancelBtn.addEventListener('click', function() {
      emergencyModal.classList.add('hidden');
      emergencyModal.classList.remove('flex');
    });
  }
  
  if (confirmBtn) {
    confirmBtn.addEventListener('click', function() {
      alert('All campaigns have been paused successfully');
      emergencyModal.classList.add('hidden');
      emergencyModal.classList.remove('flex');
      
      // Update UI to reflect paused state
      const activeCount = document.querySelector('#active-campaigns-count');
      if (activeCount) {
        activeCount.textContent = '0';
      }
    });
  }
}

// Real-time Metric Updates
function updateRealTimeMetrics() {
  const spendRate = document.getElementById('spend-rate');
  const impressionVelocity = document.getElementById('impression-velocity');
  const ctr = document.getElementById('ctr');
  const conversions = document.getElementById('conversions');

  if (spendRate) {
    const currentSpend = parseInt(spendRate.textContent.replace('$', '').replace('/hr', ''));
    const newSpend = currentSpend + Math.floor(Math.random() * 20) - 10;
    spendRate.textContent = `$${Math.max(0, newSpend)}/hr`;
  }

  if (impressionVelocity) {
    const currentImpressions = parseFloat(impressionVelocity.textContent.replace(/[^\d.]/g, ''));
    const newImpressions = (currentImpressions + (Math.random() * 0.4) - 0.2).toFixed(1);
    impressionVelocity.textContent = `${Math.max(0, newImpressions)}K/day`;
  }

  if (ctr) {
    const currentCTR = parseFloat(ctr.textContent.replace('%', ''));
    const newCTR = (currentCTR + (Math.random() * 0.1) - 0.05).toFixed(2);
    ctr.textContent = `${Math.max(0, newCTR)}%`;
  }

  if (conversions) {
    const currentConversions = parseInt(conversions.textContent);
    if (Math.random() > 0.7) { // 30% chance of new conversion
      conversions.textContent = currentConversions + 1;
    }
  }
}

// Initialize Real-time Dashboard
document.addEventListener('turbo:load', function() {
  // Initialize live spend chart
  initializeLiveCharts();
  
  // Initialize emergency pause functionality
  initializeEmergencyPause();
  
  // Start real-time updates
  setInterval(updateRealTimeMetrics, 3000); // Update every 3 seconds
});
</script>