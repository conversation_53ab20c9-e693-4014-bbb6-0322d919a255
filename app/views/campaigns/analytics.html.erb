<% content_for :title, "Campaign Analytics - #{@campaign_data[:campaign_name]}" %>

<!-- Campaign Analytics Deep Dive Dashboard -->
<div class="min-h-screen bg-gray-50" data-controller="mobile-sidebar">
  
  <!-- Mobile Sidebar Overlay -->
  <div class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden" 
       data-mobile-sidebar-target="overlay"
       data-action="click->mobile-sidebar#close"></div>
  
  <!-- Fixed Sidebar -->
  <div class="fixed left-0 top-0 h-full w-64 bg-white shadow-lg border-r border-gray-100 z-30 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out" 
       data-mobile-sidebar-target="sidebar">
    
    <!-- Sidebar Content -->
    <div class="flex flex-col h-full">
      <!-- Logo Section -->
      <div class="flex items-center justify-between p-6 border-b border-gray-100">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-lg font-bold text-gray-900">AI Marketing</h1>
            <p class="text-xs text-gray-500">Hub</p>
          </div>
        </div>
        
        <!-- Mobile Close Button -->
        <button type="button" 
                class="lg:hidden p-1 text-gray-400 hover:text-gray-600 rounded-md"
                data-action="click->mobile-sidebar#close">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Navigation -->
      <nav class="flex-1 p-6">
        <div class="space-y-2">
          <% [
               { path: dashboard_path, label: 'Dashboard', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z' },
               { path: campaigns_path, label: 'Campaigns', icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10' },
               { path: audiences_path, label: 'Audiences', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
               { path: ai_agents_path, label: 'AI Agents', icon: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z' },
               { path: vibe_analytics_path, label: 'Vibe Analytics', icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z' },
               { path: '#', label: 'Analytics', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z', active: true },
               { path: account_settings_path, label: 'Settings', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z' }
             ].each do |nav_item| %>
            <%= link_to nav_item[:path], 
                class: "flex items-center space-x-3 px-3 py-2.5 rounded-xl transition-colors duration-200 group #{ nav_item[:active] ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }" do %>
              <svg class="w-5 h-5 flex-shrink-0 #{ nav_item[:active] ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600' }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= nav_item[:icon] %>"></path>
              </svg>
              <span class="font-medium"><%= nav_item[:label] %></span>
            <% end %>
          <% end %>
        </div>
      </nav>

      <!-- User Profile Section -->
      <div class="p-6 border-t border-gray-100">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-600 rounded-lg flex items-center justify-center text-white font-semibold">
            <%= current_user.first_name.first %><%= current_user.last_name.first %>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate"><%= current_user.first_name %></p>
            <p class="text-xs text-gray-500 truncate"><%= current_user.email %></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="ml-0 lg:ml-64">
    <!-- Header -->
    <div class="bg-white border-b border-gray-100 px-4 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <!-- Mobile Menu Button -->
        <button type="button" 
                class="lg:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                data-action="click->mobile-sidebar#toggle"
                aria-label="Toggle menu">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
        
        <div class="flex-1 lg:flex-none">
          <div class="flex items-center space-x-3">
            <%= link_to campaigns_path, class: "text-gray-500 hover:text-gray-700" do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            <% end %>
            <div>
              <h1 class="text-2xl font-bold text-gray-900">Campaign Analytics</h1>
              <p class="text-gray-600"><%= @campaign_data[:campaign_name] %> • <%= @campaign_data[:campaign_type] %></p>
            </div>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- Export Button -->
          <button type="button" 
                  class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span>Export Report</span>
          </button>
          
          <!-- Time Range Dropdown -->
          <div class="relative" data-controller="dropdown">
            <button type="button" 
                    class="flex items-center space-x-2 px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
                    data-action="click->dropdown#toggle"
                    data-dropdown-target="button">
              <span>Last 30 days</span>
              <svg class="w-4 h-4 text-gray-400 transition-transform duration-200" data-dropdown-target="arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 opacity-0 invisible transform scale-95 transition-all duration-200 z-50"
                 data-dropdown-target="menu">
              <div class="py-2">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Last 7 days</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Last 30 days</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Last 90 days</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Custom range</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-4 lg:p-8 space-y-8">
      
      <!-- Campaign Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Revenue -->
        <div class="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Revenue</p>
              <p class="text-2xl font-bold text-gray-900">$<%= number_with_delimiter(@performance_metrics[:total_revenue], delimiter: ',') %></p>
              <p class="text-sm text-green-600 mt-1">
                <span class="inline-flex items-center">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                  </svg>
                  +12.5% vs last period
                </span>
              </p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Total Impressions -->
        <div class="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Impressions</p>
              <p class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@performance_metrics[:total_impressions], delimiter: ',') %></p>
              <p class="text-sm text-blue-600 mt-1">
                <span class="inline-flex items-center">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                  </svg>
                  +8.3% vs last period
                </span>
              </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Click-through Rate -->
        <div class="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Click-through Rate</p>
              <p class="text-2xl font-bold text-gray-900"><%= @performance_metrics[:ctr] %>%</p>
              <p class="text-sm text-green-600 mt-1">
                <span class="inline-flex items-center">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                  </svg>
                  +0.4% vs last period
                </span>
              </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Conversion Rate -->
        <div class="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Conversion Rate</p>
              <p class="text-2xl font-bold text-gray-900"><%= @performance_metrics[:conversion_rate] %>%</p>
              <p class="text-sm text-orange-600 mt-1">
                <span class="inline-flex items-center">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                  </svg>
                  -0.2% vs last period
                </span>
              </p>
            </div>
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Campaign Performance Chart -->
      <div class="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Performance Overview</h3>
            <p class="text-sm text-gray-600">Track key metrics over time</p>
          </div>
          <div class="flex items-center space-x-2">
            <button class="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-600 rounded-lg">Impressions</button>
            <button class="px-3 py-1 text-xs font-medium text-gray-600 hover:bg-gray-100 rounded-lg">Clicks</button>
            <button class="px-3 py-1 text-xs font-medium text-gray-600 hover:bg-gray-100 rounded-lg">Conversions</button>
            <button class="px-3 py-1 text-xs font-medium text-gray-600 hover:bg-gray-100 rounded-lg">Revenue</button>
          </div>
        </div>
        <div class="h-80">
          <canvas id="performanceChart" data-chart-data='<%= @chart_data[:performance_chart].to_json %>'></canvas>
        </div>
      </div>

      <!-- Conversion Funnel & Audience Breakdown -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Conversion Funnel -->
        <div class="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Conversion Funnel</h3>
            <p class="text-sm text-gray-600">Track user journey from impression to conversion</p>
          </div>
          <div class="space-y-4">
            <% @chart_data[:conversion_funnel].each_with_index do |stage, index| %>
              <div class="relative">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-700"><%= stage[:stage] %></span>
                  <div class="text-right">
                    <div class="text-sm font-semibold text-gray-900"><%= number_with_delimiter(stage[:count], delimiter: ',') %></div>
                    <div class="text-xs text-gray-500"><%= stage[:percentage] %>%</div>
                  </div>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500" 
                       style="width: <%= stage[:percentage] %>%"></div>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Audience Breakdown -->
        <div class="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Audience Breakdown</h3>
            <p class="text-sm text-gray-600">Demographics and interests analysis</p>
          </div>
          
          <!-- Age Demographics -->
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Age Demographics</h4>
            <div class="space-y-2">
              <% @chart_data[:audience_breakdown][:demographics].each do |age_range, percentage| %>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600"><%= age_range %></span>
                  <div class="flex items-center space-x-2">
                    <div class="w-20 bg-gray-200 rounded-full h-2">
                      <div class="bg-blue-500 h-2 rounded-full transition-all duration-500" 
                           style="width: <%= percentage * 2 %>%"></div>
                    </div>
                    <span class="text-sm font-medium text-gray-900 w-8"><%= percentage %>%</span>
                  </div>
                </div>
              <% end %>
            </div>
          </div>

          <!-- Device Types -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-3">Device Types</h4>
            <div class="space-y-2">
              <% @chart_data[:audience_breakdown][:devices].each do |device, percentage| %>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600"><%= device %></span>
                  <div class="flex items-center space-x-2">
                    <div class="w-20 bg-gray-200 rounded-full h-2">
                      <div class="bg-green-500 h-2 rounded-full transition-all duration-500" 
                           style="width: <%= percentage * 2 %>%"></div>
                    </div>
                    <span class="text-sm font-medium text-gray-900 w-8"><%= percentage %>%</span>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Platform Performance Table -->
      <div class="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-gray-900">Platform Performance</h3>
          <p class="text-sm text-gray-600">Compare performance across advertising platforms</p>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Platform</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impressions</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clicks</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conversions</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CTR</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CVR</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @chart_data[:platform_performance].each do |platform_data| %>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="text-sm font-medium text-gray-900"><%= platform_data[:platform] %></div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= number_with_delimiter(platform_data[:impressions], delimiter: ',') %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= number_with_delimiter(platform_data[:clicks], delimiter: ',') %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= number_with_delimiter(platform_data[:conversions], delimiter: ',') %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    $<%= number_with_delimiter(platform_data[:cost], delimiter: ',') %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= ((platform_data[:clicks].to_f / platform_data[:impressions]) * 100).round(2) %>%
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= ((platform_data[:conversions].to_f / platform_data[:clicks]) * 100).round(2) %>%
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Campaign Details & Insights -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        <!-- Campaign Details -->
        <div class="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Campaign Details</h3>
            <p class="text-sm text-gray-600">Overview of campaign configuration</p>
          </div>
          
          <div class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500">Campaign Type</dt>
              <dd class="text-sm text-gray-900 mt-1"><%= @campaign_data[:campaign_type] %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Status</dt>
              <dd class="text-sm mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                       <%= @campaign_data[:status] == 'Active' ? 'bg-green-100 text-green-800' : 
                           @campaign_data[:status] == 'Paused' ? 'bg-yellow-100 text-yellow-800' : 
                           'bg-gray-100 text-gray-800' %>">
                  <%= @campaign_data[:status] %>
                </span>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Budget</dt>
              <dd class="text-sm text-gray-900 mt-1">$<%= number_with_delimiter(@campaign_data[:budget], delimiter: ',') %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Days Running</dt>
              <dd class="text-sm text-gray-900 mt-1"><%= @campaign_data[:days_running] %> days</dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Target Audience</dt>
              <dd class="text-sm text-gray-900 mt-1"><%= @campaign_data[:target_audience] || 'General' %></dd>
            </div>
          </div>
        </div>

        <!-- Key Insights -->
        <div class="lg:col-span-2 bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900">AI-Generated Insights</h3>
            <p class="text-sm text-gray-600">Automated analysis and recommendations</p>
          </div>
          
          <div class="space-y-4">
            <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  <svg class="w-5 h-5 text-green-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <h4 class="text-sm font-medium text-green-800">Strong Performance</h4>
                  <p class="text-sm text-green-700 mt-1">Your campaign is performing <%= (@performance_metrics[:roas] * 100 / 7.93).round(0) %>% above industry average for <%= @campaign_data[:campaign_type].downcase %> campaigns. The ROAS of <%= @performance_metrics[:roas] %> indicates excellent return on investment.</p>
                </div>
              </div>
            </div>
            
            <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <div>
                  <h4 class="text-sm font-medium text-blue-800">Optimization Opportunity</h4>
                  <p class="text-sm text-blue-700 mt-1">Mobile traffic shows higher conversion rates (+15%). Consider increasing mobile-optimized ad spend and creating mobile-specific creative assets.</p>
                </div>
              </div>
            </div>
            
            <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  <svg class="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                </div>
                <div>
                  <h4 class="text-sm font-medium text-yellow-800">Watch Point</h4>
                  <p class="text-sm text-yellow-700 mt-1">Conversion rate has decreased by 0.2% over the last 7 days. Consider A/B testing new ad creative or landing page variations to improve performance.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js Script -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
  const ctx = document.getElementById('performanceChart').getContext('2d');
  const chartData = JSON.parse(document.getElementById('performanceChart').dataset.chartData);
  
  new Chart(ctx, {
    type: 'line',
    data: {
      labels: chartData.labels,
      datasets: [{
        label: 'Impressions',
        data: chartData.impressions,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.05)'
          }
        },
        x: {
          grid: {
            display: false
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      }
    }
  });
});
</script>
