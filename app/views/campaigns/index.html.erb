<%# filepath: /Users/<USER>/Developer/ai_marketing_hub/app/views/campaigns/index.html.erb %>

<% content_for :title, "Campaign Dashboard" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="campaigns-dashboard">
      <!-- Dashboard Header -->
      <div class="dashboard-header mb-8">
        <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Campaign Dashboard</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">Manage and monitor your marketing campaigns</p>
      </div>
      <div class="flex space-x-3">
        <%= link_to  new_campaign_path, 
            class: "btn btn-primary flex items-center space-x-2" do %>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          <span>New Campaign</span>
        <% end %>
        <%= link_to "#", 
            class: "btn btn-secondary flex items-center space-x-2" do %>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
          </svg>
          <span>Import</span>
        <% end %>
      </div>
      </div>

      <!-- Statistics Cards -->
      <div class="stats-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
    <div class="stat-card bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Campaigns</p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white"><%= @campaign_stats[:total] %></p>
        </div>
      </div>
    </div>

    <div class="stat-card bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
          <p class="text-2xl font-semibold text-green-600 dark:text-green-400"><%= @campaign_stats[:active] %></p>
        </div>
      </div>
    </div>

    <div class="stat-card bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Draft</p>
          <p class="text-2xl font-semibold text-yellow-600 dark:text-yellow-400"><%= @campaign_stats[:draft] %></p>
        </div>
      </div>
    </div>

    <div class="stat-card bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Paused</p>
          <p class="text-2xl font-semibold text-orange-600 dark:text-orange-400"><%= @campaign_stats[:paused] %></p>
        </div>
      </div>
    </div>

    <div class="stat-card bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
          <p class="text-2xl font-semibold text-blue-600 dark:text-blue-400"><%= @campaign_stats[:completed] %></p>
        </div>
      </div>
    </div>

    <div class="stat-card bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Cancelled</p>
          <p class="text-2xl font-semibold text-red-600 dark:text-red-400"><%= @campaign_stats[:cancelled] %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Search -->
  <div class="filter-section bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
    <%= form_with url: campaigns_path, method: :get, local: true, class: "campaign-filters" do |f| %>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
        <div>
          <%= f.label :search, "Search Campaigns", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= f.text_field :search, value: params[:search], 
              placeholder: "Search by name, description...", 
              class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" %>
        </div>

        <div>
          <%= f.label :status, "Status", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= f.select :status, 
              options_for_select([
                ['All Statuses', ''],
                ['Active', 'active'],
                ['Draft', 'draft'],
                ['Paused', 'paused'],
                ['Completed', 'completed'],
                ['Cancelled', 'cancelled']
              ], params[:status]), 
              {}, 
              class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" %>
        </div>

        <div>
          <%= f.label :campaign_type, "Type", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
          <%= f.select :campaign_type, 
              options_for_select([
                ['All Types', ''],
                ['Email', 'email'],
                ['Social', 'social'],
                ['SEO', 'seo'],
                ['Multi-Channel', 'multi_channel']
              ], params[:campaign_type]), 
              {}, 
              class: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" %>
        </div>

        <div class="flex space-x-2">
          <%= f.submit "Filter", class: "btn btn-primary flex-1" %>
          <%= link_to "Clear", campaigns_path, class: "btn btn-secondary px-4" %>
        </div>
      </div>

      <div class="mt-4 flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            Showing <%= @campaigns.size %> campaigns
          </span>
          
          <!-- View Toggle -->
          <div class="view-toggle flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button type="button" data-view="grid" class="view-toggle-btn px-3 py-1 text-sm rounded-md active">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
              </svg>
            </button>
            <button type="button" data-view="list" class="view-toggle-btn px-3 py-1 text-sm rounded-md">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
              </svg>
            </button>
          </div>
        </div>

        <div class="sort-options">
          <%= f.label :sort, "Sort by:", class: "text-sm text-gray-600 dark:text-gray-400 mr-2" %>
          <%= f.select :sort, 
              options_for_select([
                ['Name A-Z', 'name_asc'],
                ['Name Z-A', 'name_desc'],
                ['Created (Newest)', 'created_desc'],
                ['Created (Oldest)', 'created_asc'],
                ['Status', 'status'],
                ['Budget (High-Low)', 'budget_desc'],
                ['Budget (Low-High)', 'budget_asc']
              ], params[:sort]), 
              {}, 
              { class: "px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white",
                onchange: "this.form.submit();" } %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Campaign Grid/List -->
  <div class="campaigns-container">
    <% if @campaigns.present? %>
      <div id="campaigns-grid" class="campaigns-grid grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
        <% @campaigns.each do |campaign| %>
          <div class="campaign-card bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
            <!-- Campaign Header -->
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
              <div class="flex justify-between items-start mb-3">
                <div class="flex-1 min-w-0">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                    <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                    <%= campaign.description.present? ? truncate(campaign.description, length: 100) : "No description" %>
                  </p>
                </div>
                
                <!-- Status Badge -->
                <span class="status-badge <%= campaign.status %>">
                  <%= campaign.status.titleize %>
                </span>
              </div>

              <!-- Campaign Type & Metrics -->
              <div class="flex items-center justify-between text-sm">
                <div class="flex items-center space-x-2">
                  <span class="campaign-type-badge <%= campaign.campaign_type %>">
                    <%= campaign.campaign_type.titleize %>
                  </span>
                  <% if campaign.vibe_status.present? && campaign.vibe_status != 'pending' %>
                    <span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 rounded-full">
                      Vibe
                    </span>
                  <% end %>
                </div>
                
                <span class="text-gray-500 dark:text-gray-400">
                  <%= time_ago_in_words(campaign.created_at) %> ago
                </span>
              </div>
            </div>

            <!-- Campaign Metrics -->
            <div class="p-6">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center">
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">
                    <%= number_to_currency(campaign.budget_in_dollars || 0) %>
                  </p>
                  <p class="text-xs text-gray-600 dark:text-gray-400">Budget</p>
                </div>
                <div class="text-center">
                  <p class="text-2xl font-bold text-gray-900 dark:text-white">
                    <%= campaign.progress_percentage.present? ? "#{campaign.progress_percentage}%" : "0%" %>
                  </p>
                  <p class="text-xs text-gray-600 dark:text-gray-400">Progress</p>
                </div>
              </div>

              <!-- Progress Bar -->
              <div class="mb-4">
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div class="h-2 rounded-full transition-all duration-300 progress-bar-<%= campaign.status %>" 
                       style="width: <%= campaign.progress_percentage || 0 %>%"></div>
                </div>
              </div>

              <!-- Campaign Dates -->
              <div class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                <% if campaign.start_date %>
                  <div class="flex justify-between">
                    <span>Start:</span>
                    <span><%= campaign.start_date.strftime("%b %d, %Y") %></span>
                  </div>
                <% end %>
                <% if campaign.end_date %>
                  <div class="flex justify-between">
                    <span>End:</span>
                    <span><%= campaign.end_date.strftime("%b %d, %Y") %></span>
                  </div>
                <% end %>
              </div>
            </div>

            <!-- Campaign Actions -->
            <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-700">
              <div class="flex justify-between items-center">
                <div class="flex space-x-2">
                  <%= link_to campaign_path(campaign), 
                      class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm font-medium" do %>
                    View
                  <% end %>
                  
                  <%= link_to analytics_campaign_path(campaign), 
                      class: "text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200 text-sm font-medium" do %>
                    Analytics
                  <% end %>
                  
                  <%= link_to edit_campaign_path(campaign), 
                      class: "text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 text-sm font-medium" do %>
                    Edit
                  <% end %>
                </div>

                <!-- Quick Actions -->
                <div class="flex space-x-1">
                  <% if campaign.draft? %>
                    <%= button_to "Activate", activate_campaign_path(campaign),
                        method: :patch,
                        class: "btn-sm btn-success",
                        form: { data: { turbo_confirm: "Are you sure you want to activate this campaign?" } } %>
                  <% elsif campaign.active? %>
                    <%= button_to "Pause", pause_campaign_path(campaign),
                        method: :patch,
                        class: "btn-sm btn-warning",
                        form: { data: { turbo_confirm: "Are you sure you want to pause this campaign?" } } %>
                  <% elsif campaign.paused? %>
                    <%= button_to "Resume", activate_campaign_path(campaign),
                        method: :patch,
                        class: "btn-sm btn-success",
                        form: { data: { turbo_confirm: "Are you sure you want to resume this campaign?" } } %>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>

      <!-- List View (Hidden by default) -->
      <div id="campaigns-list" class="campaigns-list hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Campaign
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Type
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Budget
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Progress
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Created
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <% @campaigns.each do |campaign| %>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900 dark:text-white">
                        <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                      </div>
                      <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                        <%= campaign.description.present? ? truncate(campaign.description, length: 50) : "No description" %>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="campaign-type-badge <%= campaign.campaign_type %>">
                      <%= campaign.campaign_type.titleize %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="status-badge <%= campaign.status %>">
                      <%= campaign.status.titleize %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    <%= number_to_currency(campaign.budget_in_dollars || 0) %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                        <div class="h-2 rounded-full progress-bar-<%= campaign.status %>" style="width: <%= campaign.progress_percentage || 0 %>%"></div>
                      </div>
                      <span class="text-sm text-gray-900 dark:text-white"><%= campaign.progress_percentage || 0 %>%</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <%= campaign.created_at.strftime("%b %d, %Y") %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex justify-end space-x-2">
                      <%= link_to "View", campaign_path(campaign), 
                          class: "text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200" %>
                      <%= link_to "Analytics", analytics_campaign_path(campaign), 
                          class: "text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-200" %>
                      <%= link_to "Edit", edit_campaign_path(campaign), 
                          class: "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="empty-state text-center py-16">
        <div class="mx-auto w-32 h-32 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-700 rounded-full flex items-center justify-center mb-6">
          <svg class="w-16 h-16 text-blue-400 dark:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">No campaigns found</h3>
        <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
          <% if params[:search].present? || params[:status].present? || params[:campaign_type].present? %>
            No campaigns match your current filters. Try adjusting your search criteria.
          <% else %>
            Get started by creating your first marketing campaign to reach your audience and grow your business.
          <% end %>
        </p>
        <%= link_to "Create Campaign", new_campaign_path, class: "btn btn-primary text-base px-6 py-3" %>
      </div>
    <% end %>
  </div>
</div>

<!-- JavaScript for View Toggle -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const viewToggleBtns = document.querySelectorAll('.view-toggle-btn');
  const gridView = document.getElementById('campaigns-grid');
  const listView = document.getElementById('campaigns-list');
  
  viewToggleBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      const view = this.dataset.view;
      
      // Update active state
      viewToggleBtns.forEach(b => b.classList.remove('active', 'bg-white', 'text-gray-900', 'shadow-sm'));
      this.classList.add('active', 'bg-white', 'text-gray-900', 'shadow-sm');
      
      // Toggle views
      if (view === 'grid') {
        gridView.classList.remove('hidden');
        listView.classList.add('hidden');
      } else {
        gridView.classList.add('hidden');
        listView.classList.remove('hidden');
      }
      
      // Store preference
      localStorage.setItem('campaigns-view', view);
    });
  });
  
  // Restore saved view preference
  const savedView = localStorage.getItem('campaigns-view');
  if (savedView) {
    const btn = document.querySelector(`[data-view="${savedView}"]`);
    if (btn) btn.click();
  }
});
</script>

<style>
/* Status badges */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-badge.active {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.status-badge.draft {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.status-badge.paused {
  @apply bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200;
}

.status-badge.completed {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

.status-badge.cancelled {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

/* Campaign type badges */
.campaign-type-badge {
  @apply inline-flex items-center px-2 py-1 rounded text-xs font-medium;
}

.campaign-type-badge.email {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

.campaign-type-badge.social {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200;
}

.campaign-type-badge.seo {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.campaign-type-badge.multi_channel {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200;
}

/* Button styles */
.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700;
}

.btn-sm {
  @apply px-2 py-1 text-xs;
}

.btn-success {
  @apply text-white bg-green-600 hover:bg-green-700 focus:ring-green-500;
}

.btn-warning {
  @apply text-white bg-orange-600 hover:bg-orange-700 focus:ring-orange-500;
}

.btn-danger {
  @apply text-white bg-red-600 hover:bg-red-700 focus:ring-red-500;
}

/* Dark mode button adjustments */
.dark .btn-secondary {
  @apply bg-gray-600 text-gray-200 hover:bg-gray-500;
}

/* View toggle active state */
.view-toggle-btn.active {
  @apply bg-white text-gray-900 shadow-sm dark:bg-gray-600 dark:text-white;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Progress bars with status colors */
.progress-bar-active {
  @apply bg-green-500;
}

.progress-bar-paused {
  @apply bg-orange-500;
}

.progress-bar-completed {
  @apply bg-blue-500;
}

.progress-bar-draft {
  @apply bg-gray-400;
}

.progress-bar-cancelled {
  @apply bg-red-500;
}

/* Campaign cards hover effect */
.campaign-card {
  transition: all 0.2s ease-in-out;
}

.campaign-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .campaign-card:hover {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
}

/* Statistics cards */
.stats-grid .stat-card {
  transition: all 0.2s ease-in-out;
}

.stats-grid .stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>