<% content_for :title, "#{@ai_provider_configuration.provider_name.titleize} Configuration" %>

<div class="space-y-6">
  <!-- Header -->
  <div>
    <nav class="flex" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-4">
        <li>
          <%= link_to admin_ai_provider_configurations_path, class: "text-gray-400 hover:text-gray-500" do %>
            <span>AI Provider Configurations</span>
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="ml-4 text-sm font-medium text-gray-500"><%= @ai_provider_configuration.provider_name.titleize %></span>
          </div>
        </li>
      </ol>
    </nav>
    <div class="mt-4 sm:flex sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 flex items-center">
          <%= @ai_provider_configuration.provider_name.titleize %> - <%= @ai_provider_configuration.ai_model_name %>
          <% if @ai_provider_configuration.is_active? %>
            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Active
            </span>
          <% else %>
            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Inactive
            </span>
          <% end %>
        </h1>
        <p class="mt-2 text-sm text-gray-700">Configuration details and usage statistics for this AI provider.</p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-4 flex space-x-3">
        <%= link_to test_connection_admin_ai_provider_configuration_path(@ai_provider_configuration), 
            method: :post,
            class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
          </svg>
          Test Connection
        <% end %>
        <%= link_to edit_admin_ai_provider_configuration_path(@ai_provider_configuration), 
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
          </svg>
          Edit
        <% end %>
      </div>
    </div>
  </div>

  <!-- Configuration Details -->
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900">Configuration Details</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Core settings and parameters for this AI provider.</p>
    </div>
    <div class="border-t border-gray-200">
      <dl>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Provider</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0"><%= @ai_provider_configuration.provider_name.titleize %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Model Name</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0"><%= @ai_provider_configuration.ai_model_name %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Base URL</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <%= @ai_provider_configuration.base_url.present? ? @ai_provider_configuration.base_url : "Default endpoint" %>
          </dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Max Tokens</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0"><%= number_with_delimiter(@ai_provider_configuration.max_tokens) %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Temperature</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0"><%= @ai_provider_configuration.temperature %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Cost per Token</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">$<%= @ai_provider_configuration.cost_per_token %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Priority</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <%= @ai_provider_configuration.priority %>
            <span class="text-gray-500 text-xs ml-2">(1 = highest, 10 = lowest)</span>
          </dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Supported Tasks</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <% if @ai_provider_configuration.task_types.any? %>
              <div class="flex flex-wrap gap-2">
                <% @ai_provider_configuration.task_types.each do |task_type| %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <%= task_type.humanize %>
                  </span>
                <% end %>
              </div>
            <% else %>
              <span class="text-gray-500 italic">No specific tasks configured</span>
            <% end %>
          </dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">API Key</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <span class="font-mono text-xs">••••••••••••••••</span>
            <span class="text-gray-500 text-xs ml-2">(encrypted)</span>
          </dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Status</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <% if @ai_provider_configuration.is_active? %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                  <circle cx="4" cy="4" r="3"/>
                </svg>
                Active
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-gray-400" fill="currentColor" viewBox="0 0 8 8">
                  <circle cx="4" cy="4" r="3"/>
                </svg>
                Inactive
              </span>
            <% end %>
          </dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Created</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <%= time_ago_in_words(@ai_provider_configuration.created_at) %> ago
          </dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <%= time_ago_in_words(@ai_provider_configuration.updated_at) %> ago
          </dd>
        </div>
      </dl>
    </div>
  </div>

  <!-- Usage Statistics -->
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900">Usage Statistics</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Request metrics and performance data for this provider.</p>
    </div>
    <div class="border-t border-gray-200">
      <dl class="sm:divide-y sm:divide-gray-200">
        <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
          <dt class="text-sm font-medium text-gray-500">Total Requests</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <div class="flex items-center">
              <span class="text-2xl font-semibold"><%= number_with_delimiter(@usage_stats[:total_requests]) %></span>
              <span class="ml-2 text-sm text-gray-500">requests</span>
            </div>
          </dd>
        </div>
        <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
          <dt class="text-sm font-medium text-gray-500">Success Rate</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <% success_rate = (@usage_stats[:successful_requests].to_f / @usage_stats[:total_requests] * 100).round(1) %>
            <div class="flex items-center">
              <span class="text-2xl font-semibold"><%= success_rate %>%</span>
              <div class="ml-4 flex-1">
                <div class="bg-gray-200 rounded-full h-2">
                  <div class="bg-green-500 h-2 rounded-full" style="width: <%= success_rate %>%"></div>
                </div>
              </div>
            </div>
          </dd>
        </div>
        <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
          <dt class="text-sm font-medium text-gray-500">Average Response Time</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <span class="text-2xl font-semibold"><%= @usage_stats[:average_response_time] %></span>
            <span class="ml-1 text-sm text-gray-500">ms</span>
          </dd>
        </div>
        <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
          <dt class="text-sm font-medium text-gray-500">Total Cost</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
            <span class="text-2xl font-semibold">$<%= sprintf('%.2f', @usage_stats[:total_cost]) %></span>
            <span class="ml-2 text-sm text-gray-500">USD</span>
          </dd>
        </div>
      </dl>
    </div>
  </div>
</div>
