<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Email Preview - <%= @email_campaign.subject_line %></title>
  <style>
    /* Email-safe CSS styles */
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333333;
      background-color: #f8f9fa;
    }
    
    .email-container {
      max-width: 600px;
      margin: 20px auto;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .email-header {
      background-color: #f8f9fa;
      padding: 20px;
      border-bottom: 1px solid #e9ecef;
    }
    
    .email-meta {
      font-size: 14px;
      color: #6c757d;
      margin-bottom: 10px;
    }
    
    .email-subject {
      font-size: 24px;
      font-weight: 600;
      color: #212529;
      margin: 0;
    }
    
    .email-preview-text {
      font-size: 16px;
      color: #6c757d;
      margin: 10px 0 0 0;
      font-style: italic;
    }
    
    .email-body {
      padding: 30px;
    }
    
    .email-content {
      font-size: 16px;
      line-height: 1.6;
      color: #333333;
    }
    
    .email-content h1 {
      color: #212529;
      font-size: 28px;
      margin-bottom: 20px;
    }
    
    .email-content h2 {
      color: #495057;
      font-size: 24px;
      margin-bottom: 16px;
    }
    
    .email-content h3 {
      color: #495057;
      font-size: 20px;
      margin-bottom: 12px;
    }
    
    .email-content p {
      margin-bottom: 16px;
    }
    
    .email-content a {
      color: #007bff;
      text-decoration: none;
    }
    
    .email-content a:hover {
      text-decoration: underline;
    }
    
    .email-footer {
      background-color: #f8f9fa;
      padding: 20px;
      border-top: 1px solid #e9ecef;
      font-size: 14px;
      color: #6c757d;
      text-align: center;
    }
    
    .preview-controls {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 1000;
    }
    
    .preview-controls button {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 0 5px;
      font-size: 14px;
    }
    
    .preview-controls button:hover {
      background: #0056b3;
    }
    
    .preview-controls .close-btn {
      background: #6c757d;
    }
    
    .preview-controls .close-btn:hover {
      background: #545b62;
    }
    
    @media (max-width: 640px) {
      .email-container {
        margin: 10px;
        border-radius: 0;
      }
      
      .email-body {
        padding: 20px;
      }
      
      .preview-controls {
        position: static;
        margin: 10px;
        text-align: center;
      }
    }
  </style>
</head>
<body>
  <!-- Preview Controls -->
  <div class="preview-controls">
    <button onclick="window.close()" class="close-btn">Close Preview</button>
    <button onclick="window.print()">Print</button>
  </div>

  <!-- Email Container -->
  <div class="email-container">
    <!-- Email Header -->
    <div class="email-header">
      <div class="email-meta">
        <strong>From:</strong> <%= @email_campaign.from_name.presence || "Unknown Sender" %> &lt;<%= @email_campaign.from_email.presence || "<EMAIL>" %>&gt;<br>
        <strong>Subject:</strong> <%= @email_campaign.subject_line.presence || "No Subject" %>
        <% if @email_campaign.respond_to?(:estimated_send_time) && @email_campaign.estimated_send_time.present? %>
          <br><strong>Estimated Send Time:</strong> <%= time_ago_in_words(@email_campaign.estimated_send_time) %> ago
        <% end %>
      </div>

      <h1 class="email-subject"><%= @email_campaign.subject_line.presence || "No Subject" %></h1>

      <% if @email_campaign.preview_text.present? %>
        <p class="email-preview-text"><%= @email_campaign.preview_text %></p>
      <% end %>
    </div>

    <!-- Email Body -->
    <div class="email-body">
      <div class="email-content">
        <% if @email_campaign.content.present? %>
          <%= @email_campaign.content.html_safe %>
        <% else %>
          <p style="color: #6c757d; font-style: italic;">No email content available.</p>
        <% end %>
      </div>

      <!-- Attachments Section -->
      <% if @email_campaign.has_attachments? %>
        <div class="email-attachments" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
          <h3 style="color: #495057; font-size: 18px; margin-bottom: 16px;">Attachments</h3>

          <!-- Images -->
          <% if @email_campaign.images.attached? %>
            <div style="margin-bottom: 20px;">
              <h4 style="color: #6c757d; font-size: 14px; margin-bottom: 10px;">Images</h4>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                <% @email_campaign.images.each do |image| %>
                  <div style="border: 1px solid #dee2e6; border-radius: 8px; overflow: hidden;">
                    <%= image_tag image.variant(:medium),
                        style: "width: 100%; height: 120px; object-fit: cover;" %>
                    <div style="padding: 8px; background-color: #f8f9fa; font-size: 12px; color: #6c757d;">
                      <%= truncate(image.filename.to_s, length: 20) %>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>

          <!-- Documents -->
          <% if @email_campaign.documents.attached? %>
            <div style="margin-bottom: 20px;">
              <h4 style="color: #6c757d; font-size: 14px; margin-bottom: 10px;">Documents</h4>
              <div style="space-y: 8px;">
                <% @email_campaign.documents.each do |document| %>
                  <div style="display: flex; align-items: center; padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; margin-bottom: 8px;">
                    <svg style="width: 20px; height: 20px; color: #6c757d; margin-right: 12px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <div>
                      <div style="font-weight: 500; color: #212529; font-size: 14px;"><%= document.filename %></div>
                      <div style="color: #6c757d; font-size: 12px;"><%= number_to_human_size(document.byte_size) %></div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>

          <!-- Audio Files -->
          <% if @email_campaign.audio_files.attached? %>
            <div style="margin-bottom: 20px;">
              <h4 style="color: #6c757d; font-size: 14px; margin-bottom: 10px;">Audio Files</h4>
              <div style="space-y: 8px;">
                <% @email_campaign.audio_files.each do |audio| %>
                  <div style="display: flex; align-items: center; padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; margin-bottom: 8px;">
                    <svg style="width: 20px; height: 20px; color: #6c757d; margin-right: 12px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"/>
                    </svg>
                    <div>
                      <div style="font-weight: 500; color: #212529; font-size: 14px;"><%= audio.filename %></div>
                      <div style="color: #6c757d; font-size: 12px;"><%= number_to_human_size(audio.byte_size) %></div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>

          <!-- Video Files -->
          <% if @email_campaign.video_files.attached? %>
            <div style="margin-bottom: 20px;">
              <h4 style="color: #6c757d; font-size: 14px; margin-bottom: 10px;">Video Files</h4>
              <div style="space-y: 8px;">
                <% @email_campaign.video_files.each do |video| %>
                  <div style="display: flex; align-items: center; padding: 12px; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; margin-bottom: 8px;">
                    <svg style="width: 20px; height: 20px; color: #6c757d; margin-right: 12px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                    <div>
                      <div style="font-weight: 500; color: #212529; font-size: 14px;"><%= video.filename %></div>
                      <div style="color: #6c757d; font-size: 12px;"><%= number_to_human_size(video.byte_size) %></div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>

    <!-- Email Footer -->
    <div class="email-footer">
      <p>
        This is a preview of your email campaign.<br>
        Campaign: <strong><%= @campaign.name %></strong>
      </p>
      <p>
        <small>
          Generated by AI Marketing Hub &bull; 
          <%= Date.current.strftime("%B %d, %Y") %>
        </small>
      </p>
    </div>
  </div>

  <script>
    // Auto-focus for better UX
    document.addEventListener('DOMContentLoaded', function() {
      // Add any interactive functionality here if needed
      console.log('Email preview loaded successfully');
    });
  </script>
</body>
</html>
