<% content_for :title, "Edit Email Content - #{@campaign.name}" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center space-x-4">
          <%= link_to campaign_email_content_path(@campaign),
              class: "text-gray-500 hover:text-gray-700 transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          <% end %>
          <h1 class="text-xl font-semibold text-gray-900">Edit Email Content</h1>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <%= @campaign.campaign_type.humanize %>
          </span>
        </div>

        <div class="flex items-center space-x-3">
          <%= link_to "Preview", preview_campaign_email_content_path(@campaign),
              class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",
              data: { turbo_method: :get } %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <%= link_to dashboard_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
            </svg>
            Dashboard
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <%= link_to campaigns_path, class: "ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors md:ml-2" do %>
              Campaigns
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <%= link_to campaign_path(@campaign), class: "ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors md:ml-2" do %>
              <%= @campaign.name %>
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <%= link_to campaign_email_content_path(@campaign), class: "ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors md:ml-2" do %>
              Email Content
            <% end %>
          </div>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Edit</span>
          </div>
        </li>
      </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Content -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center space-x-3 mb-6">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
              </svg>
            </div>
            <div>
              <h2 class="text-lg font-medium text-gray-900">Edit Email Campaign Content</h2>
              <p class="text-sm text-gray-500">Update your email content and settings</p>
            </div>
          </div>

      <%= form_with model: @email_campaign, url: campaign_email_content_path(@campaign), method: :patch, local: true, class: "space-y-6" do |form| %>
        <% if @email_campaign.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
              </svg>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                  <% @email_campaign.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :subject_line, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :subject_line,
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                placeholder: "Enter your email subject line...",
                maxlength: 150 %>
            <p class="text-xs text-gray-500 mt-1">Maximum 150 characters</p>
          </div>

          <div>
            <%= form.label :preview_text, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :preview_text,
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                placeholder: "Preview text (optional)...",
                maxlength: 200 %>
            <p class="text-xs text-gray-500 mt-1">Maximum 200 characters</p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :from_name, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :from_name,
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                placeholder: "Sender name..." %>
          </div>

          <div>
            <%= form.label :from_email, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.email_field :from_email,
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                placeholder: "<EMAIL>" %>
          </div>
        </div>

        <div>
          <%= form.label :content, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_area :content,
              class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
              rows: 12,
              placeholder: "Write your email content here..." %>
          <p class="text-xs text-gray-500 mt-1">You can use merge tags like {{first_name}} for personalization</p>
        </div>

        <!-- File Upload Section -->
        <%= render "shared/file_upload_section", form: form, email_campaign: @email_campaign %>

        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
          <div class="flex space-x-3">
            <%= link_to "Cancel", campaign_email_content_path(@campaign),
                class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
            <%= link_to "Delete", campaign_email_content_path(@campaign),
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",
                data: {
                  turbo_method: :delete,
                  turbo_confirm: "Are you sure you want to delete this email content?"
                } %>
          </div>
          <div class="flex space-x-3">
            <%= form.submit "Save Changes",
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
          </div>
        </div>
      <% end %>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Campaign Info -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center space-x-3 mb-4">
            <div class="flex-shrink-0">
              <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">Campaign Info</h3>
              <p class="text-sm text-gray-500">Current campaign details</p>
            </div>
          </div>
          <dl class="space-y-3">
            <div class="flex justify-between">
              <dt class="text-sm font-medium text-gray-500">Name</dt>
              <dd class="text-sm text-gray-900 font-medium"><%= @campaign.name %></dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-sm font-medium text-gray-500">Type</dt>
              <dd>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <%= @campaign.campaign_type&.humanize %>
                </span>
              </dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-sm font-medium text-gray-500">Budget</dt>
              <dd class="text-sm text-gray-900 font-medium"><%= number_to_currency(@campaign.budget_cents / 100.0) %></dd>
            </div>
          </dl>
        </div>

        <!-- Content Tips -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center space-x-3 mb-4">
            <div class="flex-shrink-0">
              <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">Content Tips</h3>
              <p class="text-sm text-gray-500">Best practices for email content</p>
            </div>
          </div>
          <ul class="space-y-2 text-sm text-gray-600">
            <li class="flex items-start">
              <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              Keep subject lines under 50 characters
            </li>
            <li class="flex items-start">
              <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              Use personalization with merge tags
            </li>
            <li class="flex items-start">
              <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              Include clear call-to-action
            </li>
            <li class="flex items-start">
              <svg class="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              Test on mobile devices
            </li>
          </ul>
        </div>

        <!-- AI Content Optimization -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center space-x-3 mb-4">
            <div class="flex-shrink-0">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">AI Optimization</h3>
              <p class="text-sm text-gray-500">Improve your email content</p>
            </div>
          </div>

          <%= form_with url: optimize_content_campaign_email_content_path(@campaign),
              method: :post, local: true, class: "space-y-4" do |form| %>

            <div>
              <%= form.label :optimization_goals, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.text_field :optimization_goals,
                  placeholder: "e.g., engagement, conversion, click-through rate",
                  class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",
                  value: "engagement, conversion" %>
              <p class="text-xs text-gray-500 mt-1">Separate multiple goals with commas</p>
            </div>

            <div>
              <%= form.label :target_metrics, class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.select :target_metrics,
                  options_for_select([
                    ['Increase Open Rate', 'open_rate'],
                    ['Improve Click-Through Rate', 'click_rate'],
                    ['Boost Conversion Rate', 'conversion_rate'],
                    ['Enhance Engagement', 'engagement'],
                    ['Reduce Unsubscribes', 'retention']
                  ], 'engagement'),
                  {}, { class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm" } %>
            </div>

            <%= form.submit "Optimize Content",
                class: "w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors",
                data: { turbo_confirm: "This will update your email content with AI optimizations. Continue?" } %>
          <% end %>
        </div>
      </div>
    </div>

    <!-- AI Provider Status at bottom -->
    <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center space-x-3 mb-4">
        <div class="flex-shrink-0">
          <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-medium text-gray-900">Available AI Models</h3>
          <p class="text-sm text-gray-500">Current status of AI content generation</p>
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
          <div class="flex items-center space-x-3">
            <div class="w-3 h-3 bg-green-400 rounded-full"></div>
            <span class="text-sm font-medium text-green-900">OpenAI GPT-4</span>
          </div>
          <span class="text-xs text-green-700 bg-green-100 px-2 py-1 rounded-full">Active</span>
        </div>
        
        <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div class="flex items-center space-x-3">
            <div class="w-3 h-3 bg-blue-400 rounded-full"></div>
            <span class="text-sm font-medium text-blue-900">Claude 3.5 Sonnet</span>
          </div>
          <span class="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded-full">Available</span>
        </div>
        
        <div class="flex items-center justify-between p-4 bg-purple-50 rounded-lg border border-purple-200">
          <div class="flex items-center space-x-3">
            <div class="w-3 h-3 bg-purple-400 rounded-full"></div>
            <span class="text-sm font-medium text-purple-900">Gemini Pro</span>
          </div>
          <span class="text-xs text-purple-700 bg-purple-100 px-2 py-1 rounded-full">Available</span>
        </div>
      </div>
    </div>
  </div>
</div>
