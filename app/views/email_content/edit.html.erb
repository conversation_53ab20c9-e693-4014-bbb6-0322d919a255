<% content_for :title, "Edit Email Content - #{@campaign.name}" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center space-x-4">
          <%= link_to campaign_email_content_path(@campaign),
              class: "text-gray-500 hover:text-gray-700 transition-colors" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          <% end %>
          <h1 class="text-xl font-semibold text-gray-900">Edit Email Content</h1>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <%= @campaign.campaign_type.humanize %>
          </span>
        </div>

        <div class="flex items-center space-x-3">
          <%= link_to "Preview", preview_campaign_email_content_path(@campaign),
              class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",
              data: { turbo_method: :get } %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="mb-6">
        <h2 class="text-lg font-medium text-gray-900">Edit Email Campaign Content</h2>
        <p class="text-sm text-gray-600 mt-1">Update your email content and settings</p>
      </div>

      <%= form_with model: [@campaign, @email_campaign], local: true, class: "space-y-6" do |form| %>
        <% if @email_campaign.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
              </svg>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                  <% @email_campaign.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :subject_line, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :subject_line,
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                placeholder: "Enter your email subject line...",
                maxlength: 150 %>
            <p class="text-xs text-gray-500 mt-1">Maximum 150 characters</p>
          </div>

          <div>
            <%= form.label :preview_text, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :preview_text,
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                placeholder: "Preview text (optional)...",
                maxlength: 200 %>
            <p class="text-xs text-gray-500 mt-1">Maximum 200 characters</p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= form.label :from_name, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :from_name,
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                placeholder: "Sender name..." %>
          </div>

          <div>
            <%= form.label :from_email, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.email_field :from_email,
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                placeholder: "<EMAIL>" %>
          </div>
        </div>

        <div>
          <%= form.label :content, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_area :content,
              class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
              rows: 12,
              placeholder: "Write your email content here..." %>
          <p class="text-xs text-gray-500 mt-1">You can use merge tags like {{first_name}} for personalization</p>
        </div>

        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
          <div class="flex space-x-3">
            <%= link_to "Cancel", campaign_email_content_path(@campaign),
                class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
            <%= link_to "Delete", campaign_email_content_path(@campaign),
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",
                data: {
                  turbo_method: :delete,
                  turbo_confirm: "Are you sure you want to delete this email content?"
                } %>
          </div>
          <div class="flex space-x-3">
            <%= form.submit "Save Changes",
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
          </div>
        </div>
      <% end %>
    </div>

    <!-- AI Content Optimization -->
    <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">AI Content Optimization</h3>
      <p class="text-sm text-gray-600 mb-4">Optimize your existing email content for better engagement and conversion rates.</p>

      <%= form_with url: optimize_content_campaign_email_content_path(@campaign),
          method: :post, local: true, class: "space-y-4" do |form| %>

        <div>
          <%= form.label :optimization_goals, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :optimization_goals,
              placeholder: "e.g., engagement, conversion, click-through rate",
              class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
              value: "engagement, conversion" %>
          <p class="text-xs text-gray-500 mt-1">Separate multiple goals with commas</p>
        </div>

        <div>
          <%= form.label :target_metrics, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.select :target_metrics,
              options_for_select([
                ['Increase Open Rate', 'open_rate'],
                ['Improve Click-Through Rate', 'click_rate'],
                ['Boost Conversion Rate', 'conversion_rate'],
                ['Enhance Engagement', 'engagement'],
                ['Reduce Unsubscribes', 'retention']
              ], 'engagement'),
              {}, { class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
        </div>

        <%= form.submit "Optimize Content",
            class: "w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",
            data: { turbo_confirm: "This will update your email content with AI optimizations. Continue?" } %>
      <% end %>
    </div>
  </div>
</div>
