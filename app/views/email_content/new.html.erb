<% content_for :title, "Generate Email Content" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center space-x-4">
          <%= link_to campaign_path(@campaign),
              class: "text-gray-500 hover:text-gray-700 transition-colors",
              data: { turbo_method: :get } do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          <% end %>
          <h1 class="text-xl font-semibold text-gray-900">Generate Email Content</h1>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <%= @campaign.campaign_type&.humanize || "Standard" %>
          </span>
        </div>

        <div class="flex items-center space-x-3">
          <%= link_to "Back to Campaign", campaign_path(@campaign),
              class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

      <!-- Campaign Information Sidebar -->
      <div class="lg:col-span-1 space-y-6">
        <!-- Campaign Details Card -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center mb-4">
            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <h2 class="text-lg font-medium text-gray-900">Campaign Information</h2>
          </div>

          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500">Campaign Name</dt>
              <dd class="mt-1 text-sm text-gray-900 font-medium"><%= @campaign.name %></dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Type</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <%= @campaign.campaign_type&.humanize || "Standard" %>
                </span>
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Status</dt>
              <dd class="mt-1">
                <%= render 'dashboard/status_badge', status: @campaign.status %>
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Audience</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <% if @campaign.respond_to?(:audience) && @campaign.audience.present? %>
                  <%= link_to @campaign.audience.name, audience_path(@campaign.audience),
                      class: "text-blue-600 hover:text-blue-800 font-medium" %>
                  <span class="text-gray-500 text-xs block mt-1">
                    <%= @campaign.audience.subscriber_count || 0 %> subscribers
                  </span>
                <% else %>
                  <span class="text-gray-500">Not specified</span>
                <% end %>
              </dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Created</dt>
              <dd class="mt-1 text-sm text-gray-900"><%= @campaign.created_at.strftime("%B %d, %Y") %></dd>
            </div>

            <div>
              <dt class="text-sm font-medium text-gray-500">Target Date</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <% if @campaign.respond_to?(:target_completion_date) && @campaign.target_completion_date.present? %>
                  <%= @campaign.target_completion_date.strftime("%B %d, %Y") %>
                <% else %>
                  <span class="text-gray-500">Not specified</span>
                <% end %>
              </dd>
            </div>
          </dl>
        </div>

        <!-- AI Provider Status -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center mb-4">
            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
            </svg>
            <h3 class="text-base font-medium text-gray-900">AI Providers</h3>
          </div>

          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                <span class="text-sm text-gray-900">OpenAI GPT-4o</span>
              </div>
              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                Available
              </span>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                <span class="text-sm text-gray-900">Anthropic Claude</span>
              </div>
              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                Available
              </span>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <% if @campaign.respond_to?(:audience) && @campaign.audience.present? %>
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-base font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-3">
              <button type="button"
                      class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                      data-controller="audience-insights"
                      data-action="audience-insights#show"
                      data-audience-insights-audience-id-value="<%= @campaign.audience.id %>">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
                View Audience Insights
              </button>

              <button type="button"
                      class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                      data-controller="template-library"
                      data-action="template-library#show">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
                Browse Template Library
              </button>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Content Generation Panel -->
      <div class="lg:col-span-2">
        <%= render 'shared/content_generation_panel', 
          campaign: @campaign,
          endpoint: generate_ai_content_campaign_email_content_path(@campaign),
          apply_endpoint: campaign_email_content_path(@campaign),
          form_id: 'email_generation_form',
          include_version_history: true,
          brand_voice_options: ['professional', 'casual', 'authoritative', 'playful', 'technical', 'inspirational'],
          email_type_options: ['promotional', 'newsletter', 'welcome', 'announcement', 'follow-up', 'abandoned_cart', 're-engagement'],
          custom_prompt_placeholder: 'Enter key message or specific instructions for this email content...'
        %>
      </div>
    </div>

    <!-- AI Budget Usage & Analytics -->
    <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- AI Budget Usage Card -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center mb-4">
          <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
          </svg>
          <h3 class="text-lg font-medium text-gray-900">AI Budget Usage</h3>
        </div>

        <div class="space-y-4">
          <div>
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm font-medium text-gray-700">Current Month</span>
              <span class="text-sm text-gray-500">65%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-yellow-500 h-2 rounded-full transition-all duration-300" style="width: 65%;"></div>
            </div>
            <p class="text-xs text-gray-500 mt-2">Estimated cost per generation: $0.05 - $0.15</p>
          </div>

          <div class="pt-4 border-t border-gray-200">
            <button type="button"
                    class="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
                    data-turbo-frame="modal"
                    data-turbo-action="advance">
              Learn more about AI budget →
            </button>
          </div>
        </div>
      </div>

      <!-- Generation Statistics Card -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center mb-4">
          <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          <h3 class="text-lg font-medium text-gray-900">Generation Statistics</h3>
        </div>

        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              <span class="text-sm text-gray-600">Successful generations</span>
            </div>
            <span class="text-sm font-semibold text-gray-900">24</span>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <svg class="w-4 h-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L10 8.586l1.293-1.293a1 1 0 111.414 1.414L11.414 10l1.293 1.293a1 1 0 01-1.414 1.414L10 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L8.707 10 7.414 8.707a1 1 0 011.414-1.414L10 8.586l1.293-1.293z" clip-rule="evenodd"/>
              </svg>
              <span class="text-sm text-gray-600">Failed generations</span>
            </div>
            <span class="text-sm font-semibold text-gray-900">3</span>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <svg class="w-4 h-4 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
              </svg>
              <span class="text-sm text-gray-600">Average generation time</span>
            </div>
            <span class="text-sm font-semibold text-gray-900">8.2s</span>
          </div>
        </div>

        <div class="pt-4 border-t border-gray-200 mt-4">
          <p class="text-xs text-gray-500">
            Budget refreshes on the 1st of each month
          </p>
        </div>
      </div>
    </div>
  </div>
</div>

  </div>
</div>

<!-- Render the audience insights modal partial -->
<%= render 'shared/audience_insights_modal' %>

<!-- Render the template library modal partial -->
<%= render 'shared/template_library_modal' %>
