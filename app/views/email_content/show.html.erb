<% content_for :title, "Email Content - #{@campaign.name}" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center space-x-4">
          <%= link_to campaign_path(@campaign),
              class: "text-gray-500 hover:text-gray-700 transition-colors",
              data: { turbo_method: :get } do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          <% end %>
          <h1 class="text-xl font-semibold text-gray-900">Email Content</h1>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <%= @campaign.campaign_type.humanize %>
          </span>
        </div>

        <div class="flex items-center space-x-3">
          <% if @email_campaign.persisted? %>
            <%= link_to "Edit Content", edit_campaign_email_content_path(@campaign),
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
          <% else %>
            <%= link_to "Create Content", new_campaign_email_content_path(@campaign),
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <%= link_to dashboard_index_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors" do %>
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
            </svg>
            Dashboard
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <%= link_to campaigns_path, class: "ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors md:ml-2" do %>
              Campaigns
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <%= link_to campaign_path(@campaign), class: "ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors md:ml-2" do %>
              <%= @campaign.name %>
            <% end %>
          </div>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Email Content</span>
          </div>
        </li>
      </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

      <!-- Email Content Form/Display -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                </svg>
              </div>
              <div>
                <h2 class="text-lg font-medium text-gray-900">Email Campaign Content</h2>
                <p class="text-sm text-gray-500">Manage and preview your email content</p>
              </div>
            </div>
            <% if @email_campaign.persisted? %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                Content Ready
              </span>
            <% else %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
                Setup Required
              </span>
            <% end %>
          </div>

          <% if @email_campaign.persisted? %>
            <!-- Display Email Content -->
            <div class="space-y-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Subject Line</label>
                <div class="p-3 bg-gray-50 rounded-md border border-gray-200">
                  <%= @email_campaign.subject_line %>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Preview Text</label>
                <div class="p-3 bg-gray-50 rounded-md border border-gray-200">
                  <%= @email_campaign.preview_text.presence || "No preview text set" %>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">From Name</label>
                <div class="p-3 bg-gray-50 rounded-md border border-gray-200">
                  <%= @email_campaign.from_name %>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">From Email</label>
                <div class="p-3 bg-gray-50 rounded-md border border-gray-200">
                  <%= @email_campaign.from_email %>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email Content</label>
                <div class="p-4 bg-gray-50 rounded-md border border-gray-200 min-h-[200px]">
                  <%= simple_format(@email_campaign.content) %>
                </div>
              </div>
            </div>
          <% else %>
            <!-- Setup Required Message -->
            <div class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No email content yet</h3>
              <p class="mt-1 text-sm text-gray-500">Get started by creating your email campaign content.</p>
              <div class="mt-6">
                <%= link_to "Create Email Content", new_campaign_email_content_path(@campaign),
                    class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Campaign Info Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center space-x-3 mb-4">
            <div class="flex-shrink-0">
              <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">Campaign Details</h3>
              <p class="text-sm text-gray-500">Overview of your campaign</p>
            </div>
          </div>
          <dl class="space-y-4">
            <div class="flex justify-between items-start">
              <dt class="text-sm font-medium text-gray-500">Name</dt>
              <dd class="text-sm text-gray-900 font-medium text-right"><%= @campaign.name %></dd>
            </div>
            <div class="flex justify-between items-start">
              <dt class="text-sm font-medium text-gray-500">Type</dt>
              <dd>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <%= @campaign.campaign_type&.humanize %>
                </span>
              </dd>
            </div>
            <div class="flex justify-between items-start">
              <dt class="text-sm font-medium text-gray-500">Target Audience</dt>
              <dd class="text-sm text-gray-900 text-right max-w-32 truncate" title="<%= @campaign.target_audience %>">
                <%= @campaign.target_audience %>
              </dd>
            </div>
            <div class="flex justify-between items-start">
              <dt class="text-sm font-medium text-gray-500">Budget</dt>
              <dd class="text-sm text-gray-900 font-medium"><%= number_to_currency(@campaign.budget_cents / 100.0) %></dd>
            </div>
            <div class="flex justify-between items-start">
              <dt class="text-sm font-medium text-gray-500">Status</dt>
              <dd>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                  <%= case @campaign.status
                      when 'active' then 'bg-green-100 text-green-800'
                      when 'draft' then 'bg-gray-100 text-gray-800'
                      when 'paused' then 'bg-yellow-100 text-yellow-800'
                      when 'completed' then 'bg-blue-100 text-blue-800'
                      else 'bg-gray-100 text-gray-800'
                      end %>">
                  <%= @campaign.status.humanize %>
                </span>
              </dd>
            </div>
          </dl>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div class="space-y-3">
            <% if @email_campaign.persisted? %>
              <%= link_to edit_campaign_email_content_path(@campaign),
                  class: "w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                Edit Content
              <% end %>
              <%= link_to preview_campaign_email_content_path(@campaign),
                  class: "w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",
                  data: { turbo_method: :get } do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                </svg>
                Preview Email
              <% end %>
            <% else %>
              <%= link_to new_campaign_email_content_path(@campaign),
                  class: "w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Create Email Content
              <% end %>
            <% end %>
          </div>
        </div>

        <!-- AI Content Generation -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center space-x-3 mb-4">
            <div class="flex-shrink-0">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">AI Content Generation</h3>
              <p class="text-sm text-gray-500">Generate email content with AI</p>
            </div>
          </div>

          <%= form_with url: generate_ai_content_campaign_email_content_path(@campaign),
              method: :post, local: true, class: "space-y-4" do |form| %>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Brand Voice</label>
              <%= form.select :brand_voice,
                  options_for_select([
                    ['Professional', 'professional'],
                    ['Friendly', 'friendly'],
                    ['Casual', 'casual'],
                    ['Authoritative', 'authoritative'],
                    ['Playful', 'playful']
                  ], 'professional'),
                  {}, { class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Email Type</label>
              <%= form.select :email_type,
                  options_for_select([
                    ['Promotional', 'promotional'],
                    ['Newsletter', 'newsletter'],
                    ['Welcome', 'welcome'],
                    ['Follow-up', 'followup'],
                    ['Announcement', 'announcement']
                  ], 'promotional'),
                  {}, { class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" } %>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Key Message</label>
              <%= form.text_area :key_message,
                  placeholder: "What's the main message you want to convey?",
                  class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                  rows: 3 %>
            </div>

            <%= form.submit "Generate AI Content",
                class: "w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors",
                data: { turbo_confirm: "This will replace existing content. Continue?" } %>
          <% end %>
        </div>

        <!-- AI Provider Status -->
        <%= render 'shared/ai_provider_status_comprehensive' %>

        <!-- Campaign Info -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Preview</h3>
          
          <% if @email_campaign.persisted? %>
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Estimated Open Rate</span>
                <span class="text-sm font-medium text-green-600">24-28%</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Estimated Click Rate</span>
                <span class="text-sm font-medium text-blue-600">3-5%</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Subject Line Score</span>
                <span class="text-sm font-medium text-indigo-600">85/100</span>
              </div>
              
              <div class="pt-3 border-t border-gray-200">
                <button type="button" class="w-full text-left text-sm text-blue-600 hover:text-blue-800 transition-colors">
                  View detailed analytics →
                </button>
              </div>
            </div>
          <% else %>
            <div class="text-center py-4">
              <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
              <p class="text-sm text-gray-500 mt-2">Performance data will be available after content creation</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
