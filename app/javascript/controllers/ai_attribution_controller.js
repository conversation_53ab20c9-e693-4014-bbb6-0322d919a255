import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "toggleButton", "toggleText", "toggleIcon", "detailsPanel"
  ]
  
  static values = {
    expanded: { type: Boolean, default: false },
    objectType: String,
    objectId: Number
  }

  connect() {
    this.updateToggleState()
  }

  toggleExpanded() {
    this.expandedValue = !this.expandedValue
    this.updateToggleState()
    
    if (this.hasDetailsPanelTarget) {
      if (this.expandedValue) {
        this.detailsPanelTarget.classList.remove('hidden')
        this.loadDetailedData()
      } else {
        this.detailsPanelTarget.classList.add('hidden')
      }
    }
  }

  showDetails(event) {
    const field = event.currentTarget.dataset.field
    this.showFieldDetailsModal(field)
  }

  showFieldDetails(event) {
    const field = event.currentTarget.dataset.field
    this.showFieldDetailsModal(field)
  }

  exportData(event) {
    const field = event.currentTarget.dataset.field
    this.exportFieldData(field)
  }

  exportReport() {
    this.exportFullReport()
  }

  // Private methods
  updateToggleState() {
    if (this.hasToggleTextTarget) {
      this.toggleTextTarget.textContent = this.expandedValue ? 'Show Less' : 'Show Details'
    }
    
    if (this.hasToggleIconTarget) {
      if (this.expandedValue) {
        this.toggleIconTarget.classList.add('rotate-180')
      } else {
        this.toggleIconTarget.classList.remove('rotate-180')
      }
    }
  }

  loadDetailedData() {
    // Load additional data if needed
    if (!this.detailedDataLoaded) {
      this.fetchDetailedAttributionData()
      this.detailedDataLoaded = true
    }
  }

  fetchDetailedAttributionData() {
    if (!this.objectTypeValue || !this.objectIdValue) return

    const url = `/api/ai_attribution/${this.objectTypeValue.toLowerCase()}/${this.objectIdValue}/details`
    
    fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      return response.json()
    })
    .then(data => {
      this.updateDetailedView(data)
    })
    .catch(error => {
      console.error('Failed to load detailed attribution data:', error)
      this.showNotification('Failed to load detailed information', 'error')
    })
  }

  updateDetailedView(data) {
    // Update the detailed view with fresh data
    // This could involve updating specific elements or re-rendering sections
    console.log('Detailed attribution data:', data)
  }

  showFieldDetailsModal(field) {
    const modalData = this.getFieldModalData(field)
    
    // Create and show modal
    const modal = this.createFieldDetailsModal(modalData)
    document.body.appendChild(modal)
    
    // Show modal with animation
    setTimeout(() => {
      modal.classList.remove('opacity-0')
      modal.querySelector('.modal-content').classList.remove('scale-95')
      modal.querySelector('.modal-content').classList.add('scale-100')
    }, 10)
  }

  getFieldModalData(field) {
    // Get field-specific data from the page
    const fieldDataScript = document.querySelector(`script[data-ai-attribution-data="${field}"]`)
    if (fieldDataScript) {
      try {
        return JSON.parse(fieldDataScript.textContent)
      } catch (e) {
        console.error('Failed to parse field data:', e)
      }
    }
    
    return { field: field, error: 'No data available' }
  }

  createFieldDetailsModal(data) {
    const modal = document.createElement('div')
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 opacity-0 transition-opacity duration-300'
    
    modal.innerHTML = `
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white modal-content transform scale-95 transition-transform duration-300">
        <div class="mt-3">
          <!-- Header -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              AI Attribution Details: ${data.field ? data.field.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Unknown Field'}
            </h3>
            <button type="button" class="text-gray-400 hover:text-gray-600 close-modal">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
          
          <!-- Content -->
          <div class="space-y-4">
            ${this.renderModalContent(data)}
          </div>
          
          <!-- Actions -->
          <div class="mt-6 flex items-center justify-end space-x-3">
            <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md close-modal">
              Close
            </button>
            <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md export-field-data">
              Export Data
            </button>
          </div>
        </div>
      </div>
    `
    
    // Add event listeners
    modal.querySelectorAll('.close-modal').forEach(btn => {
      btn.addEventListener('click', () => this.closeModal(modal))
    })
    
    modal.querySelector('.export-field-data').addEventListener('click', () => {
      this.exportFieldData(data.field)
      this.closeModal(modal)
    })
    
    // Close on backdrop click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.closeModal(modal)
      }
    })
    
    return modal
  }

  renderModalContent(data) {
    if (data.error) {
      return `<div class="text-red-600">${data.error}</div>`
    }
    
    return `
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="font-medium text-gray-900 mb-2">Generation Information</h4>
        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
          ${data.ai_generated ? `
            <div>
              <dt class="font-medium text-gray-700">AI Generated:</dt>
              <dd class="text-green-600">Yes</dd>
            </div>
            ${data.model ? `
              <div>
                <dt class="font-medium text-gray-700">Model:</dt>
                <dd class="text-gray-900">${data.model}</dd>
              </div>
            ` : ''}
            ${data.generated_at ? `
              <div>
                <dt class="font-medium text-gray-700">Generated:</dt>
                <dd class="text-gray-900">${new Date(data.generated_at).toLocaleString()}</dd>
              </div>
            ` : ''}
          ` : `
            <div>
              <dt class="font-medium text-gray-700">AI Generated:</dt>
              <dd class="text-gray-600">No</dd>
            </div>
          `}
        </dl>
      </div>
      
      ${data.display_info ? `
        <div class="bg-blue-50 rounded-lg p-4">
          <h4 class="font-medium text-blue-900 mb-2">Summary</h4>
          <p class="text-blue-800 text-sm">${data.display_info}</p>
        </div>
      ` : ''}
    `
  }

  closeModal(modal) {
    modal.classList.add('opacity-0')
    modal.querySelector('.modal-content').classList.remove('scale-100')
    modal.querySelector('.modal-content').classList.add('scale-95')
    
    setTimeout(() => {
      modal.remove()
    }, 300)
  }

  exportFieldData(field) {
    this.performExport('field', { field: field })
  }

  exportFullReport() {
    this.performExport('full')
  }

  performExport(type, options = {}) {
    const url = `/api/ai_attribution/${this.objectTypeValue.toLowerCase()}/${this.objectIdValue}/export`
    const params = new URLSearchParams({
      type: type,
      ...options
    })
    
    fetch(`${url}?${params}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      return response.blob()
    })
    .then(blob => {
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `ai_attribution_${type}_${this.objectTypeValue}_${this.objectIdValue}_${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      this.showNotification('Attribution data exported successfully', 'success')
    })
    .catch(error => {
      console.error('Export failed:', error)
      this.showNotification('Failed to export attribution data', 'error')
    })
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div')
    
    const typeStyles = {
      success: 'bg-green-100 text-green-800 border-green-200',
      error: 'bg-red-100 text-red-800 border-red-200',
      info: 'bg-blue-100 text-blue-800 border-blue-200'
    }
    
    notification.className = `fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 border text-sm transition-all duration-300 transform translate-x-full ${typeStyles[type] || typeStyles.info}`
    notification.textContent = message

    document.body.appendChild(notification)

    // Slide in animation
    setTimeout(() => {
      notification.classList.remove('translate-x-full')
    }, 10)

    // Remove after 4 seconds
    setTimeout(() => {
      notification.classList.add('translate-x-full')
      setTimeout(() => {
        notification.remove()
      }, 300)
    }, 4000)
  }
}
