import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "modelSelect", "modelInfo", "advancedOptions", "advancedToggle",
    "temperatureSelect", "maxTokensSelect", "costEstimate", 
    "modelComparison", "hiddenInput"
  ]

  connect() {
    this.models = window.aiModelSelectorData?.models || []
    this.updateModelInfo()
    this.updateCostEstimate()
  }

  modelChanged(event) {
    const selectedModelId = event.target.value
    this.hiddenInputTarget.value = selectedModelId
    
    this.updateModelInfo()
    this.updateCostEstimate()
    this.trackModelSelection(selectedModelId)
  }

  toggleAdvanced(event) {
    const isHidden = this.advancedOptionsTarget.classList.contains('hidden')
    
    if (isHidden) {
      this.advancedOptionsTarget.classList.remove('hidden')
      this.advancedToggleTarget.textContent = 'Hide Advanced'
    } else {
      this.advancedOptionsTarget.classList.add('hidden')
      this.advancedToggleTarget.textContent = 'Advanced Options'
    }
  }

  updateModelInfo() {
    const selectedModelId = this.modelSelectTarget.value
    const selectedModel = this.models.find(m => m.id === selectedModelId)
    
    if (!selectedModel) {
      this.modelInfoTarget.innerHTML = `
        <div class="text-center text-gray-500 text-sm">
          Select a model to see details
        </div>
      `
      return
    }

    // Update model information display
    this.modelInfoTarget.innerHTML = `
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h4 class="text-sm font-medium text-gray-900 mb-1">${selectedModel.name}</h4>
          <p class="text-xs text-gray-600 mb-2">${selectedModel.description}</p>
          
          <!-- Capabilities -->
          <div class="flex flex-wrap gap-1 mb-2">
            ${selectedModel.capabilities.map(cap => 
              `<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                ${this.humanize(cap)}
              </span>`
            ).join('')}
          </div>

          <!-- Strengths -->
          <div class="text-xs text-gray-500">
            <strong>Best for:</strong> ${selectedModel.strengths.join(", ")}
          </div>
        </div>

        <!-- Performance Badge -->
        <div class="ml-3 flex flex-col items-end">
          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${this.getPerformanceBadgeClass(selectedModel.performance_tier)}">
            ${this.humanize(selectedModel.performance_tier)}
          </span>
          <div class="text-xs text-gray-500 mt-1">
            ${this.formatNumber(selectedModel.context_window)} tokens
          </div>
        </div>
      </div>
    `
  }

  updateCostEstimate() {
    const selectedModelId = this.modelSelectTarget.value
    const selectedModel = this.models.find(m => m.id === selectedModelId)
    
    if (!selectedModel || !this.hasTarget('maxTokensSelect') || !this.hasTarget('costEstimate')) {
      return
    }

    const maxTokens = parseInt(this.maxTokensSelectTarget.value) || 2000
    const costPer1K = selectedModel.cost_per_1k_tokens
    const estimatedCost = (maxTokens / 1000) * costPer1K

    this.costEstimateTarget.textContent = `$${estimatedCost.toFixed(4)}`
  }

  temperatureChanged() {
    // Update any temperature-related UI if needed
    this.updateCostEstimate()
  }

  maxTokensChanged() {
    this.updateCostEstimate()
  }

  showComparison() {
    if (this.hasTarget('modelComparison')) {
      this.modelComparisonTarget.classList.toggle('hidden')
    }
  }

  // Utility methods
  humanize(str) {
    return str.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  formatNumber(num) {
    return new Intl.NumberFormat().format(num)
  }

  getPerformanceBadgeClass(tier) {
    switch (tier) {
      case 'premium':
        return 'bg-purple-100 text-purple-800'
      case 'standard':
        return 'bg-green-100 text-green-800'
      case 'basic':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }

  trackModelSelection(modelId) {
    // Track model selection for analytics
    if (window.gtag) {
      gtag('event', 'ai_model_selected', {
        model_id: modelId,
        task_type: window.aiModelSelectorData?.taskType || 'unknown',
        timestamp: new Date().toISOString()
      })
    }

    // Dispatch custom event for other components to listen to
    this.dispatch('modelSelected', {
      detail: {
        modelId: modelId,
        model: this.models.find(m => m.id === modelId)
      }
    })
  }

  // Public API for other controllers
  getSelectedModel() {
    const selectedModelId = this.modelSelectTarget.value
    return this.models.find(m => m.id === selectedModelId)
  }

  getSelectedSettings() {
    return {
      modelId: this.modelSelectTarget.value,
      temperature: this.hasTarget('temperatureSelect') ? parseFloat(this.temperatureSelectTarget.value) : 0.7,
      maxTokens: this.hasTarget('maxTokensSelect') ? parseInt(this.maxTokensSelectTarget.value) : 2000
    }
  }

  setModel(modelId) {
    this.modelSelectTarget.value = modelId
    this.hiddenInputTarget.value = modelId
    this.updateModelInfo()
    this.updateCostEstimate()
  }

  // Handle model availability changes
  updateModelAvailability(availableModels) {
    this.models = availableModels
    
    // Update the select options
    const currentValue = this.modelSelectTarget.value
    const isCurrentStillAvailable = availableModels.some(m => m.id === currentValue)
    
    if (!isCurrentStillAvailable && availableModels.length > 0) {
      // Switch to first available model
      this.setModel(availableModels[0].id)
    }
    
    this.updateModelInfo()
    this.updateCostEstimate()
  }

  // Error handling
  handleModelError(error) {
    console.error('AI Model Selector Error:', error)
    
    // Show error in the model info area
    this.modelInfoTarget.innerHTML = `
      <div class="text-center text-red-500 text-sm">
        <svg class="w-4 h-4 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        Error loading model information
      </div>
    `
  }

  // Keyboard shortcuts
  handleKeydown(event) {
    // Ctrl/Cmd + M to toggle advanced options
    if ((event.ctrlKey || event.metaKey) && event.key === 'm') {
      event.preventDefault()
      this.toggleAdvanced()
    }
  }
}
