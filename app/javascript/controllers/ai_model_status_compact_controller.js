import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["toggleButton", "setupModal"]

  connect() {
    this.showAll = window.aiModelDataCompact?.showAll || false
    this.maxModels = window.aiModelDataCompact?.maxModels || 4
    this.models = window.aiModelDataCompact?.models || []
    this.recommended = window.aiModelDataCompact?.recommended || []
  }

  refresh(event) {
    const button = event.currentTarget
    const icon = button.querySelector('svg')
    
    // Add spinning animation
    icon.classList.add('animate-spin')
    button.disabled = true

    // Make AJAX request to refresh model status
    fetch('/api/ai_models/status', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      return response.json()
    })
    .then(data => {
      this.updateModelStatus(data)
      this.showNotification('Model status updated', 'success')
    })
    .catch(error => {
      console.error('Failed to refresh model status:', error)
      this.showNotification('Failed to refresh status', 'error')
    })
    .finally(() => {
      // Remove spinning animation
      icon.classList.remove('animate-spin')
      button.disabled = false
    })
  }

  toggleShowAll(event) {
    this.showAll = !this.showAll
    
    // Update the component by reloading with new parameter
    const url = new URL(window.location)
    url.searchParams.set('show_all_models', this.showAll)
    
    // Use Turbo to reload just this component
    fetch(url.toString(), {
      headers: {
        'Accept': 'text/vnd.turbo-stream.html',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => response.text())
    .then(html => {
      // Update the component content
      this.element.outerHTML = html
    })
    .catch(error => {
      console.error('Failed to toggle view:', error)
      // Fallback: just update the button text
      if (this.hasToggleButtonTarget) {
        this.toggleButtonTarget.textContent = this.showAll ? 
          "Show Less" : 
          `Show All (${this.models.length})`
      }
    })
  }

  showSetupGuide() {
    if (this.hasSetupModalTarget) {
      this.setupModalTarget.classList.remove('hidden')
      document.body.style.overflow = 'hidden'
    }
  }

  closeSetupGuide() {
    if (this.hasSetupModalTarget) {
      this.setupModalTarget.classList.add('hidden')
      document.body.style.overflow = 'auto'
    }
  }

  updateModelStatus(data) {
    // Update the global model data
    if (window.aiModelDataCompact) {
      window.aiModelDataCompact.models = data.models
      window.aiModelDataCompact.totalAvailable = data.totals.available
      window.aiModelDataCompact.lastUpdated = data.lastUpdated
    }

    // Update status indicators in the UI
    this.updateStatusIndicators(data.models)
    this.updateAvailabilityBadge(data.totals.available)
  }

  updateStatusIndicators(models) {
    models.forEach(model => {
      // Find model elements and update their status
      const modelElements = this.element.querySelectorAll(`[data-model-id="${model.id}"]`)
      
      modelElements.forEach(element => {
        const statusDot = element.querySelector('.w-2.h-2')
        const statusBadge = element.querySelector('.inline-flex.items-center.px-2')
        
        if (statusDot) {
          // Update status dot color
          statusDot.className = `w-2 h-2 rounded-full ${this.getStatusDotColor(model.status)}`
        }
        
        if (statusBadge) {
          // Update status badge
          statusBadge.className = `inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${this.getStatusBadgeClasses(model.status)}`
          statusBadge.textContent = this.getStatusText(model.status)
        }
      })
    })
  }

  updateAvailabilityBadge(availableCount) {
    const badge = this.element.querySelector('.inline-flex.items-center.px-2.py-0\\.5.rounded-full')
    if (badge) {
      badge.textContent = `${availableCount} Available`
      badge.className = `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
        availableCount > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`
    }
  }

  showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div')
    
    const typeStyles = {
      success: 'bg-green-100 text-green-800 border-green-200',
      error: 'bg-red-100 text-red-800 border-red-200',
      info: 'bg-blue-100 text-blue-800 border-blue-200'
    }
    
    notification.className = `fixed top-4 right-4 px-3 py-2 rounded-lg shadow-lg z-50 border text-sm transition-all duration-300 transform translate-x-full ${typeStyles[type] || typeStyles.info}`
    notification.textContent = message

    document.body.appendChild(notification)

    // Slide in animation
    setTimeout(() => {
      notification.classList.remove('translate-x-full')
    }, 10)

    // Remove after 3 seconds
    setTimeout(() => {
      notification.classList.add('translate-x-full')
      setTimeout(() => {
        notification.remove()
      }, 300)
    }, 3000)
  }

  // Utility methods
  getStatusDotColor(status) {
    switch (status) {
      case 'available':
        return 'bg-green-400'
      case 'error':
        return 'bg-red-400'
      case 'not_configured':
        return 'bg-gray-400'
      case 'placeholder':
        return 'bg-yellow-400'
      default:
        return 'bg-gray-300'
    }
  }

  getStatusBadgeClasses(status) {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      case 'not_configured':
        return 'bg-gray-100 text-gray-800'
      case 'placeholder':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  getStatusText(status) {
    switch (status) {
      case 'available':
        return 'Available'
      case 'error':
        return 'Error'
      case 'not_configured':
        return 'Setup'
      case 'placeholder':
        return 'Config'
      default:
        return 'Unknown'
    }
  }

  // Handle clicks outside modal to close it
  handleOutsideClick(event) {
    if (this.hasSetupModalTarget && 
        this.setupModalTarget.contains(event.target) && 
        event.target === this.setupModalTarget) {
      this.closeSetupGuide()
    }
  }

  // Keyboard accessibility
  handleKeydown(event) {
    if (event.key === 'Escape' && this.hasSetupModalTarget && !this.setupModalTarget.classList.contains('hidden')) {
      this.closeSetupGuide()
    }
  }
}
