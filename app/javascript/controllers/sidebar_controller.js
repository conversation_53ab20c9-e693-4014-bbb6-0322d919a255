// Sidebar Controller for AI Marketing Hub
// Handles sidebar toggle, responsive behavior, and layout management

import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["sidebar", "overlay", "toggle"]
  static classes = ["open", "closed"]

  connect() {
    this.setupEventListeners()
    this.handleResize()
  }

  disconnect() {
    this.removeEventListeners()
  }

  // Toggle sidebar visibility
  toggle(event) {
    if (event) {
      event.preventDefault()
      event.stopPropagation()
    }

    const sidebar = this.sidebarTarget
    const isOpen = sidebar.classList.contains('open')

    if (isOpen) {
      this.close()
    } else {
      this.open()
    }
  }

  // Open sidebar
  open() {
    const sidebar = this.sidebarTarget
    const overlay = this.hasOverlayTarget ? this.overlayTarget : null

    // Add open class to sidebar
    sidebar.classList.add('open')
    sidebar.classList.remove('closed')

    // Show overlay on mobile
    if (overlay && window.innerWidth < 1024) {
      overlay.classList.remove('hidden')
    }

    // Prevent body scroll on mobile
    if (window.innerWidth < 1024) {
      document.body.style.overflow = 'hidden'
    }

    // Update aria attributes
    sidebar.setAttribute('aria-hidden', 'false')
    
    // Focus management
    this.focusFirstItem()

    // Dispatch custom event
    this.dispatch('opened', { detail: { sidebar: sidebar } })
  }

  // Close sidebar
  close() {
    const sidebar = this.sidebarTarget
    const overlay = this.hasOverlayTarget ? this.overlayTarget : null

    // Remove open class from sidebar
    sidebar.classList.remove('open')
    sidebar.classList.add('closed')

    // Hide overlay
    if (overlay) {
      overlay.classList.add('hidden')
    }

    // Restore body scroll
    document.body.style.overflow = ''

    // Update aria attributes
    sidebar.setAttribute('aria-hidden', 'true')

    // Dispatch custom event
    this.dispatch('closed', { detail: { sidebar: sidebar } })
  }

  // Handle overlay click
  overlayClick(event) {
    event.preventDefault()
    this.close()
  }

  // Setup event listeners
  setupEventListeners() {
    // Handle window resize
    this.boundHandleResize = this.handleResize.bind(this)
    window.addEventListener('resize', this.boundHandleResize)

    // Handle escape key
    this.boundHandleEscape = this.handleEscape.bind(this)
    document.addEventListener('keydown', this.boundHandleEscape)

    // Listen for navbar toggle events
    this.boundHandleNavbarToggle = this.handleNavbarToggle.bind(this)
    document.addEventListener('navbar:toggleSidebar', this.boundHandleNavbarToggle)
  }

  // Remove event listeners
  removeEventListeners() {
    if (this.boundHandleResize) {
      window.removeEventListener('resize', this.boundHandleResize)
    }
    if (this.boundHandleEscape) {
      document.removeEventListener('keydown', this.boundHandleEscape)
    }
    if (this.boundHandleNavbarToggle) {
      document.removeEventListener('navbar:toggleSidebar', this.boundHandleNavbarToggle)
    }
  }

  // Handle window resize
  handleResize() {
    const sidebar = this.sidebarTarget
    
    // Auto-close sidebar on mobile when resizing to desktop
    if (window.innerWidth >= 1024) {
      this.close()
      document.body.style.overflow = ''
    }
    
    // Ensure proper positioning
    this.updateSidebarPosition()
  }

  // Handle escape key
  handleEscape(event) {
    if (event.key === 'Escape') {
      const sidebar = this.sidebarTarget
      if (sidebar.classList.contains('open') && window.innerWidth < 1024) {
        this.close()
      }
    }
  }

  // Handle navbar toggle events
  handleNavbarToggle(event) {
    this.toggle()
  }

  // Focus first interactive element in sidebar
  focusFirstItem() {
    const sidebar = this.sidebarTarget
    const firstFocusable = sidebar.querySelector('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])')
    
    if (firstFocusable) {
      firstFocusable.focus()
    }
  }

  // Update sidebar position based on screen size
  updateSidebarPosition() {
    const sidebar = this.sidebarTarget
    
    // Ensure sidebar has correct classes for current screen size
    if (window.innerWidth >= 1024) {
      // Desktop: sidebar should be visible
      sidebar.style.transform = ''
    } else {
      // Mobile: sidebar should be hidden unless open
      if (!sidebar.classList.contains('open')) {
        sidebar.style.transform = 'translateX(-100%)'
      }
    }
  }

  // Get current sidebar state
  get isOpen() {
    return this.sidebarTarget.classList.contains('open')
  }

  // Get current screen size category
  get isMobile() {
    return window.innerWidth < 1024
  }

  get isDesktop() {
    return window.innerWidth >= 1024
  }
}
