// Modern Dropdown Controller for AI Marketing Hub
// Handles all dropdown functionality with smooth animations

import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["button", "menu", "arrow"]
  static values = { open: Boolean }

  connect() {
    this.openValue = false
    this.closeDropdown()
    
    // Bind event handlers
    this.boundCloseOnClickOutside = this.closeOnClickOutside.bind(this)
    this.boundCloseOnEscape = this.closeOnEscape.bind(this)
  }

  disconnect() {
    this.removeEventListeners()
  }

  toggle(event) {
    event.preventDefault()
    event.stopPropagation()
    
    if (this.openValue) {
      this.close()
    } else {
      this.open()
    }
  }

  open() {
    // Close other dropdowns first
    this.closeOtherDropdowns()
    
    // Show the dropdown
    this.menuTarget.classList.remove('opacity-0', 'invisible', 'scale-95')
    this.menuTarget.classList.add('opacity-100', 'visible', 'scale-100')
    
    // Rotate arrow if present
    if (this.hasArrowTarget) {
      this.arrowTarget.style.transform = 'rotate(180deg)'
    }
    
    // Update state
    this.openValue = true
    this.buttonTarget.setAttribute('aria-expanded', 'true')
    
    // Add event listeners
    this.addEventListeners()
    
    // Focus management
    this.menuTarget.focus()
  }

  close() {
    // Hide the dropdown
    this.menuTarget.classList.add('opacity-0', 'invisible', 'scale-95')
    this.menuTarget.classList.remove('opacity-100', 'visible', 'scale-100')
    
    // Reset arrow if present
    if (this.hasArrowTarget) {
      this.arrowTarget.style.transform = 'rotate(0deg)'
    }
    
    // Update state
    this.openValue = false
    this.buttonTarget.setAttribute('aria-expanded', 'false')
    
    // Remove event listeners
    this.removeEventListeners()
  }

  closeDropdown() {
    this.close()
  }

  // Event handlers
  closeOnClickOutside(event) {
    if (!this.element.contains(event.target)) {
      this.close()
    }
  }

  closeOnEscape(event) {
    if (event.key === 'Escape') {
      this.close()
      this.buttonTarget.focus()
    }
    
    // Handle arrow key navigation
    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
      event.preventDefault()
      this.navigateMenu(event.key === 'ArrowDown' ? 1 : -1)
    }
  }

  // Helper methods
  addEventListeners() {
    document.addEventListener('click', this.boundCloseOnClickOutside)
    document.addEventListener('keydown', this.boundCloseOnEscape)
  }

  removeEventListeners() {
    document.removeEventListener('click', this.boundCloseOnClickOutside)
    document.removeEventListener('keydown', this.boundCloseOnEscape)
  }

  closeOtherDropdowns() {
    // Find all dropdown controllers and close them
    const allDropdowns = document.querySelectorAll('[data-controller*="dropdown"]')
    
    allDropdowns.forEach(dropdown => {
      if (dropdown !== this.element) {
        const controller = this.application.getControllerForElementAndIdentifier(dropdown, 'dropdown')
        if (controller && controller.openValue) {
          controller.close()
        }
      }
    })
  }

  navigateMenu(direction) {
    const menuItems = this.menuTarget.querySelectorAll('a, button')
    const currentIndex = Array.from(menuItems).findIndex(item => 
      item === document.activeElement
    )
    
    let nextIndex = currentIndex + direction
    
    // Wrap around navigation
    if (nextIndex < 0) {
      nextIndex = menuItems.length - 1
    } else if (nextIndex >= menuItems.length) {
      nextIndex = 0
    }
    
    if (menuItems[nextIndex]) {
      menuItems[nextIndex].focus()
    }
  }

  // Handle menu item selection
  selectItem(event) {
    const selectedText = event.target.textContent.trim()
    
    // Update button text if it's a select-style dropdown
    const buttonText = this.buttonTarget.querySelector('span')
    if (buttonText && !buttonText.classList.contains('keep-text')) {
      buttonText.textContent = selectedText
    }
    
    // Close dropdown
    this.close()
    
    // Dispatch custom event for handling selection
    this.dispatch('itemSelected', {
      detail: {
        value: event.target.dataset.value || selectedText,
        text: selectedText,
        element: event.target
      }
    })
  }

  // Animation values changed
  openValueChanged() {
    if (this.openValue) {
      this.element.classList.add('dropdown-open')
    } else {
      this.element.classList.remove('dropdown-open')
    }
  }
}
