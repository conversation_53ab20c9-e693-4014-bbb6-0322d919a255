import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["providerTab", "providerContent"]

  connect() {
    this.refreshInterval = null
    this.setupAutoRefresh()
  }

  disconnect() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval)
    }
  }

  switchProvider(event) {
    const targetProvider = event.currentTarget.dataset.provider
    
    // Update tab states
    this.providerTabTargets.forEach(tab => {
      const isActive = tab.dataset.provider === targetProvider
      
      if (isActive) {
        tab.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300')
        tab.classList.add('border-blue-500', 'text-blue-600')
      } else {
        tab.classList.remove('border-blue-500', 'text-blue-600')
        tab.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300')
      }
    })

    // Update content visibility
    this.providerContentTargets.forEach(content => {
      const isActive = content.dataset.provider === targetProvider
      
      if (isActive) {
        content.classList.remove('hidden')
      } else {
        content.classList.add('hidden')
      }
    })

    // Track provider selection
    this.trackProviderSelection(targetProvider)
  }

  refresh(event) {
    const button = event.currentTarget
    const icon = button.querySelector('svg')
    
    // Add spinning animation
    icon.classList.add('animate-spin')
    button.disabled = true

    // Make AJAX request to refresh model status
    fetch('/api/ai_models/status', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    })
    .then(response => response.json())
    .then(data => {
      this.updateModelStatus(data)
      this.showRefreshSuccess()
    })
    .catch(error => {
      console.error('Failed to refresh model status:', error)
      this.showRefreshError()
    })
    .finally(() => {
      // Remove spinning animation
      icon.classList.remove('animate-spin')
      button.disabled = false
    })
  }

  updateModelStatus(data) {
    // Update the global model data
    if (window.aiModelData) {
      window.aiModelData.models = data.models
      window.aiModelData.providerStats = data.providerStats
      window.aiModelData.lastUpdated = data.lastUpdated
    }

    // Update status indicators in the UI
    data.models.forEach(model => {
      this.updateModelStatusIndicator(model)
    })

    // Update provider availability counts
    Object.entries(data.providerStats).forEach(([provider, stats]) => {
      this.updateProviderStats(provider, stats)
    })

    // Update overall availability count
    const totalAvailable = data.models.filter(m => m.status === 'available').length
    const totalModels = data.models.length
    this.updateOverallStats(totalAvailable, totalModels)
  }

  updateModelStatusIndicator(model) {
    const modelElements = document.querySelectorAll(`[data-model-id="${model.id}"]`)
    
    modelElements.forEach(element => {
      const statusIndicator = element.querySelector('.status-indicator')
      const statusBadge = element.querySelector('.status-badge')
      const responseTime = element.querySelector('.response-time')

      if (statusIndicator) {
        // Update status dot color
        statusIndicator.className = `w-3 h-3 rounded-full ${this.getStatusColor(model.status)}`
        statusIndicator.title = this.getStatusTitle(model)
      }

      if (statusBadge) {
        // Update status badge
        statusBadge.className = `inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${this.getStatusBadgeClasses(model.status)}`
        statusBadge.textContent = this.getStatusText(model.status)
      }

      if (responseTime && model.response_time > 0) {
        responseTime.textContent = `Response: ${model.response_time}ms`
      }
    })
  }

  updateProviderStats(provider, stats) {
    const providerTab = document.querySelector(`[data-provider="${provider}"]`)
    if (providerTab) {
      const countBadge = providerTab.querySelector('.inline-flex')
      if (countBadge) {
        countBadge.textContent = stats.available.toString()
        countBadge.className = `ml-1 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${
          stats.available > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`
      }
    }
  }

  updateOverallStats(available, total) {
    const overallBadge = document.querySelector('.inline-flex.items-center.px-2.py-0\\.5.rounded-full')
    if (overallBadge) {
      overallBadge.textContent = `${available}/${total} Available`
      overallBadge.className = `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
        available > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`
    }
  }

  setupAutoRefresh() {
    // Auto-refresh every 5 minutes
    this.refreshInterval = setInterval(() => {
      this.refresh({ currentTarget: document.querySelector('[data-action*="refresh"]') })
    }, 5 * 60 * 1000)
  }

  showRefreshSuccess() {
    this.showNotification('Model status updated successfully', 'success')
  }

  showRefreshError() {
    this.showNotification('Failed to refresh model status', 'error')
  }

  showNotification(message, type) {
    // Create a temporary notification
    const notification = document.createElement('div')
    notification.className = `fixed top-4 right-4 px-4 py-2 rounded-md shadow-lg z-50 ${
      type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'
    }`
    notification.textContent = message

    document.body.appendChild(notification)

    // Remove after 3 seconds
    setTimeout(() => {
      notification.remove()
    }, 3000)
  }

  trackProviderSelection(provider) {
    // Track provider selection for analytics
    if (window.gtag) {
      gtag('event', 'ai_provider_selected', {
        provider: provider,
        timestamp: new Date().toISOString()
      })
    }
  }

  getStatusColor(status) {
    switch (status) {
      case 'available':
        return 'bg-green-400'
      case 'error':
        return 'bg-red-400'
      case 'not_configured':
        return 'bg-gray-400'
      case 'placeholder':
        return 'bg-yellow-400'
      default:
        return 'bg-gray-300'
    }
  }

  getStatusBadgeClasses(status) {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      case 'not_configured':
        return 'bg-gray-100 text-gray-800'
      case 'placeholder':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  getStatusText(status) {
    switch (status) {
      case 'available':
        return 'Available'
      case 'error':
        return 'Error'
      case 'not_configured':
        return 'Not Configured'
      case 'placeholder':
        return 'Setup Required'
      default:
        return 'Unknown'
    }
  }

  getStatusTitle(model) {
    switch (model.status) {
      case 'available':
        return 'Available'
      case 'error':
        return `Error: ${model.error_message || 'Unknown error'}`
      case 'not_configured':
        return 'Not configured'
      case 'placeholder':
        return 'Placeholder API key detected'
      default:
        return 'Unknown status'
    }
  }
}
