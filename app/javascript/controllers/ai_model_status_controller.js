import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "refreshButton", "viewToggle", "modelView", "setupModal",
    "providerGroupedView", "groupingSelect"
  ]
  static values = {
    refreshInterval: { type: Number, default: 300000 } // 5 minutes
  }

  connect() {
    this.refreshInterval = null
    this.currentView = 'recommended'
    this.currentGrouping = 'provider'
    this.setupAutoRefresh()
    this.initializeView()
  }

  disconnect() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval)
    }
  }

  initializeView() {
    // Set initial view state
    this.updateViewToggleButtons()
    this.showView(this.currentView)
  }

  toggleView(event) {
    const targetView = event.currentTarget.dataset.view
    this.currentView = targetView

    this.updateViewToggleButtons()
    this.showView(targetView)
    this.trackViewChange(targetView)
  }

  updateViewToggleButtons() {
    this.viewToggleTargets.forEach(button => {
      const isActive = button.dataset.view === this.currentView

      if (isActive) {
        button.classList.remove('text-gray-600', 'hover:text-gray-900')
        button.classList.add('bg-white', 'text-gray-900', 'shadow-sm')
      } else {
        button.classList.remove('bg-white', 'text-gray-900', 'shadow-sm')
        button.classList.add('text-gray-600', 'hover:text-gray-900')
      }
    })
  }

  showView(viewName) {
    this.modelViewTargets.forEach(view => {
      const isActive = view.dataset.view === viewName

      if (isActive) {
        view.classList.remove('hidden')
        // Add fade-in animation
        view.style.opacity = '0'
        setTimeout(() => {
          view.style.opacity = '1'
          view.style.transition = 'opacity 0.3s ease-in-out'
        }, 10)
      } else {
        view.classList.add('hidden')
      }
    })
  }

  changeGrouping(event) {
    this.currentGrouping = event.target.value
    this.updateGroupedView()
    this.trackGroupingChange(this.currentGrouping)
  }

  updateGroupedView() {
    // This would reorganize the models based on the selected grouping
    // For now, we'll just track the change
    console.log(`Grouping changed to: ${this.currentGrouping}`)
  }

  refresh(event) {
    const button = this.refreshButtonTarget
    const icon = button.querySelector('svg')
    const buttonText = button.querySelector('span')

    // Add loading state
    icon.classList.add('animate-spin')
    button.disabled = true
    if (buttonText) {
      buttonText.textContent = 'Refreshing...'
    }

    // Make AJAX request to refresh model status
    fetch('/api/ai_models/status', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      return response.json()
    })
    .then(data => {
      this.updateModelStatus(data)
      this.showNotification('Model status updated successfully', 'success')
    })
    .catch(error => {
      console.error('Failed to refresh model status:', error)
      this.showNotification('Failed to refresh model status. Please try again.', 'error')
    })
    .finally(() => {
      // Remove loading state
      icon.classList.remove('animate-spin')
      button.disabled = false
      if (buttonText) {
        buttonText.textContent = 'Refresh'
      }
    })
  }

  selectModel(event) {
    const modelId = event.currentTarget.dataset.modelId

    // Dispatch custom event for other components
    this.dispatch('modelSelected', {
      detail: { modelId: modelId }
    })

    this.showNotification(`Selected ${modelId} model`, 'info')
    this.trackModelSelection(modelId)
  }

  configureModel(event) {
    const modelId = event.currentTarget.dataset.modelId
    const provider = event.currentTarget.dataset.provider

    this.showSetupGuide()
    this.trackConfigurationAttempt(modelId, provider)
  }

  showSetupGuide() {
    if (this.hasSetupModalTarget) {
      this.setupModalTarget.classList.remove('hidden')
      document.body.style.overflow = 'hidden'
    }
  }

  closeSetupGuide() {
    if (this.hasSetupModalTarget) {
      this.setupModalTarget.classList.add('hidden')
      document.body.style.overflow = 'auto'
    }
  }

  updateModelStatus(data) {
    // Update the global model data
    if (window.aiModelData) {
      window.aiModelData.models = data.models
      window.aiModelData.providerStats = data.providerStats
      window.aiModelData.lastUpdated = data.lastUpdated
    }

    // Update status indicators in the UI
    data.models.forEach(model => {
      this.updateModelStatusIndicator(model)
    })

    // Update provider availability counts
    Object.entries(data.providerStats).forEach(([provider, stats]) => {
      this.updateProviderStats(provider, stats)
    })

    // Update overall availability count
    const totalAvailable = data.models.filter(m => m.status === 'available').length
    const totalModels = data.models.length
    this.updateOverallStats(totalAvailable, totalModels)
  }

  updateModelStatusIndicator(model) {
    const modelElements = document.querySelectorAll(`[data-model-id="${model.id}"]`)
    
    modelElements.forEach(element => {
      const statusIndicator = element.querySelector('.status-indicator')
      const statusBadge = element.querySelector('.status-badge')
      const responseTime = element.querySelector('.response-time')

      if (statusIndicator) {
        // Update status dot color
        statusIndicator.className = `w-3 h-3 rounded-full ${this.getStatusColor(model.status)}`
        statusIndicator.title = this.getStatusTitle(model)
      }

      if (statusBadge) {
        // Update status badge
        statusBadge.className = `inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${this.getStatusBadgeClasses(model.status)}`
        statusBadge.textContent = this.getStatusText(model.status)
      }

      if (responseTime && model.response_time > 0) {
        responseTime.textContent = `Response: ${model.response_time}ms`
      }
    })
  }

  updateProviderStats(provider, stats) {
    const providerTab = document.querySelector(`[data-provider="${provider}"]`)
    if (providerTab) {
      const countBadge = providerTab.querySelector('.inline-flex')
      if (countBadge) {
        countBadge.textContent = stats.available.toString()
        countBadge.className = `ml-1 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${
          stats.available > 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`
      }
    }
  }

  updateOverallStats(available, total) {
    const overallBadge = document.querySelector('.inline-flex.items-center.px-2.py-0\\.5.rounded-full')
    if (overallBadge) {
      overallBadge.textContent = `${available}/${total} Available`
      overallBadge.className = `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
        available > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`
    }
  }

  setupAutoRefresh() {
    if (this.refreshIntervalValue > 0) {
      this.refreshInterval = setInterval(() => {
        // Only auto-refresh if the page is visible
        if (!document.hidden) {
          this.refresh({ currentTarget: this.refreshButtonTarget })
        }
      }, this.refreshIntervalValue)
    }
  }

  // Visibility change handler to pause/resume auto-refresh
  handleVisibilityChange() {
    if (document.hidden) {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
        this.refreshInterval = null
      }
    } else {
      this.setupAutoRefresh()
    }
  }

  showNotification(message, type = 'info') {
    // Create enhanced notification with better styling
    const notification = document.createElement('div')

    const typeStyles = {
      success: 'bg-green-100 text-green-800 border-green-200',
      error: 'bg-red-100 text-red-800 border-red-200',
      warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      info: 'bg-blue-100 text-blue-800 border-blue-200'
    }

    const icons = {
      success: '✓',
      error: '✕',
      warning: '⚠',
      info: 'ℹ'
    }

    notification.className = `fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 border transition-all duration-300 transform translate-x-full ${typeStyles[type] || typeStyles.info}`
    notification.innerHTML = `
      <div class="flex items-center space-x-2">
        <span class="font-medium">${icons[type] || icons.info}</span>
        <span>${message}</span>
      </div>
    `

    document.body.appendChild(notification)

    // Slide in animation
    setTimeout(() => {
      notification.classList.remove('translate-x-full')
    }, 10)

    // Remove after 4 seconds with slide out animation
    setTimeout(() => {
      notification.classList.add('translate-x-full')
      setTimeout(() => {
        notification.remove()
      }, 300)
    }, 4000)
  }

  // Analytics tracking methods
  trackViewChange(view) {
    if (window.gtag) {
      gtag('event', 'ai_model_view_changed', {
        view: view,
        timestamp: new Date().toISOString()
      })
    }
  }

  trackGroupingChange(grouping) {
    if (window.gtag) {
      gtag('event', 'ai_model_grouping_changed', {
        grouping: grouping,
        timestamp: new Date().toISOString()
      })
    }
  }

  trackModelSelection(modelId) {
    if (window.gtag) {
      gtag('event', 'ai_model_selected', {
        model_id: modelId,
        view: this.currentView,
        timestamp: new Date().toISOString()
      })
    }
  }

  trackConfigurationAttempt(modelId, provider) {
    if (window.gtag) {
      gtag('event', 'ai_model_configuration_attempted', {
        model_id: modelId,
        provider: provider,
        timestamp: new Date().toISOString()
      })
    }
  }

  getStatusColor(status) {
    switch (status) {
      case 'available':
        return 'bg-green-400'
      case 'error':
        return 'bg-red-400'
      case 'not_configured':
        return 'bg-gray-400'
      case 'placeholder':
        return 'bg-yellow-400'
      default:
        return 'bg-gray-300'
    }
  }

  getStatusBadgeClasses(status) {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      case 'not_configured':
        return 'bg-gray-100 text-gray-800'
      case 'placeholder':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  getStatusText(status) {
    switch (status) {
      case 'available':
        return 'Available'
      case 'error':
        return 'Error'
      case 'not_configured':
        return 'Not Configured'
      case 'placeholder':
        return 'Setup Required'
      default:
        return 'Unknown'
    }
  }

  getStatusTitle(model) {
    switch (model.status) {
      case 'available':
        return 'Available'
      case 'error':
        return `Error: ${model.error_message || 'Unknown error'}`
      case 'not_configured':
        return 'Not configured'
      case 'placeholder':
        return 'Placeholder API key detected'
      default:
        return 'Unknown status'
    }
  }
}
