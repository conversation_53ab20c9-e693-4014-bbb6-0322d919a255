import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["selectionIndicator"]

  connect() {
    this.modelId = this.element.dataset.modelId
    this.modelStatus = this.element.dataset.modelStatus
    this.isSelected = this.element.classList.contains('selected-model')
    
    this.setupHoverEffects()
    this.updateCardState()
  }

  selectModel(event) {
    // Prevent selection if model is not available
    if (this.modelStatus !== 'available') {
      this.showUnavailableMessage()
      return
    }

    // Toggle selection state
    this.isSelected = !this.isSelected
    this.updateCardState()
    
    // Dispatch selection event to parent controller
    this.dispatch('modelSelected', {
      detail: {
        modelId: this.modelId,
        selected: this.isSelected,
        element: this.element
      }
    })

    // Update other cards in the same container
    this.updateOtherCards()
    
    // Show feedback
    this.showSelectionFeedback()
  }

  updateCardState() {
    if (this.isSelected) {
      this.element.classList.add('selected-model')
      this.element.classList.remove('border-gray-200')
      this.element.classList.add('border-blue-500', 'ring-2', 'ring-blue-200')
      
      if (this.hasSelectionIndicatorTarget) {
        this.selectionIndicatorTarget.classList.remove('hidden')
      }
    } else {
      this.element.classList.remove('selected-model')
      this.element.classList.remove('border-blue-500', 'ring-2', 'ring-blue-200')
      this.element.classList.add('border-gray-200')
      
      if (this.hasSelectionIndicatorTarget) {
        this.selectionIndicatorTarget.classList.add('hidden')
      }
    }
  }

  updateOtherCards() {
    // If this is a single-selection context, deselect other cards
    const container = this.element.closest('[data-selection-mode="single"]')
    if (container && this.isSelected) {
      const otherCards = container.querySelectorAll('.ai-model-card:not([data-model-id="' + this.modelId + '"])')
      otherCards.forEach(card => {
        const controller = this.application.getControllerForElementAndIdentifier(card, 'ai-model-card')
        if (controller) {
          controller.deselect()
        }
      })
    }
  }

  deselect() {
    this.isSelected = false
    this.updateCardState()
  }

  setupHoverEffects() {
    if (this.modelStatus === 'available') {
      this.element.addEventListener('mouseenter', this.handleMouseEnter.bind(this))
      this.element.addEventListener('mouseleave', this.handleMouseLeave.bind(this))
    }
  }

  handleMouseEnter() {
    if (!this.isSelected && this.modelStatus === 'available') {
      this.element.classList.add('shadow-md', 'scale-105')
      this.element.style.transform = 'translateY(-2px)'
      this.element.style.transition = 'all 0.2s ease-in-out'
    }
  }

  handleMouseLeave() {
    if (!this.isSelected) {
      this.element.classList.remove('shadow-md', 'scale-105')
      this.element.style.transform = 'translateY(0)'
    }
  }

  showUnavailableMessage() {
    const message = this.getUnavailableMessage()
    this.showTooltip(message, 'warning')
  }

  getUnavailableMessage() {
    switch (this.modelStatus) {
      case 'not_configured':
        return 'This model requires API key configuration'
      case 'placeholder':
        return 'Please replace the placeholder API key'
      case 'error':
        return 'This model has a configuration error'
      default:
        return 'This model is currently unavailable'
    }
  }

  showSelectionFeedback() {
    const message = this.isSelected ? 
      `Selected ${this.getModelName()} for content generation` :
      `Deselected ${this.getModelName()}`
    
    this.showTooltip(message, this.isSelected ? 'success' : 'info')
  }

  getModelName() {
    const nameElement = this.element.querySelector('h4')
    return nameElement ? nameElement.textContent.trim() : this.modelId
  }

  showTooltip(message, type = 'info') {
    // Create temporary tooltip
    const tooltip = document.createElement('div')
    const rect = this.element.getBoundingClientRect()
    
    const typeStyles = {
      success: 'bg-green-100 text-green-800 border-green-200',
      warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      error: 'bg-red-100 text-red-800 border-red-200',
      info: 'bg-blue-100 text-blue-800 border-blue-200'
    }
    
    tooltip.className = `fixed z-50 px-3 py-2 text-sm rounded-lg border shadow-lg pointer-events-none transition-opacity duration-200 ${typeStyles[type] || typeStyles.info}`
    tooltip.textContent = message
    tooltip.style.left = `${rect.left + rect.width / 2}px`
    tooltip.style.top = `${rect.top - 40}px`
    tooltip.style.transform = 'translateX(-50%)'
    tooltip.style.opacity = '0'
    
    document.body.appendChild(tooltip)
    
    // Fade in
    setTimeout(() => {
      tooltip.style.opacity = '1'
    }, 10)
    
    // Remove after 2 seconds
    setTimeout(() => {
      tooltip.style.opacity = '0'
      setTimeout(() => {
        tooltip.remove()
      }, 200)
    }, 2000)
  }

  // Public API methods
  getModelData() {
    return {
      id: this.modelId,
      status: this.modelStatus,
      selected: this.isSelected,
      element: this.element
    }
  }

  setSelected(selected) {
    this.isSelected = selected
    this.updateCardState()
  }

  // Animation methods
  pulseCard() {
    this.element.classList.add('animate-pulse')
    setTimeout(() => {
      this.element.classList.remove('animate-pulse')
    }, 1000)
  }

  highlightCard(duration = 2000) {
    const originalClasses = this.element.className
    this.element.classList.add('ring-4', 'ring-yellow-300', 'ring-opacity-50')
    
    setTimeout(() => {
      this.element.classList.remove('ring-4', 'ring-yellow-300', 'ring-opacity-50')
    }, duration)
  }

  // Error handling
  handleError(error) {
    console.error('AI Model Card Error:', error)
    this.showTooltip('An error occurred with this model', 'error')
  }

  // Keyboard accessibility
  handleKeydown(event) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      this.selectModel(event)
    }
  }

  // Focus management
  focus() {
    this.element.focus()
  }

  blur() {
    this.element.blur()
  }
}
