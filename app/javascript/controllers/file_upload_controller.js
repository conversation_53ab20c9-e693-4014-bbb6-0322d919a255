import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "tabBtn", "tabContent", "dropZone", "fileInput", 
    "previewArea", "progressContainer", "progressBar"
  ]

  connect() {
    this.setupDragAndDrop()
    this.maxFileSizes = {
      images: 10 * 1024 * 1024,    // 10MB
      documents: 25 * 1024 * 1024, // 25MB
      audio: 50 * 1024 * 1024,     // 50MB
      video: 100 * 1024 * 1024     // 100MB
    }
    
    this.allowedTypes = {
      images: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
      documents: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
      audio: ['audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/x-m4a'],
      video: ['video/mp4', 'video/quicktime', 'video/x-msvideo']
    }
  }

  switchTab(event) {
    const targetTab = event.currentTarget.dataset.tab
    
    // Update tab buttons
    this.tabBtnTargets.forEach(btn => {
      btn.classList.remove('active', 'border-blue-500', 'text-blue-600')
      btn.classList.add('border-transparent', 'text-gray-500')
    })
    
    event.currentTarget.classList.add('active', 'border-blue-500', 'text-blue-600')
    event.currentTarget.classList.remove('border-transparent', 'text-gray-500')
    
    // Update tab content
    this.tabContentTargets.forEach(content => {
      if (content.dataset.tab === targetTab) {
        content.classList.remove('hidden')
      } else {
        content.classList.add('hidden')
      }
    })
  }

  setupDragAndDrop() {
    this.dropZoneTargets.forEach(dropZone => {
      dropZone.addEventListener('dragover', this.handleDragOver.bind(this))
      dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this))
      dropZone.addEventListener('drop', this.handleDrop.bind(this))
    })
  }

  handleDragOver(event) {
    event.preventDefault()
    event.currentTarget.classList.add('border-blue-400', 'bg-blue-50')
  }

  handleDragLeave(event) {
    event.preventDefault()
    event.currentTarget.classList.remove('border-blue-400', 'bg-blue-50')
  }

  handleDrop(event) {
    event.preventDefault()
    const dropZone = event.currentTarget
    dropZone.classList.remove('border-blue-400', 'bg-blue-50')
    
    const fileType = dropZone.dataset.fileType
    const files = Array.from(event.dataTransfer.files)
    
    this.processFiles(files, fileType)
  }

  handleFileSelect(event) {
    const fileType = event.currentTarget.dataset.fileType
    const files = Array.from(event.currentTarget.files)
    
    this.processFiles(files, fileType)
  }

  processFiles(files, fileType) {
    const validFiles = []
    const errors = []

    files.forEach(file => {
      // Check file type
      if (!this.allowedTypes[fileType].includes(file.type)) {
        errors.push(`${file.name}: Invalid file type. Please upload ${this.getFileTypeDescription(fileType)}.`)
        return
      }

      // Check file size
      if (file.size > this.maxFileSizes[fileType]) {
        errors.push(`${file.name}: File too large. Maximum size is ${this.formatFileSize(this.maxFileSizes[fileType])}.`)
        return
      }

      validFiles.push(file)
    })

    // Show errors if any
    if (errors.length > 0) {
      this.showErrors(errors)
    }

    // Process valid files
    if (validFiles.length > 0) {
      this.uploadFiles(validFiles, fileType)
    }
  }

  uploadFiles(files, fileType) {
    this.showProgress()
    
    files.forEach((file, index) => {
      this.createFilePreview(file, fileType)
      
      // Update progress
      const progress = ((index + 1) / files.length) * 100
      this.updateProgress(progress)
    })

    // Hide progress after a delay
    setTimeout(() => {
      this.hideProgress()
    }, 1000)
  }

  createFilePreview(file, fileType) {
    const previewArea = this.previewAreaTargets.find(area => area.dataset.fileType === fileType)
    if (!previewArea) return

    const previewElement = document.createElement('div')
    
    if (fileType === 'images') {
      previewElement.className = 'relative group'
      previewElement.innerHTML = `
        <img src="${URL.createObjectURL(file)}" 
             class="w-full h-24 object-cover rounded-lg border border-gray-200" 
             alt="${file.name}">
        <button type="button" 
                class="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                data-action="click->file-upload#removePreview">
          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
        <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b-lg">
          ${this.truncateFilename(file.name, 15)}
        </div>
      `
    } else {
      previewElement.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200'
      previewElement.innerHTML = `
        <div class="flex items-center space-x-3">
          <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            ${this.getFileIcon(fileType)}
          </svg>
          <div>
            <p class="text-sm font-medium text-gray-900">${file.name}</p>
            <p class="text-xs text-gray-500">${this.formatFileSize(file.size)}</p>
          </div>
        </div>
        <button type="button" 
                class="text-red-500 hover:text-red-700"
                data-action="click->file-upload#removePreview">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
          </svg>
        </button>
      `
    }

    previewArea.appendChild(previewElement)
  }

  removePreview(event) {
    event.currentTarget.closest('.relative, .flex').remove()
  }

  removeFile(event) {
    const fileId = event.currentTarget.dataset.fileId
    const fileType = event.currentTarget.dataset.fileType
    
    // Here you would typically make an AJAX request to remove the file from the server
    // For now, just remove the preview element
    event.currentTarget.closest('.relative, .flex').remove()
    
    console.log(`Removing file ${fileId} of type ${fileType}`)
  }

  showProgress() {
    this.progressContainerTarget.classList.remove('hidden')
    this.progressBarTarget.style.width = '0%'
  }

  updateProgress(percentage) {
    this.progressBarTarget.style.width = `${percentage}%`
  }

  hideProgress() {
    this.progressContainerTarget.classList.add('hidden')
  }

  showErrors(errors) {
    const errorContainer = document.createElement('div')
    errorContainer.className = 'mt-4 bg-red-50 border border-red-200 rounded-md p-4'
    errorContainer.innerHTML = `
      <div class="flex">
        <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Upload Errors</h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              ${errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
          </div>
        </div>
      </div>
    `

    // Insert error container after the file upload section
    this.element.appendChild(errorContainer)

    // Remove error container after 5 seconds
    setTimeout(() => {
      errorContainer.remove()
    }, 5000)
  }

  getFileTypeDescription(fileType) {
    const descriptions = {
      images: 'JPEG, PNG, GIF, or WebP images',
      documents: 'PDF, DOC, DOCX, or TXT files',
      audio: 'MP3, WAV, or M4A audio files',
      video: 'MP4, MOV, or AVI video files'
    }
    return descriptions[fileType] || 'valid files'
  }

  getFileIcon(fileType) {
    const icons = {
      documents: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>',
      audio: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"/>',
      video: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>'
    }
    return icons[fileType] || icons.documents
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  truncateFilename(filename, maxLength) {
    if (filename.length <= maxLength) return filename
    const extension = filename.split('.').pop()
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'))
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4)
    return `${truncatedName}...${extension}`
  }
}
