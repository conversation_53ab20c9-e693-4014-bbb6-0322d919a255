import { Controller } from "@hotwired/stimulus"

// Enhanced Navbar Controller for AI Marketing Hub
// Handles mobile menu, dropdown interactions, and sidebar integration
export default class extends Controller {
  static targets = ["mobileMenu", "mobileButton", "mobileIcon", "mobileCloseIcon"]

  connect() {
    // Initialize navbar state
    this.mobileMenuOpen = false
    
    // Close mobile menu on escape key
    this.boundHandleEscape = this.handleEscape.bind(this)
    document.addEventListener("keydown", this.boundHandleEscape)
    
    // Close mobile menu when clicking outside
    this.boundHandleOutsideClick = this.handleOutsideClick.bind(this)
    document.addEventListener("click", this.boundHandleOutsideClick)
    
    // Close mobile menu on window resize (if becoming desktop size)
    this.boundHandleResize = this.handleResize.bind(this)
    window.addEventListener("resize", this.boundHandleResize)
  }

  disconnect() {
    // Clean up event listeners
    document.removeEventListener("keydown", this.boundHandleEscape)
    document.removeEventListener("click", this.boundHandleOutsideClick)
    window.removeEventListener("resize", this.boundHandleResize)
  }

  // Toggle mobile menu
  toggleMobile(event) {
    event.preventDefault()
    
    if (this.mobileMenuOpen) {
      this.closeMobileMenu()
    } else {
      this.openMobileMenu()
    }
  }

  // Open mobile menu
  openMobileMenu() {
    this.mobileMenuOpen = true
    this.mobileMenuTarget.classList.remove("hidden")
    this.mobileButtonTarget.setAttribute("aria-expanded", "true")
    this.mobileIconTarget.classList.add("hidden")
    this.mobileCloseIconTarget.classList.remove("hidden")
    
    // Prevent body scroll when mobile menu is open
    document.body.classList.add("overflow-hidden")
    
    // Focus management for accessibility
    this.focusTrap()
  }

  // Close mobile menu
  closeMobileMenu() {
    this.mobileMenuOpen = false
    this.mobileMenuTarget.classList.add("hidden")
    this.mobileButtonTarget.setAttribute("aria-expanded", "false")
    this.mobileIconTarget.classList.remove("hidden")
    this.mobileCloseIconTarget.classList.add("hidden")
    
    // Restore body scroll
    document.body.classList.remove("overflow-hidden")
    
    // Return focus to toggle button
    this.mobileButtonTarget.focus()
  }

  // Handle sidebar toggle for authenticated users
  toggleSidebar(event) {
    event.preventDefault()
    
    // Dispatch custom event that the sidebar can listen to
    const sidebarEvent = new CustomEvent("navbar:toggleSidebar", {
      bubbles: true,
      detail: { source: "navbar" }
    })
    document.dispatchEvent(sidebarEvent)
  }

  // Handle escape key press
  handleEscape(event) {
    if (event.key === "Escape" && this.mobileMenuOpen) {
      this.closeMobileMenu()
    }
  }

  // Handle clicks outside mobile menu
  handleOutsideClick(event) {
    if (!this.mobileMenuOpen) return
    
    // Check if click is outside the navbar
    if (!this.element.contains(event.target)) {
      this.closeMobileMenu()
    }
  }

  // Handle window resize
  handleResize() {
    // Close mobile menu if window becomes desktop size
    if (window.innerWidth >= 768 && this.mobileMenuOpen) { // md breakpoint
      this.closeMobileMenu()
    }
  }

  // Simple focus trap for mobile menu accessibility
  focusTrap() {
    const focusableElements = this.mobileMenuTarget.querySelectorAll(
      'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    )
    
    if (focusableElements.length > 0) {
      focusableElements[0].focus()
    }
  }
}