// Mobile Sidebar Controller for Dashboard
// Handles mobile sidebar toggle functionality

import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["sidebar", "overlay"]

  connect() {
    this.setupResponsiveHandling()
  }

  toggle() {
    const sidebar = this.sidebarTarget
    const overlay = this.hasOverlayTarget ? this.overlayTarget : null
    
    sidebar.classList.toggle('open')
    
    if (overlay) {
      overlay.classList.toggle('hidden')
    }
    
    // Prevent body scroll when sidebar is open
    if (sidebar.classList.contains('open')) {
      document.body.style.overflow = 'hidden'
      document.body.classList.add('mobile-sidebar-open')
    } else {
      document.body.style.overflow = ''
      document.body.classList.remove('mobile-sidebar-open')
    }
    
    // Manage focus and ARIA for accessibility
    const toggleButton = document.querySelector('[data-action*="mobile-sidebar#toggle"]')
    if (sidebar.classList.contains('open')) {
      sidebar.focus()
      if (toggleButton) {
        toggleButton.setAttribute('aria-expanded', 'true')
      }
    } else {
      if (toggleButton) {
        toggleButton.setAttribute('aria-expanded', 'false')
      }
    }
  }

  close() {
    const sidebar = this.sidebarTarget
    const overlay = this.hasOverlayTarget ? this.overlayTarget : null
    
    sidebar.classList.remove('open')
    
    if (overlay) {
      overlay.classList.add('hidden')
    }
    
    document.body.style.overflow = ''
    document.body.classList.remove('mobile-sidebar-open')
    
    // Return focus to the toggle button and update ARIA
    const toggleButton = document.querySelector('[data-action*="mobile-sidebar#toggle"]')
    if (toggleButton) {
      toggleButton.focus()
      toggleButton.setAttribute('aria-expanded', 'false')
    }
  }

  setupResponsiveHandling() {
    // Close sidebar when clicking overlay
    if (this.hasOverlayTarget) {
      this.overlayTarget.addEventListener('click', () => {
        this.close()
      })
    }
    
    // Handle window resize
    window.addEventListener('resize', () => {
      if (window.innerWidth >= 1024) { // lg breakpoint
        this.close()
      }
    })
    
    // Handle escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        this.close()
      }
    })
  }
}
