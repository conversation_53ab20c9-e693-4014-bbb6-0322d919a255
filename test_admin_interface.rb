#!/usr/bin/env ruby
require_relative 'config/environment'

# Test the admin AI provider configurations interface
puts "Testing AI Provider Configurations Admin Interface..."

# Create a test tenant and user
tenant = Tenant.first || Tenant.create!(
  name: "Test Company",
  subdomain: "test-company",
  primary_domain: "test-company.example.com"
)

user = User.where(email: "<EMAIL>").first_or_create! do |u|
  u.password = "password123"
  u.first_name = "Admin"
  u.last_name = "User"
  u.role = "admin"
  u.tenant = tenant
end

puts "Test tenant: #{tenant.name}"
puts "Test user: #{user.email}"

# Test creating an AI provider configuration
config = AiProviderConfiguration.create!(
  tenant: tenant,
  provider_name: "openai",
  ai_model_name: "gpt-4",
  api_key: "test-key-123",
  base_url: "https://api.openai.com/v1",
  is_active: true,
  default_for_task_type: "content_generation"
)

puts "\nCreated AI Provider Configuration:"
puts "- Provider: #{config.provider_name}"
puts "- Model: #{config.ai_model_name}"
puts "- Active: #{config.is_active?}"
puts "- Default for: #{config.default_for_task_type}"

# Test the controller methods that were fixed
puts "\nTesting controller queries that were previously failing..."

# Test the main index query
configs = AiProviderConfiguration.joins(:tenant)
                                .where(tenant: tenant)
                                .order(:provider_name, :ai_model_name)

puts "Index query successful - found #{configs.count} configurations"

# Test the summary query
summary = AiProviderConfiguration.joins(:tenant)
                                .where(tenant: tenant)
                                .group(:provider_name, :is_active)
                                .count

puts "Summary query successful - results: #{summary}"

# Test accessing the fixed column names
config.reload
puts "\nTesting column access:"
puts "- provider_name: #{config.provider_name}"
puts "- ai_model_name: #{config.ai_model_name}"
puts "- Provider display: #{config.provider_name.titleize}"
puts "- First two letters: #{config.provider_name.first(2).upcase}"

puts "\n✅ All tests passed! The admin interface should now work correctly."
