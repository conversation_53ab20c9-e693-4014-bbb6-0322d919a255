FactoryBot.define do
  factory :ai_provider_configuration do
    association :tenant
    provider_name { "openai" }
    ai_model_name { "gpt-4o" }
    cost_per_token { 0.000015 }
    is_active { true }
    # Don't set default_for_task_type by default to avoid conflicts
    capabilities { ["text", "function_calling"] }
    configuration do
      {
        "max_tokens" => 4096,
        "temperature" => 0.7,
        "timeout_seconds" => 180,
        "retry_attempts" => 3,
        "retry_delay" => 1.0
      }
    end

    trait :openai do
      provider_name { "openai" }
      ai_model_name { "gpt-4o" }
      cost_per_token { 0.000015 }
      capabilities { ["text", "function_calling"] }
    end

    trait :openai_mini do
      provider_name { "openai" }
      ai_model_name { "gpt-4o-mini" }
      cost_per_token { 0.0000015 }
      capabilities { ["text", "function_calling"] }
    end

    trait :anthropic do
      provider_name { "anthropic" }
      ai_model_name { "claude-3-sonnet" }
      cost_per_token { 0.000015 }
      capabilities { ["text", "vision", "function_calling"] }
    end

    trait :gemini do
      provider_name { "gemini" }
      ai_model_name { "gemini-1.5-pro" }
      cost_per_token { 0.0000035 }
      capabilities { ["text", "vision", "function_calling"] }
    end

    trait :deepseek do
      provider_name { "deepseek" }
      ai_model_name { "deepseek-chat" }
      cost_per_token { 0.00000014 }
      capabilities { ["text", "function_calling"] }
    end

    trait :inactive do
      is_active { false }
    end

    trait :creative_content do
      default_for_task_type { "creative_content" }
      configuration do
        {
          "max_tokens" => 2048,
          "temperature" => 0.8,
          "timeout_seconds" => 180,
          "retry_attempts" => 3,
          "retry_delay" => 1.0
        }
      end
    end

    trait :data_analysis do
      default_for_task_type { "data_analysis" }
      configuration do
        {
          "max_tokens" => 4096,
          "temperature" => 0.1,
          "timeout_seconds" => 300,
          "retry_attempts" => 5,
          "retry_delay" => 1.0
        }
      end
    end
  end
end
