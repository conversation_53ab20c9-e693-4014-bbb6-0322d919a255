# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CampaignsController, type: :controller do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:controller) { described_class.new }

  before do
    ActsAsTenant.current_tenant = tenant
    allow(controller).to receive(:current_user).and_return(user)
    allow(controller).to receive(:current_tenant).and_return(tenant)
  end

  describe '#campaign_contract_params' do
    it 'converts budget_in_dollars to budget_cents' do
      params = ActionController::Parameters.new(
        campaign: {
          name: 'Test Campaign',
          campaign_type: 'email',
          target_audience: 'Young professionals',
          budget_in_dollars: 500.00
        }
      )

      allow(controller).to receive(:params).and_return(params)

      result = controller.send(:campaign_contract_params)

      expect(result[:budget_cents]).to eq(50000)
      expect(result[:budget_in_dollars]).to be_nil
    end

    it 'handles missing budget_in_dollars' do
      params = ActionController::Parameters.new(
        campaign: {
          name: 'Test Campaign',
          campaign_type: 'email',
          target_audience: 'Young professionals'
        }
      )

      allow(controller).to receive(:params).and_return(params)

      result = controller.send(:campaign_contract_params)

      expect(result[:budget_cents]).to be_nil
    end
  end

  describe '#format_contract_errors' do
    it 'formats validation errors correctly' do
      # Create a real validation error by running an invalid contract
      contract = CampaignContract.new
      result = contract.call({
        name: '',
        campaign_type: 'invalid_type',
        target_audience: 'ab',
        budget_cents: -100
      })

      formatted_errors = controller.send(:format_contract_errors, result.errors)

      expect(formatted_errors).to include('Name: must be filled')
      expect(formatted_errors).to include('Campaign type: must be one of: email, social, seo, multi_channel')
      expect(formatted_errors).to include('Target audience: size cannot be less than 3')
      expect(formatted_errors).to include('Budget cents: must be greater than or equal to 0')
    end
  end

  describe 'validation integration' do
    let(:valid_params) do
      {
        name: 'Test Campaign',
        campaign_type: 'email',
        target_audience: 'Young professionals aged 25-35',
        budget_cents: 50000,
        start_date: Date.current + 1.day,
        end_date: Date.current + 30.days,
        description: 'A test campaign for validation'
      }
    end

    let(:invalid_params) do
      {
        name: '', # Invalid: empty name
        campaign_type: 'invalid_type', # Invalid: not in allowed types
        target_audience: 'ab', # Invalid: too short
        budget_cents: -100 # Invalid: negative budget
      }
    end

    it 'validates valid parameters successfully' do
      contract = CampaignContract.new
      result = contract.call(valid_params)

      expect(result).to be_success
      expect(result.errors).to be_empty
    end

    it 'catches validation errors for invalid parameters' do
      contract = CampaignContract.new
      result = contract.call(invalid_params)

      expect(result).to be_failure
      expect(result.errors[:name]).to include('must be filled')
      expect(result.errors[:campaign_type]).to include('must be one of: email, social, seo, multi_channel')
      expect(result.errors[:target_audience]).to include('size cannot be less than 3')
      expect(result.errors[:budget_cents]).to include('must be greater than or equal to 0')
    end

    it 'validates date constraints' do
      invalid_date_params = valid_params.merge(
        start_date: Date.current + 30.days,
        end_date: Date.current + 1.day # End date before start date
      )

      contract = CampaignContract.new
      result = contract.call(invalid_date_params)

      expect(result).to be_failure
      expect(result.errors[:end_date]).to include('must be after start date')
    end

    it 'validates past start date' do
      invalid_date_params = valid_params.merge(
        start_date: Date.current - 1.day # Past date
      )

      contract = CampaignContract.new
      result = contract.call(invalid_date_params)

      expect(result).to be_failure
      expect(result.errors[:start_date]).to include('cannot be in the past')
    end
  end

  describe 'PATCH #activate' do
    let(:campaign) { create(:campaign, tenant: tenant, status: 'draft') }

    before do
      sign_in user
    end

    it 'activates a campaign that can be activated' do
      allow(campaign).to receive(:can_be_activated?).and_return(true)
      allow(Campaign).to receive(:find).and_return(campaign)

      patch :activate, params: { id: campaign.id }

      expect(campaign).to have_received(:update).with(status: "active")
      expect(response).to redirect_to(campaign)
      expect(flash[:notice]).to eq("Campaign activated successfully.")
    end

    it 'does not activate a campaign that cannot be activated' do
      allow(campaign).to receive(:can_be_activated?).and_return(false)
      allow(Campaign).to receive(:find).and_return(campaign)

      patch :activate, params: { id: campaign.id }

      expect(response).to redirect_to(campaign)
      expect(flash[:alert]).to eq("Campaign cannot be activated in its current state.")
    end
  end
end
