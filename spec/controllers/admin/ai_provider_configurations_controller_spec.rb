require 'rails_helper'

RSpec.describe Admin::AiProviderConfigurationsController, type: :controller do
  let(:tenant) { create(:tenant) }
  let(:admin_user) { create(:user, :admin, tenant: tenant) }
  let(:regular_user) { create(:user, tenant: tenant) }
  let(:ai_provider_configuration) { create(:ai_provider_configuration, tenant: tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe 'authentication and authorization' do
    context 'when user is not authenticated' do
      it 'redirects to login' do
        get :index
        expect(response).to redirect_to(new_user_session_path)
      end
    end

    context 'when user is authenticated but not admin' do
      before { sign_in regular_user }

      it 'redirects to dashboard with access denied message' do
        get :index
        expect(response).to redirect_to(dashboard_path)
        expect(flash[:alert]).to eq('Access denied.')
      end
    end

    context 'when user is admin' do
      before { sign_in admin_user }

      it 'allows access to index' do
        get :index
        expect(response).to have_http_status(:success)
      end
    end
  end

  describe 'GET #index' do
    before { sign_in admin_user }

    let!(:openai_config) { create(:ai_provider_configuration, tenant: tenant, provider_name: "openai", ai_model_name: "gpt-4o-mini") }
    let!(:anthropic_config) { create(:ai_provider_configuration, tenant: tenant, provider_name: "anthropic", ai_model_name: "claude-3-haiku") }
    let!(:inactive_config) { create(:ai_provider_configuration, tenant: tenant, ai_model_name: "gpt-4-inactive", is_active: false) }

    it 'assigns all tenant AI provider configurations' do
      get :index
      expect(assigns(:ai_provider_configurations)).to match_array([openai_config, anthropic_config, inactive_config])
    end

    it 'includes providers summary' do
      get :index
      expect(assigns(:providers_summary)).to be_present
    end

    it 'orders configurations by provider and model name' do
      get :index
      configurations = assigns(:ai_provider_configurations)
      expect(configurations.first.provider_name).to eq('anthropic')
      expect(configurations.last.provider_name).to eq('openai')
    end
  end

  describe 'GET #show' do
    before { sign_in admin_user }

    it 'assigns the requested AI provider configuration' do
      get :show, params: { id: ai_provider_configuration.id }
      expect(assigns(:ai_provider_configuration)).to eq(ai_provider_configuration)
    end

    it 'assigns usage stats and recent requests' do
      get :show, params: { id: ai_provider_configuration.id }
      expect(assigns(:usage_stats)).to be_present
      expect(assigns(:recent_requests)).to be_present
    end

    it 'returns http success' do
      get :show, params: { id: ai_provider_configuration.id }
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #new' do
    before { sign_in admin_user }

    it 'assigns a new AI provider configuration' do
      get :new
      expect(assigns(:ai_provider_configuration)).to be_a_new(AiProviderConfiguration)
      expect(assigns(:ai_provider_configuration).tenant).to eq(tenant)
    end

    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    before { sign_in admin_user }

    let(:valid_attributes) do
      {
        provider_name: 'openai',
        ai_model_name: 'gpt-4o',
        cost_per_token: 0.000015,
        is_active: true,
        configuration: { max_tokens: 4096, temperature: 0.7 }
      }
    end

    let(:invalid_attributes) do
      {
        provider_name: '',
        ai_model_name: '',
        cost_per_token: nil
      }
    end

    context 'with valid parameters' do
      it 'creates a new AI provider configuration' do
        expect {
          post :create, params: { ai_provider_configuration: valid_attributes }
        }.to change(AiProviderConfiguration, :count).by(1)
      end

      it 'assigns the configuration to the current tenant' do
        post :create, params: { ai_provider_configuration: valid_attributes }
        expect(assigns(:ai_provider_configuration).tenant).to eq(tenant)
      end

      it 'redirects to the created configuration' do
        post :create, params: { ai_provider_configuration: valid_attributes }
        expect(response).to redirect_to(admin_ai_provider_configuration_path(assigns(:ai_provider_configuration)))
      end

      it 'sets a success notice' do
        post :create, params: { ai_provider_configuration: valid_attributes }
        expect(flash[:notice]).to eq('AI provider configuration was successfully created.')
      end
    end

    context 'with invalid parameters' do
      it 'does not create a new AI provider configuration' do
        expect {
          post :create, params: { ai_provider_configuration: invalid_attributes }
        }.not_to change(AiProviderConfiguration, :count)
      end

      it 'renders the new template with unprocessable entity status' do
        post :create, params: { ai_provider_configuration: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response).to render_template(:new)
      end
    end
  end

  describe 'GET #edit' do
    before { sign_in admin_user }

    it 'assigns the requested AI provider configuration' do
      get :edit, params: { id: ai_provider_configuration.id }
      expect(assigns(:ai_provider_configuration)).to eq(ai_provider_configuration)
    end

    it 'returns http success' do
      get :edit, params: { id: ai_provider_configuration.id }
      expect(response).to have_http_status(:success)
    end
  end

  describe 'PATCH #update' do
    before { sign_in admin_user }

    let(:new_attributes) do
      {
        ai_model_name: 'gpt-4o-mini',
        cost_per_token: 0.0000015
      }
    end

    let(:invalid_attributes) do
      {
        provider_name: '',
        cost_per_token: nil
      }
    end

    context 'with valid parameters' do
      it 'updates the requested AI provider configuration' do
        patch :update, params: { id: ai_provider_configuration.id, ai_provider_configuration: new_attributes }
        ai_provider_configuration.reload
        expect(ai_provider_configuration.ai_model_name).to eq('gpt-4o-mini')
        expect(ai_provider_configuration.cost_per_token).to eq(0.0000015)
      end

      it 'redirects to the configuration' do
        patch :update, params: { id: ai_provider_configuration.id, ai_provider_configuration: new_attributes }
        expect(response).to redirect_to(admin_ai_provider_configuration_path(ai_provider_configuration))
      end

      it 'sets a success notice' do
        patch :update, params: { id: ai_provider_configuration.id, ai_provider_configuration: new_attributes }
        expect(flash[:notice]).to eq('AI provider configuration was successfully updated.')
      end
    end

    context 'with invalid parameters' do
      it 'does not update the configuration' do
        original_name = ai_provider_configuration.ai_model_name
        patch :update, params: { id: ai_provider_configuration.id, ai_provider_configuration: invalid_attributes }
        ai_provider_configuration.reload
        expect(ai_provider_configuration.ai_model_name).to eq(original_name)
      end

      it 'renders the edit template with unprocessable entity status' do
        patch :update, params: { id: ai_provider_configuration.id, ai_provider_configuration: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response).to render_template(:edit)
      end
    end
  end

  describe 'DELETE #destroy' do
    before { sign_in admin_user }

    let!(:configuration_to_delete) { create(:ai_provider_configuration, tenant: tenant) }

    it 'destroys the requested AI provider configuration' do
      expect {
        delete :destroy, params: { id: configuration_to_delete.id }
      }.to change(AiProviderConfiguration, :count).by(-1)
    end

    it 'redirects to the configurations list' do
      delete :destroy, params: { id: configuration_to_delete.id }
      expect(response).to redirect_to(admin_ai_provider_configurations_path)
    end

    it 'sets a success notice with provider name' do
      provider_name = configuration_to_delete.provider_name
      delete :destroy, params: { id: configuration_to_delete.id }
      expect(flash[:notice]).to eq("#{provider_name} configuration was successfully deleted.")
    end
  end

  describe 'POST #test_connection' do
    before { sign_in admin_user }

    context 'when connection test succeeds' do
      before do
        allow_any_instance_of(AiProviderConfiguration).to receive(:test_connection).and_return({
          success: true,
          response_time: 250
        })
      end

      it 'sets a success notice' do
        post :test_connection, params: { id: ai_provider_configuration.id }
        expect(flash[:notice]).to eq('Connection successful! Response time: 250ms')
      end

      it 'redirects to the configuration' do
        post :test_connection, params: { id: ai_provider_configuration.id }
        expect(response).to redirect_to(admin_ai_provider_configuration_path(ai_provider_configuration))
      end
    end

    context 'when connection test fails' do
      before do
        allow_any_instance_of(AiProviderConfiguration).to receive(:test_connection).and_return({
          success: false,
          error: 'Invalid API key'
        })
      end

      it 'sets an error alert' do
        post :test_connection, params: { id: ai_provider_configuration.id }
        expect(flash[:alert]).to eq('Connection failed: Invalid API key')
      end

      it 'redirects to the configuration' do
        post :test_connection, params: { id: ai_provider_configuration.id }
        expect(response).to redirect_to(admin_ai_provider_configuration_path(ai_provider_configuration))
      end
    end
  end

  describe 'PATCH #toggle_active' do
    before { sign_in admin_user }

    context 'when configuration is active' do
      let(:active_config) { create(:ai_provider_configuration, tenant: tenant, is_active: true) }

      it 'deactivates the configuration' do
        patch :toggle_active, params: { id: active_config.id }
        active_config.reload
        expect(active_config.is_active).to be false
      end

      it 'sets a deactivated notice' do
        patch :toggle_active, params: { id: active_config.id }
        expect(flash[:notice]).to eq("#{active_config.provider_name} configuration deactivated.")
      end
    end

    context 'when configuration is inactive' do
      let(:inactive_config) { create(:ai_provider_configuration, :inactive, tenant: tenant) }

      it 'activates the configuration' do
        patch :toggle_active, params: { id: inactive_config.id }
        inactive_config.reload
        expect(inactive_config.is_active).to be true
      end

      it 'sets an activated notice' do
        patch :toggle_active, params: { id: inactive_config.id }
        expect(flash[:notice]).to eq("#{inactive_config.provider_name} configuration activated.")
      end
    end

    it 'redirects to the configurations list' do
      patch :toggle_active, params: { id: ai_provider_configuration.id }
      expect(response).to redirect_to(admin_ai_provider_configurations_path)
    end
  end

  describe 'tenant scoping' do
    before { sign_in admin_user }

    let(:other_tenant) { create(:tenant) }
    let(:other_tenant_config) { create(:ai_provider_configuration, tenant: other_tenant) }

    it 'does not show configurations from other tenants in index' do
      ai_provider_configuration # create config for current tenant
      other_tenant_config # create config for other tenant
      
      get :index
      configurations = assigns(:ai_provider_configurations)
      expect(configurations).to include(ai_provider_configuration)
      expect(configurations).not_to include(other_tenant_config)
    end

    it 'raises error when trying to access other tenant configuration' do
      expect {
        get :show, params: { id: other_tenant_config.id }
      }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'raises error when trying to edit other tenant configuration' do
      expect {
        get :edit, params: { id: other_tenant_config.id }
      }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'raises error when trying to update other tenant configuration' do
      expect {
        patch :update, params: { id: other_tenant_config.id, ai_provider_configuration: { ai_model_name: 'new-model' } }
      }.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'raises error when trying to delete other tenant configuration' do
      expect {
        delete :destroy, params: { id: other_tenant_config.id }
      }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end

  describe 'parameter filtering' do
    before { sign_in admin_user }

    let(:valid_params) do
      {
        provider_name: 'openai',
        ai_model_name: 'gpt-4o',
        cost_per_token: 0.000015,
        is_active: true,
        capabilities: ['text', 'function_calling']
      }
    end

    let(:params_with_forbidden_attributes) do
      valid_params.merge(
        tenant_id: create(:tenant).id,
        created_at: 1.day.ago,
        updated_at: 1.day.ago
      )
    end

    it 'filters out forbidden parameters' do
      post :create, params: { ai_provider_configuration: params_with_forbidden_attributes }
      created_config = assigns(:ai_provider_configuration)
      
      expect(created_config.tenant).to eq(tenant) # Should use current_tenant, not passed tenant_id
      expect(created_config.provider_name).to eq('openai') # Allowed param should work
    end

    it 'allows permitted parameters' do
      post :create, params: { ai_provider_configuration: valid_params }
      created_config = assigns(:ai_provider_configuration)
      
      expect(created_config.provider_name).to eq('openai')
      expect(created_config.ai_model_name).to eq('gpt-4o')
      expect(created_config.cost_per_token).to eq(0.000015)
      expect(created_config.is_active).to be true
    end
  end
end
