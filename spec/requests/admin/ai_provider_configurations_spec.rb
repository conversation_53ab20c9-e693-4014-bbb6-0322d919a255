require 'rails_helper'

RSpec.describe "Admin::AiProviderConfigurations", type: :request do
  let(:tenant) { Tenant.first || create(:tenant) }
  let(:admin_user) { create(:user, :admin, tenant: tenant) }
  
  before do
    ActsAsTenant.current_tenant = tenant
    sign_in admin_user
  end

  describe "GET /admin/ai_provider_configurations" do
    it "returns http success" do
      get "/admin/ai_provider_configurations"
      expect(response).to have_http_status(:success)
    end

    it "displays configurations" do
      config = create(:ai_provider_configuration, tenant: tenant, provider_name: "openai", ai_model_name: "gpt-4o")
      get "/admin/ai_provider_configurations"
      expect(response.body).to include("openai")
      expect(response.body).to include("gpt-4o")
    end
  end

  describe "GET /admin/ai_provider_configurations/new" do
    it "returns http success" do
      get "/admin/ai_provider_configurations/new"
      expect(response).to have_http_status(:success)
    end
  end

  describe "POST /admin/ai_provider_configurations" do
    it "creates a new configuration" do
      expect {
        post "/admin/ai_provider_configurations", params: {
          ai_provider_configuration: {
            provider_name: "openai",
            ai_model_name: "gpt-4o",
            cost_per_token: 0.000015,
            is_active: true
          }
        }
      }.to change(AiProviderConfiguration, :count).by(1)
      
      expect(response).to redirect_to("/admin/ai_provider_configurations/#{AiProviderConfiguration.last.id}")
    end
  end
end
