#!/usr/bin/env ruby

# Test script to verify the admin interface works after fixing column references
require_relative 'config/environment'

puts "Testing Admin AI Provider Configurations Interface..."
puts "=" * 60

# Create a test tenant if needed
tenant = Tenant.first || Tenant.create!(
  name: "Test Tenant",
  slug: "test-tenant",
  subscription_plan: "basic"
)

puts "Using tenant: #{tenant.name}"

# Create some test configurations
configurations = [
  {
    provider_name: "openai",
    ai_model_name: "gpt-4o-mini",
    cost_per_token: 0.00000015,
    is_active: true,
    default_for_task_type: "general",
    capabilities: ["text", "vision"],
    configuration: {
      "max_tokens" => 2048,
      "temperature" => 0.7,
      "priority" => 1
    }
  },
  {
    provider_name: "anthropic",
    ai_model_name: "claude-3-haiku-20240307",
    cost_per_token: 0.00000025,
    is_active: true,
    default_for_task_type: "creative_content",
    capabilities: ["text"],
    configuration: {
      "max_tokens" => 4096,
      "temperature" => 0.8,
      "priority" => 2
    }
  }
]

puts "\nCreating test configurations..."
configurations.each do |config_attrs|
  config = tenant.ai_provider_configurations.find_or_create_by(
    provider_name: config_attrs[:provider_name],
    ai_model_name: config_attrs[:ai_model_name]
  ) do |c|
    c.assign_attributes(config_attrs)
  end
  puts "✓ #{config.provider_name} - #{config.ai_model_name}"
end

puts "\nTesting controller methods..."

# Test the index query
puts "\n1. Testing index query..."
begin
  configs = tenant.ai_provider_configurations
    .includes(:tenant)
    .order(:provider_name, :ai_model_name)
  
  puts "✓ Found #{configs.count} configurations"
  
  configs.each do |config|
    puts "  - #{config.provider_name.titleize} #{config.ai_model_name}"
    puts "    Cost: $#{config.cost_per_token}/token"
    puts "    Max tokens: #{config.max_tokens}"
    puts "    Priority: #{config.priority}"
    puts "    Task types: #{config.task_types.join(', ')}"
    puts "    Capabilities: #{config.capabilities.join(', ')}"
    puts "    Active: #{config.is_active?}"
    puts ""
  end
  
rescue => e
  puts "✗ Error in index query: #{e.message}"
  puts e.backtrace.first(3)
end

# Test the summary query
puts "\n2. Testing summary query..."
begin
  summary = tenant.ai_provider_configurations
    .group(:provider_name, :is_active)
    .count
  
  puts "✓ Summary query works: #{summary}"
rescue => e
  puts "✗ Error in summary query: #{e.message}"
  puts e.backtrace.first(3)
end

# Test model methods
puts "\n3. Testing model methods..."
config = tenant.ai_provider_configurations.first
if config
  begin
    puts "Testing configuration: #{config.provider_name} - #{config.ai_model_name}"
    puts "✓ provider_name: #{config.provider_name}"
    puts "✓ ai_model_name: #{config.ai_model_name}"
    puts "✓ max_tokens: #{config.max_tokens}"
    puts "✓ priority: #{config.priority}"
    puts "✓ task_types: #{config.task_types}"
    puts "✓ capabilities: #{config.capabilities}"
    puts "✓ cost_per_token: #{config.cost_per_token}"
    puts "✓ is_active: #{config.is_active?}"
  rescue => e
    puts "✗ Error testing model methods: #{e.message}"
    puts e.backtrace.first(3)
  end
else
  puts "✗ No configurations found"
end

puts "\n" + "=" * 60
puts "Admin interface test completed!"
