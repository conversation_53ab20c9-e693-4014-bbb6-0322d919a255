#!/usr/bin/env ruby

# Test script for AI Attribution System
# Run with: ruby test_ai_attribution.rb

require_relative 'config/environment'

puts "🧪 Testing AI Attribution System"
puts "=" * 50

# Find or create a test tenant and user
tenant = Tenant.first || Tenant.create!(
  name: "Test Tenant",
  subdomain: "test-#{SecureRandom.hex(4)}"
)

user = tenant.users.first || tenant.users.create!(
  email: "<EMAIL>",
  password: "password123",
  password_confirmation: "password123",
  first_name: "Test",
  last_name: "User",
  role: :admin
)

# Create a test campaign
campaign = tenant.campaigns.create!(
  name: "AI Attribution Test Campaign #{SecureRandom.hex(4)}",
  campaign_type: "email",
  target_audience: "Test audience",
  budget_cents: 10000,
  status: "draft",
  created_by: user
)

puts "✅ Created test campaign: #{campaign.name}"

# Create an email campaign
email_campaign = campaign.create_email_campaign!(
  subject_line: "Test Subject Line",
  preview_text: "Test preview text",
  content: "This is test email content",
  from_name: "Test Sender",
  from_email: "<EMAIL>"
)

puts "✅ Created email campaign: #{email_campaign.id}"

# Test AI Attribution Service
attribution_service = AiAttributionService.new(user: user, generatable: email_campaign)

# Simulate AI generation result
ai_result = {
  status: "success",
  model_used: "gpt-4o",
  ai_model_name: "gpt-4o",
  generation_parameters: {
    temperature: 0.7,
    max_tokens: 1000,
    task_type: "email_generation"
  },
  token_count: 150,
  generation_cost: 0.0075,
  generation_time_ms: 2500,
  confidence_score: 0.92,
  source: "generation",
  # Add content for extraction
  subject_line: "AI Generated Subject Line",
  content: "AI Generated Email Content",
  campaign_data: {
    subject_line: "AI Generated Subject Line",
    content: "AI Generated Email Content",
    preview_text: "AI Generated Preview Text"
  }
}

puts "\n🤖 Testing AI attribution tracking..."

# Track AI generation for subject line
generation_event = attribution_service.track_generation(
  content_type: 'subject_line',
  result: ai_result,
  input_prompt: "Generate a compelling subject line for an email campaign"
)

puts "✅ Created AI generation event: #{generation_event.id}"
puts "   - Model: #{generation_event.model_display_name}"
puts "   - Cost: #{generation_event.cost_formatted}"
puts "   - Tokens: #{generation_event.token_count}"
puts "   - Confidence: #{generation_event.confidence_percentage}%"

# Test attribution info
attribution_info = attribution_service.attribution_info('subject_line')
puts "\n📊 Attribution info for subject line:"
puts "   - AI Generated: #{attribution_info[:ai_generated]}"
puts "   - Model: #{attribution_info[:model]}"
puts "   - Display Info: #{attribution_info[:display_info]}"

# Test visual indicators
visual_indicators = attribution_service.visual_indicators('subject_line')
puts "\n🎨 Visual indicators:"
puts "   - Badge Text: #{visual_indicators[:badge][:text]}"
puts "   - Badge Color: #{visual_indicators[:badge][:color]}"
puts "   - Model Name: #{visual_indicators[:model_info][:name]}"
puts "   - Tooltip: #{visual_indicators[:tooltip]}"

# Test email campaign attribution methods
puts "\n📧 Email campaign attribution methods:"
puts "   - Has AI content: #{email_campaign.ai_generated_content?}"
puts "   - Subject line AI generated: #{email_campaign.ai_generated_content?('subject_line')}"
puts "   - Content AI generated: #{email_campaign.ai_generated_content?('content')}"
puts "   - AI model for subject: #{email_campaign.ai_model_for_field('subject_line')}"

# Test attribution summary
summary = email_campaign.ai_attribution_summary
puts "\n📋 Attribution summary:"
puts "   - Overall AI generated: #{summary[:ai_generated]}"
puts "   - AI fields: #{summary[:fields].keys.join(', ')}"

# Test transparency report
transparency_report = email_campaign.ai_transparency_report
if transparency_report
  puts "\n📄 Transparency report:"
  puts "   - Campaign: #{transparency_report[:campaign_name]}"
  puts "   - AI fields: #{transparency_report[:ai_generated_fields].keys.join(', ')}"
  puts "   - Total cost: $#{transparency_report[:total_ai_cost]}"
  puts "   - Total tokens: #{transparency_report[:total_tokens]}"
end

# Test export functionality
puts "\n💾 Testing export functionality..."
export_data = attribution_service.export_attribution_data
if export_data
  puts "✅ Export data generated successfully"
  puts "   - Export version: #{export_data[:export_metadata][:export_version]}"
  puts "   - Exported by: #{export_data[:export_metadata][:exported_by]}"
else
  puts "❌ No export data available"
end

# Test usage statistics
puts "\n📈 Testing usage statistics..."
usage_stats = AiGenerationEvent.usage_stats_for_tenant(tenant)
puts "✅ Usage statistics:"
puts "   - Total generations: #{usage_stats[:total_generations]}"
puts "   - Total cost: $#{usage_stats[:total_cost]}"
puts "   - Total tokens: #{usage_stats[:total_tokens]}"
puts "   - By model: #{usage_stats[:by_model]}"
puts "   - Average confidence: #{usage_stats[:average_confidence] || 'N/A'}"

# Test bulk attribution check
puts "\n🔍 Testing bulk attribution check..."
objects = [email_campaign]
bulk_results = AiAttributionService.bulk_attribution_check(objects)
bulk_results.each do |result|
  puts "✅ Object #{result[:object].class.name}##{result[:object].id}:"
  puts "   - Has AI content: #{result[:has_ai_content]}"
  puts "   - AI fields: #{result[:ai_fields].join(', ')}"
end

puts "\n🎉 AI Attribution System test completed successfully!"
puts "=" * 50

# Cleanup (optional)
puts "\n🧹 Cleaning up test data..."
email_campaign.destroy
campaign.destroy
# user.destroy # Keep user for future tests
puts "✅ Cleanup completed"
