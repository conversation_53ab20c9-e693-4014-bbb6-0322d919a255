# frozen_string_literal: true

class AddAiAttributionToEmailCampaigns < ActiveRecord::Migration[8.0]
  def change
    add_column :email_campaigns, :ai_attribution, :jsonb, default: {}, null: false
    
    # Add index for AI attribution queries
    add_index :email_campaigns, :ai_attribution, using: :gin
    
    # Add comment to explain the structure
    change_column_comment :email_campaigns, :ai_attribution, 
      'Tracks AI generation attribution for different content fields: ' \
      '{ subject_line: { ai_generated: true, model: "gpt-4o", generated_at: "...", generation_event_id: 123 }, ' \
      'preview_text: { ai_generated: false }, content: { ai_generated: true, ... } }'
  end
end
