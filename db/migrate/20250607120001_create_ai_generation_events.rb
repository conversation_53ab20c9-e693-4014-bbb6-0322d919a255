# frozen_string_literal: true

class CreateAiGenerationEvents < ActiveRecord::Migration[8.0]
  def change
    create_table :ai_generation_events do |t|
      t.references :tenant, null: false, foreign_key: true, index: true
      t.references :user, null: false, foreign_key: true, index: true
      
      # Polymorphic association to the object that was generated for
      t.string :generatable_type, null: false
      t.bigint :generatable_id, null: false
      t.index [:generatable_type, :generatable_id], name: 'index_ai_generation_events_on_generatable'
      
      # Content and AI model information
      t.string :content_type, null: false # subject_line, preview_text, content, full_email
      t.string :ai_model_name, null: false
      t.string :ai_provider, null: false
      
      # Generation parameters and metadata
      t.jsonb :generation_parameters, null: false, default: {}
      t.text :input_prompt
      t.text :generated_content, null: false
      
      # Quality and performance metrics
      t.decimal :confidence_score, precision: 5, scale: 4
      t.integer :token_count
      t.decimal :generation_cost, precision: 10, scale: 6
      t.integer :generation_time_ms
      
      # Additional metadata
      t.jsonb :metadata, null: false, default: {}
      
      t.timestamps
    end
    
    # Additional indexes for common queries
    add_index :ai_generation_events, :content_type
    add_index :ai_generation_events, :ai_model_name
    add_index :ai_generation_events, :created_at
    
    # GIN indexes for JSONB columns
    add_index :ai_generation_events, :generation_parameters, using: :gin
    add_index :ai_generation_events, :metadata, using: :gin
  end
end
