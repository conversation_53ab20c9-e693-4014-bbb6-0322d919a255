Rails.application.routes.draw do
  # Devise routes for authentication with custom controllers
  devise_for :users, controllers: {
    sessions: "users/sessions"
  }

  # Root route - Landing page for non-authenticated users
  root "marketing#index"

  # Marketing pages
  get "features", to: "marketing#features"
  get "pricing", to: "marketing#pricing"
  get "about", to: "marketing#about"
  get "contact", to: "marketing#contact"

  # Authenticated dashboard
  get "dashboard", to: "dashboard#index"
  get "dashboard/real_time_campaign_monitoring", to: "dashboard#real_time_campaign_monitoring_dashboard"
  get "dashboard/real_time_campaign_monitoring_dashboard", to: "dashboard#real_time_campaign_monitoring_dashboard"

  # AI Agents
  resources :ai_agents, only: [ :index, :show ] do
    member do
      patch :activate
      patch :deactivate
      get :metrics
    end
  end

  # Vibe Analytics
  get "vibe_analytics", to: "vibe_analytics#index"
  get "vibe_analytics/emotional_trends", to: "vibe_analytics#emotional_trends"
  get "vibe_analytics/cultural_breakdown", to: "vibe_analytics#cultural_breakdown"
  get "vibe_analytics/authenticity_metrics", to: "vibe_analytics#authenticity_metrics"
  get "vibe_analytics/vibe_performance", to: "vibe_analytics#vibe_performance"

  # Marketing Therapist AI - Emotional Intelligence
  get "emotional_intelligence", to: "emotional_intelligence#index"
  get "emotional_intelligence/insights", to: "emotional_intelligence#insights"
  get "emotional_intelligence/real_time_monitor", to: "emotional_intelligence#real_time_monitor"
  post "emotional_intelligence/bulk_analyze", to: "emotional_intelligence#bulk_analyze"

  # Customer emotional profiles
  get "emotional_intelligence/customers/:customer_identifier", to: "emotional_intelligence#show", as: :emotional_intelligence_customer
  post "emotional_intelligence/customers/:customer_identifier/update_state", to: "emotional_intelligence#update_state", as: :emotional_intelligence_customer_update_state
  post "emotional_intelligence/customers/:customer_identifier/predict_response", to: "emotional_intelligence#predict_response", as: :emotional_intelligence_customer_predict_response

  # Campaign emotional analysis
  get "emotional_intelligence/campaigns/:campaign_id/emotional_analysis", to: "emotional_intelligence#campaign_emotional_analysis", as: :emotional_intelligence_campaign_emotional_analysis
  post "emotional_intelligence/campaigns/:campaign_id/apply_emotional_optimization", to: "emotional_intelligence#apply_emotional_optimization", as: :emotional_intelligence_campaign_apply_optimization

  # Audiences
  resources :audiences do
    member do
      get :analytics
      get :cultural_alignment
      get :engagement_metrics
    end
  end

  # Platform Configurations for Social Media OAuth
  resources :platform_configurations do
    member do
      post :refresh_token
      get :test_connection
    end

    collection do
      get "oauth_callback/:platform", action: :oauth_callback, as: :oauth_callback
    end
  end

  # RESTful campaign routes with nested agent workflows
  resources :campaigns do
    member do
      patch :activate
      patch :pause
      patch :complete
      get :analytics
    end

    # Email Content Management - nested under campaigns
    resource :email_content, controller: "email_content" do
      collection do
        post :generate_ai_content
        post :optimize_content
      end
      member do
        get :preview
      end
    end

    # Social Content Management - nested under campaigns
    resource :social_content, controller: "social_content" do
      member do
        get :preview
        post :schedule
        post :generate_ai_content
        post :optimize_content
        post :generate_hashtags
      end
    end

    # Social Scheduling - nested under campaigns
    resources :social_scheduling do
      member do
        post :duplicate_schedule
      end

      collection do
        post :bulk_schedule
        get :calendar_data
        get :optimal_times
        post :batch_reschedule
      end
    end

    # Nested agent workflows for AI automation
    resources :agent_workflows, except: [ :edit, :update, :destroy ] do
      member do
        patch :cancel
        patch :retry
      end

      collection do
        post :generate_content
        post :bulk_generate
        post :optimize_campaign
      end
    end
  end

  # API routes for real-time updates
  namespace :api do
    namespace :v1 do
      resources :campaigns, only: [ :index, :show ] do
        member do
          get :metrics
          get :progress
        end
      end

      resources :workflows, only: [ :show ] do
        member do
          patch :cancel
          post :retry
        end

        collection do
          get :status
          get :metrics
        end
      end
    end
  end

  # User Profile and Settings
  resource :profile, only: [ :show, :edit, :update ] do
    member do
      patch :update_avatar
      patch :update_preferences
    end
  end

  resource :account_settings, only: [ :show, :update ] do
    member do
      patch :update_password
      patch :update_notifications
      patch :update_security
      delete :delete_account
    end
  end

  # Help and Support
  resources :help, only: [ :index, :show ] do
    collection do
      get :contact
      post :contact, action: :create_contact
      get :faq
      get :documentation
    end
  end

  resources :support_tickets, only: [ :index, :show, :create, :update ] do
    member do
      patch :close
      patch :reopen
    end
  end

  # Admin routes
  namespace :admin do
    resources :ai_provider_configurations do
      member do
        post :test_connection
        patch :toggle_active
      end
    end
    resources :performance, only: [ :index ] do
      collection do
        get :queries
        get :caches
        get :reports
        post :optimize
      end
    end
  end

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check

  # PWA files
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
end
