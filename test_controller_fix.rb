#!/usr/bin/env ruby
require File.expand_path('../config/environment', __FILE__)

# Test the controller fix
tenant = Tenant.first || Tenant.create!(name: "Test Tenant", subdomain: "test")

puts "Testing AI Provider Configuration controller index logic..."

# Test the queries that are used in the controller
begin
  ai_provider_configurations = tenant.ai_provider_configurations.includes(:tenant)
                                     .order(:provider_name, :ai_model_name)
  puts "✓ Main query works - found #{ai_provider_configurations.count} configurations"
  
  providers_summary = tenant.ai_provider_configurations.group(:provider_name, :is_active).count
  puts "✓ Summary query works - found #{providers_summary.keys.count} provider/status combinations"
  
  puts "Summary details:"
  providers_summary.each do |(provider, is_active), count|
    status = is_active ? "active" : "inactive"
    puts "  - #{provider} (#{status}): #{count} configurations"
  end
  
rescue => e
  puts "✗ Error: #{e.message}"
  puts e.backtrace.first(5)
end
