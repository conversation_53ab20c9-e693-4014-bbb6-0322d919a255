#!/usr/bin/env ruby
require File.expand_path('../config/environment', __FILE__)

# Test the controller fix with actual data
tenant = Tenant.first || Tenant.create!(name: "Test Tenant", subdomain: "test")

# Create some test AI provider configurations if none exist
if tenant.ai_provider_configurations.empty?
  puts "Creating test AI provider configurations..."
  
  tenant.ai_provider_configurations.create!([
    {
      provider_name: "openai",
      ai_model_name: "gpt-4o",
      cost_per_token: 0.000015,
      is_active: true,
      capabilities: ["text", "function_calling"]
    },
    {
      provider_name: "openai", 
      ai_model_name: "gpt-4o-mini",
      cost_per_token: 0.0000015,
      is_active: true,
      capabilities: ["text", "function_calling"]
    },
    {
      provider_name: "anthropic",
      ai_model_name: "claude-3-sonnet",
      cost_per_token: 0.000015,
      is_active: false,
      capabilities: ["text", "vision", "function_calling"]
    }
  ])
  
  puts "Created 3 test configurations"
end

puts "Testing AI Provider Configuration controller index logic..."

# Test the queries that are used in the controller
begin
  ai_provider_configurations = tenant.ai_provider_configurations.includes(:tenant)
                                     .order(:provider_name, :ai_model_name)
  puts "✓ Main query works - found #{ai_provider_configurations.count} configurations"
  
  ai_provider_configurations.each do |config|
    puts "  - #{config.provider_name}/#{config.ai_model_name} (#{config.is_active? ? 'active' : 'inactive'})"
  end
  
  providers_summary = tenant.ai_provider_configurations.group(:provider_name, :is_active).count
  puts "✓ Summary query works - found #{providers_summary.keys.count} provider/status combinations"
  
  puts "Summary details:"
  providers_summary.each do |(provider, is_active), count|
    status = is_active ? "active" : "inactive"
    puts "  - #{provider} (#{status}): #{count} configurations"
  end
  
rescue => e
  puts "✗ Error: #{e.message}"
  puts e.backtrace.first(5)
end
